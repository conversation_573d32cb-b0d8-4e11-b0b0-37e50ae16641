import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  matSelectAnimations
} from "./chunk-USPCX32I.js";
import "./chunk-MWYTRG2M.js";
import "./chunk-RWLRIBJV.js";
import "./chunk-2OYWT7FF.js";
import "./chunk-PLSOC2RY.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Form<PERSON>ield,
  <PERSON><PERSON><PERSON>,
  MatLabel,
  MatPrefix,
  MatSuffix
} from "./chunk-ZH4EVIWQ.js";
import "./chunk-L64COGOJ.js";
import "./chunk-QGV6B73W.js";
import "./chunk-B7GQUK3J.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-T3GMY7GT.js";
import "./chunk-UBZYO7FG.js";
import "./chunk-ZI2Q76R4.js";
import "./chunk-4N4GOYJH.js";
import "./chunk-5OPE3T2R.js";
import "./chunk-FHTVLBLO.js";
import "./chunk-3OV72XIM.js";
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map

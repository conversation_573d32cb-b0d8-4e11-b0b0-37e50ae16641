{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/radio.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/a11y';\nimport * as i2 from '@angular/cdk/collections';\nimport * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, EventEmitter, booleanAttribute, Directive, Output, ContentChildren, Input, inject, NgZone, Injector, numberAttribute, afterNextRender, ANIMATION_MODULE_TYPE, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Attribute, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { MatR<PERSON>ple, _MatInternalFormField, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { CommonModule } from '@angular/common';\n\n// Increasing integer for generating unique ids for radio components.\nconst _c0 = [\"input\"];\nconst _c1 = [\"formField\"];\nconst _c2 = [\"*\"];\nlet nextUniqueId = 0;\n/** Change event object emitted by radio button and radio group. */\nclass MatRadioChange {\n  constructor( /** The radio button that emits the change event. */\n  source, /** The value of the radio button. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatRadioGroup),\n  multi: true\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = new InjectionToken('mat-radio-default-options', {\n  providedIn: 'root',\n  factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY\n});\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    disabledInteractive: false\n  };\n}\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nclass MatRadioGroup {\n  /** Name of the radio button group. All radio buttons inside this group will use this name. */\n  get name() {\n    return this._name;\n  }\n  set name(value) {\n    this._name = value;\n    this._updateRadioButtonNames();\n  }\n  /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n  get labelPosition() {\n    return this._labelPosition;\n  }\n  set labelPosition(v) {\n    this._labelPosition = v === 'before' ? 'before' : 'after';\n    this._markRadiosForCheck();\n  }\n  /**\n   * Value for the radio-group. Should equal the value of the selected radio button if there is\n   * a corresponding radio button with a matching value. If there is not such a corresponding\n   * radio button, this value persists to be applied in case a new radio button is added with a\n   * matching value.\n   */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    if (this._value !== newValue) {\n      // Set this before proceeding to ensure no circular loop occurs with selection.\n      this._value = newValue;\n      this._updateSelectedRadioFromValue();\n      this._checkSelectedRadioButton();\n    }\n  }\n  _checkSelectedRadioButton() {\n    if (this._selected && !this._selected.checked) {\n      this._selected.checked = true;\n    }\n  }\n  /**\n   * The currently selected radio button. If set to a new radio button, the radio group value\n   * will be updated to match the new selected button.\n   */\n  get selected() {\n    return this._selected;\n  }\n  set selected(selected) {\n    this._selected = selected;\n    this.value = selected ? selected.value : null;\n    this._checkSelectedRadioButton();\n  }\n  /** Whether the radio group is disabled */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._markRadiosForCheck();\n  }\n  /** Whether the radio group is required */\n  get required() {\n    return this._required;\n  }\n  set required(value) {\n    this._required = value;\n    this._markRadiosForCheck();\n  }\n  /** Whether buttons in the group should be interactive while they're disabled. */\n  get disabledInteractive() {\n    return this._disabledInteractive;\n  }\n  set disabledInteractive(value) {\n    this._disabledInteractive = value;\n    this._markRadiosForCheck();\n  }\n  constructor(_changeDetector) {\n    this._changeDetector = _changeDetector;\n    /** Selected value for the radio group. */\n    this._value = null;\n    /** The HTML name attribute applied to radio buttons in this group. */\n    this._name = `mat-radio-group-${nextUniqueId++}`;\n    /** The currently selected radio button. Should match value. */\n    this._selected = null;\n    /** Whether the `value` has been set to its initial value. */\n    this._isInitialized = false;\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    this._labelPosition = 'after';\n    /** Whether the radio group is disabled. */\n    this._disabled = false;\n    /** Whether the radio group is required. */\n    this._required = false;\n    /** The method to be called in order to update ngModel */\n    this._controlValueAccessorChangeFn = () => {};\n    /**\n     * onTouch function registered via registerOnTouch (ControlValueAccessor).\n     * @docs-private\n     */\n    this.onTouched = () => {};\n    /**\n     * Event emitted when the group value changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * a radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    this.change = new EventEmitter();\n    this._disabledInteractive = false;\n  }\n  /**\n   * Initialize properties once content children are available.\n   * This allows us to propagate relevant attributes to associated buttons.\n   */\n  ngAfterContentInit() {\n    // Mark this component as initialized in AfterContentInit because the initial value can\n    // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n    // NgModel occurs *after* the OnInit of the MatRadioGroup.\n    this._isInitialized = true;\n    // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n    // buttons depends on it. Note that we don't clear the `value`, because the radio button\n    // may be swapped out with a similar one and there are some internal apps that depend on\n    // that behavior.\n    this._buttonChanges = this._radios.changes.subscribe(() => {\n      if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n        this._selected = null;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._buttonChanges?.unsubscribe();\n  }\n  /**\n   * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n   * radio buttons upon their blur.\n   */\n  _touch() {\n    if (this.onTouched) {\n      this.onTouched();\n    }\n  }\n  _updateRadioButtonNames() {\n    if (this._radios) {\n      this._radios.forEach(radio => {\n        radio.name = this.name;\n        radio._markForCheck();\n      });\n    }\n  }\n  /** Updates the `selected` radio button from the internal _value state. */\n  _updateSelectedRadioFromValue() {\n    // If the value already matches the selected radio, do nothing.\n    const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n    if (this._radios && !isAlreadySelected) {\n      this._selected = null;\n      this._radios.forEach(radio => {\n        radio.checked = this.value === radio.value;\n        if (radio.checked) {\n          this._selected = radio;\n        }\n      });\n    }\n  }\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent() {\n    if (this._isInitialized) {\n      this.change.emit(new MatRadioChange(this._selected, this._value));\n    }\n  }\n  _markRadiosForCheck() {\n    if (this._radios) {\n      this._radios.forEach(radio => radio._markForCheck());\n    }\n  }\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value\n   */\n  writeValue(value) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n  /**\n   * Registers a callback to be triggered when the model value changes.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  /**\n   * Registers a callback to be triggered when the control is touched.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  /**\n   * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n   * @param isDisabled Whether the control should be disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetector.markForCheck();\n  }\n  static {\n    this.ɵfac = function MatRadioGroup_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRadioGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRadioGroup,\n      selectors: [[\"mat-radio-group\"]],\n      contentQueries: function MatRadioGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatRadioButton, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._radios = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"radiogroup\", 1, \"mat-mdc-radio-group\"],\n      inputs: {\n        color: \"color\",\n        name: \"name\",\n        labelPosition: \"labelPosition\",\n        value: \"value\",\n        selected: \"selected\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        required: [2, \"required\", \"required\", booleanAttribute],\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioGroup, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-radio-group',\n      exportAs: 'matRadioGroup',\n      providers: [MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }],\n      host: {\n        'role': 'radiogroup',\n        'class': 'mat-mdc-radio-group'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    change: [{\n      type: Output\n    }],\n    _radios: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatRadioButton), {\n        descendants: true\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatRadioButton {\n  /** Whether this radio button is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    if (this._checked !== value) {\n      this._checked = value;\n      if (value && this.radioGroup && this.radioGroup.value !== this.value) {\n        this.radioGroup.selected = this;\n      } else if (!value && this.radioGroup && this.radioGroup.value === this.value) {\n        // When unchecking the selected radio button, update the selected radio\n        // property on the group.\n        this.radioGroup.selected = null;\n      }\n      if (value) {\n        // Notify all radio buttons with the same name to un-check.\n        this._radioDispatcher.notify(this.id, this.name);\n      }\n      this._changeDetector.markForCheck();\n    }\n  }\n  /** The value of this radio button. */\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    if (this._value !== value) {\n      this._value = value;\n      if (this.radioGroup !== null) {\n        if (!this.checked) {\n          // Update checked when the value changed to match the radio group's value\n          this.checked = this.radioGroup.value === value;\n        }\n        if (this.checked) {\n          this.radioGroup.selected = this;\n        }\n      }\n    }\n  }\n  /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n  get labelPosition() {\n    return this._labelPosition || this.radioGroup && this.radioGroup.labelPosition || 'after';\n  }\n  set labelPosition(value) {\n    this._labelPosition = value;\n  }\n  /** Whether the radio button is disabled. */\n  get disabled() {\n    return this._disabled || this.radioGroup !== null && this.radioGroup.disabled;\n  }\n  set disabled(value) {\n    this._setDisabled(value);\n  }\n  /** Whether the radio button is required. */\n  get required() {\n    return this._required || this.radioGroup && this.radioGroup.required;\n  }\n  set required(value) {\n    this._required = value;\n  }\n  /**\n   * Theme color of the radio button. This API is supported in M2 themes only, it\n   * has no effect in M3 themes.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.io/guide/theming#using-component-color-variants.\n   */\n  get color() {\n    // As per M2 design specifications the selection control radio should use the accent color\n    // palette by default. https://m2.material.io/components/radio-buttons#specs\n    return this._color || this.radioGroup && this.radioGroup.color || this._defaultOptions && this._defaultOptions.color || 'accent';\n  }\n  set color(newValue) {\n    this._color = newValue;\n  }\n  /** Whether the radio button should remain interactive when it is disabled. */\n  get disabledInteractive() {\n    return this._disabledInteractive || this.radioGroup !== null && this.radioGroup.disabledInteractive;\n  }\n  set disabledInteractive(value) {\n    this._disabledInteractive = value;\n  }\n  /** ID of the native input element inside `<mat-radio-button>` */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  constructor(radioGroup, _elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _defaultOptions, tabIndex) {\n    this._elementRef = _elementRef;\n    this._changeDetector = _changeDetector;\n    this._focusMonitor = _focusMonitor;\n    this._radioDispatcher = _radioDispatcher;\n    this._defaultOptions = _defaultOptions;\n    this._ngZone = inject(NgZone);\n    this._uniqueId = `mat-radio-${++nextUniqueId}`;\n    /** The unique ID for the radio button. */\n    this.id = this._uniqueId;\n    /** Whether ripples are disabled inside the radio button */\n    this.disableRipple = false;\n    /** Tabindex of the radio button. */\n    this.tabIndex = 0;\n    /**\n     * Event emitted when the checked state of this radio button changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * the radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    this.change = new EventEmitter();\n    /** Whether this radio is checked. */\n    this._checked = false;\n    /** Value assigned to this radio. */\n    this._value = null;\n    /** Unregister function for _radioDispatcher */\n    this._removeUniqueSelectionListener = () => {};\n    this._injector = inject(Injector);\n    /** Called when the input is clicked. */\n    this._onInputClick = event => {\n      // If the input is disabled while interactive, we need to prevent the\n      // selection from happening in this event handler. Note that even though\n      // this happens on `click` events, the logic applies when the user is\n      // navigating with the keyboard as well. An alternative way of doing\n      // this is by resetting the `checked` state in the `change` callback but\n      // it isn't optimal, because it can allow a pre-checked disabled button\n      // to be un-checked. This approach seems to cover everything.\n      if (this.disabled && this.disabledInteractive) {\n        event.preventDefault();\n      }\n    };\n    // Assertions. Ideally these should be stripped out by the compiler.\n    // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n    this.radioGroup = radioGroup;\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    this._disabledInteractive = _defaultOptions?.disabledInteractive ?? false;\n    if (tabIndex) {\n      this.tabIndex = numberAttribute(tabIndex, 0);\n    }\n  }\n  /** Focuses the radio button. */\n  focus(options, origin) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._inputElement, origin, options);\n    } else {\n      this._inputElement.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Marks the radio button as needing checking for change detection.\n   * This method is exposed because the parent radio group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n    // update radio button's status\n    this._changeDetector.markForCheck();\n  }\n  ngOnInit() {\n    if (this.radioGroup) {\n      // If the radio is inside a radio group, determine if it should be checked\n      this.checked = this.radioGroup.value === this._value;\n      if (this.checked) {\n        this.radioGroup.selected = this;\n      }\n      // Copy name from parent radio group\n      this.name = this.radioGroup.name;\n    }\n    this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n      if (id !== this.id && name === this.name) {\n        this.checked = false;\n      }\n    });\n  }\n  ngDoCheck() {\n    this._updateTabIndex();\n  }\n  ngAfterViewInit() {\n    this._updateTabIndex();\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (!focusOrigin && this.radioGroup) {\n        this.radioGroup._touch();\n      }\n    });\n    // We bind this outside of the zone, because:\n    // 1. Its logic is completely DOM-related so we can avoid some change detections.\n    // 2. There appear to be some internal tests that break when this triggers a change detection.\n    this._ngZone.runOutsideAngular(() => {\n      this._inputElement.nativeElement.addEventListener('click', this._onInputClick);\n    });\n  }\n  ngOnDestroy() {\n    // We need to null check in case the button was destroyed before `ngAfterViewInit`.\n    this._inputElement?.nativeElement.removeEventListener('click', this._onInputClick);\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._removeUniqueSelectionListener();\n  }\n  /** Dispatch change event with current value. */\n  _emitChangeEvent() {\n    this.change.emit(new MatRadioChange(this, this._value));\n  }\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  /** Triggered when the radio button receives an interaction from the user. */\n  _onInputInteraction(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n    if (!this.checked && !this.disabled) {\n      const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n      this.checked = true;\n      this._emitChangeEvent();\n      if (this.radioGroup) {\n        this.radioGroup._controlValueAccessorChangeFn(this.value);\n        if (groupValueChanged) {\n          this.radioGroup._emitChangeEvent();\n        }\n      }\n    }\n  }\n  /** Triggered when the user clicks on the touch target. */\n  _onTouchTargetClick(event) {\n    this._onInputInteraction(event);\n    if (!this.disabled || this.disabledInteractive) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement?.nativeElement.focus();\n    }\n  }\n  /** Sets the disabled state and marks for check if a change occurred. */\n  _setDisabled(value) {\n    if (this._disabled !== value) {\n      this._disabled = value;\n      this._changeDetector.markForCheck();\n    }\n  }\n  /** Gets the tabindex for the underlying input element. */\n  _updateTabIndex() {\n    const group = this.radioGroup;\n    let value;\n    // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n    // necessary, because the browser handles the tab order for inputs inside a group automatically,\n    // but we need an explicitly higher tabindex for the selected button in order for things like\n    // the focus trap to pick it up correctly.\n    if (!group || !group.selected || this.disabled) {\n      value = this.tabIndex;\n    } else {\n      value = group.selected === this ? this.tabIndex : -1;\n    }\n    if (value !== this._previousTabIndex) {\n      // We have to set the tabindex directly on the DOM node, because it depends on\n      // the selected state which is prone to \"changed after checked errors\".\n      const input = this._inputElement?.nativeElement;\n      if (input) {\n        input.setAttribute('tabindex', value + '');\n        this._previousTabIndex = value;\n        // Wait for any pending tabindex changes to be applied\n        afterNextRender(() => {\n          queueMicrotask(() => {\n            // The radio group uses a \"selection follows focus\" pattern for tab management, so if this\n            // radio button is currently focused and another radio button in the group becomes\n            // selected, we should move focus to the newly selected radio button to maintain\n            // consistency between the focused and selected states.\n            if (group && group.selected && group.selected !== this && document.activeElement === input) {\n              group.selected?._inputElement.nativeElement.focus();\n              // If this radio button still has focus, the selected one must be disabled. In this\n              // case the radio group as a whole should lose focus.\n              if (document.activeElement === input) {\n                this._inputElement.nativeElement.blur();\n              }\n            }\n          });\n        }, {\n          injector: this._injector\n        });\n      }\n    }\n  }\n  static {\n    this.ɵfac = function MatRadioButton_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRadioButton)(i0.ɵɵdirectiveInject(MAT_RADIO_GROUP, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i2.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_RADIO_DEFAULT_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatRadioButton,\n      selectors: [[\"mat-radio-button\"]],\n      viewQuery: function MatRadioButton_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._rippleTrigger = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-radio-button\"],\n      hostVars: 19,\n      hostBindings: function MatRadioButton_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatRadioButton_focus_HostBindingHandler() {\n            return ctx._inputElement.nativeElement.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n          i0.ɵɵclassProp(\"mat-primary\", ctx.color === \"primary\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"mat-mdc-radio-checked\", ctx.checked)(\"mat-mdc-radio-disabled\", ctx.disabled)(\"mat-mdc-radio-disabled-interactive\", ctx.disabledInteractive)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        id: \"id\",\n        name: \"name\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        checked: [2, \"checked\", \"checked\", booleanAttribute],\n        value: \"value\",\n        labelPosition: \"labelPosition\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        required: [2, \"required\", \"required\", booleanAttribute],\n        color: \"color\",\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioButton\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 13,\n      vars: 17,\n      consts: [[\"formField\", \"\"], [\"input\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"labelPosition\"], [1, \"mdc-radio\"], [1, \"mat-mdc-radio-touch-target\", 3, \"click\"], [\"type\", \"radio\", 1, \"mdc-radio__native-control\", 3, \"change\", \"id\", \"checked\", \"disabled\", \"required\"], [1, \"mdc-radio__background\"], [1, \"mdc-radio__outer-circle\"], [1, \"mdc-radio__inner-circle\"], [\"mat-ripple\", \"\", 1, \"mat-radio-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mat-ripple-element\", \"mat-radio-persistent-ripple\"], [1, \"mdc-label\", 3, \"for\"]],\n      template: function MatRadioButton_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"div\", 3)(3, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function MatRadioButton_Template_div_click_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onTouchTargetClick($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"input\", 5, 1);\n          i0.ɵɵlistener(\"change\", function MatRadioButton_Template_input_change_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onInputInteraction($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵelement(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9);\n          i0.ɵɵelement(10, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"label\", 11);\n          i0.ɵɵprojection(12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"mdc-radio--disabled\", ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.inputId)(\"checked\", ctx.checked)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"required\", ctx.required);\n          i0.ɵɵattribute(\"name\", ctx.name)(\"value\", ctx.value)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx._rippleTrigger.nativeElement)(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"for\", ctx.inputId);\n        }\n      },\n      dependencies: [MatRipple, _MatInternalFormField],\n      styles: [\".mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mdc-radio-state-layer-size) - 20px)/2)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled])~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color, var(--mat-app-on-surface))}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio:active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color, var(--mat-app-on-surface))}.mat-mdc-radio-button .mdc-radio:active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-radio-button .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size);top:calc(-1*(var(--mdc-radio-state-layer-size) - 20px)/2);left:calc(-1*(var(--mdc-radio-state-layer-size) - 20px)/2)}.mat-mdc-radio-button .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color, var(--mat-app-on-surface-variant))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled{pointer-events:auto}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-app-on-surface))}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color, var(--mat-app-primary))}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-app-on-surface))}.mat-mdc-radio-button .mat-internal-form-field{color:var(--mat-radio-label-text-color, var(--mat-app-on-surface));font-family:var(--mat-radio-label-text-font, var(--mat-app-body-medium-font));line-height:var(--mat-radio-label-text-line-height, var(--mat-app-body-medium-line-height));font-size:var(--mat-radio-label-text-size, var(--mat-app-body-medium-size));letter-spacing:var(--mat-radio-label-text-tracking, var(--mat-app-body-medium-tracking));font-weight:var(--mat-radio-label-text-weight, var(--mat-app-body-medium-weight))}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, var(--mat-app-on-surface))}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-disabled{cursor:default;pointer-events:none}.mat-mdc-radio-disabled.mat-mdc-radio-disabled-interactive{pointer-events:auto}.mat-mdc-radio-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display)}[dir=rtl] .mat-mdc-radio-touch-target{left:auto;right:50%;transform:translate(50%, -50%)}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioButton, [{\n    type: Component,\n    args: [{\n      selector: 'mat-radio-button',\n      host: {\n        'class': 'mat-mdc-radio-button',\n        '[attr.id]': 'id',\n        '[class.mat-primary]': 'color === \"primary\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class.mat-mdc-radio-checked]': 'checked',\n        '[class.mat-mdc-radio-disabled]': 'disabled',\n        '[class.mat-mdc-radio-disabled-interactive]': 'disabledInteractive',\n        '[class._mat-animation-noopable]': '_noopAnimations',\n        // Needs to be removed since it causes some a11y issues (see #21266).\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null',\n        // Note: under normal conditions focus shouldn't land on this element, however it may be\n        // programmatically set, for example inside of a focus trap, in this case we want to forward\n        // the focus to the native element.\n        '(focus)': '_inputElement.nativeElement.focus()'\n      },\n      exportAs: 'matRadioButton',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [MatRipple, _MatInternalFormField],\n      template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" #formField>\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? 'true' : null\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-mdc-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"_rippleTrigger.nativeElement\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mdc-radio-state-layer-size) - 20px)/2)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled])~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color, var(--mat-app-on-surface))}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio:active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color, var(--mat-app-on-surface))}.mat-mdc-radio-button .mdc-radio:active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-radio-button .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size);top:calc(-1*(var(--mdc-radio-state-layer-size) - 20px)/2);left:calc(-1*(var(--mdc-radio-state-layer-size) - 20px)/2)}.mat-mdc-radio-button .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color, var(--mat-app-on-surface-variant))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled{pointer-events:auto}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-app-on-surface))}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color, var(--mat-app-primary))}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-app-on-surface))}.mat-mdc-radio-button .mat-internal-form-field{color:var(--mat-radio-label-text-color, var(--mat-app-on-surface));font-family:var(--mat-radio-label-text-font, var(--mat-app-body-medium-font));line-height:var(--mat-radio-label-text-line-height, var(--mat-app-body-medium-line-height));font-size:var(--mat-radio-label-text-size, var(--mat-app-body-medium-size));letter-spacing:var(--mat-radio-label-text-tracking, var(--mat-app-body-medium-tracking));font-weight:var(--mat-radio-label-text-weight, var(--mat-app-body-medium-weight))}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, var(--mat-app-on-surface))}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-disabled{cursor:default;pointer-events:none}.mat-mdc-radio-disabled.mat-mdc-radio-disabled-interactive{pointer-events:auto}.mat-mdc-radio-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display)}[dir=rtl] .mat-mdc-radio-touch-target{left:auto;right:50%;transform:translate(50%, -50%)}\"]\n    }]\n  }], () => [{\n    type: MatRadioGroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RADIO_GROUP]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: i2.UniqueSelectionDispatcher\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RADIO_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }], {\n    id: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    change: [{\n      type: Output\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    _rippleTrigger: [{\n      type: ViewChild,\n      args: ['formField', {\n        read: ElementRef,\n        static: true\n      }]\n    }]\n  });\n})();\nclass MatRadioModule {\n  static {\n    this.ɵfac = function MatRadioModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRadioModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRadioModule,\n      imports: [MatCommonModule, CommonModule, MatRippleModule, MatRadioGroup, MatRadioButton],\n      exports: [MatCommonModule, MatRadioGroup, MatRadioButton]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CommonModule, MatRippleModule, MatRadioButton, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule, MatRippleModule, MatRadioGroup, MatRadioButton],\n      exports: [MatCommonModule, MatRadioGroup, MatRadioButton]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAI,eAAe;AAEnB,IAAM,iBAAN,MAAqB;AAAA,EACnB,YACA,QACA,OAAO;AACL,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AACF;AAMA,IAAM,yCAAyC;AAAA,EAC7C,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,aAAa;AAAA,EAC3C,OAAO;AACT;AAMA,IAAM,kBAAkB,IAAI,eAAe,eAAe;AAC1D,IAAM,4BAA4B,IAAI,eAAe,6BAA6B;AAAA,EAChF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AACD,SAAS,oCAAoC;AAC3C,SAAO;AAAA,IACL,OAAO;AAAA,IACP,qBAAqB;AAAA,EACvB;AACF;AAIA,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA,EAElB,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ;AACb,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,GAAG;AACnB,SAAK,iBAAiB,MAAM,WAAW,WAAW;AAClD,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,QAAI,KAAK,WAAW,UAAU;AAE5B,WAAK,SAAS;AACd,WAAK,8BAA8B;AACnC,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,SAAS;AAC7C,WAAK,UAAU,UAAU;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,UAAU;AACrB,SAAK,YAAY;AACjB,SAAK,QAAQ,WAAW,SAAS,QAAQ;AACzC,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,sBAAsB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,oBAAoB,OAAO;AAC7B,SAAK,uBAAuB;AAC5B,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,YAAY,iBAAiB;AAC3B,SAAK,kBAAkB;AAEvB,SAAK,SAAS;AAEd,SAAK,QAAQ,mBAAmB,cAAc;AAE9C,SAAK,YAAY;AAEjB,SAAK,iBAAiB;AAEtB,SAAK,iBAAiB;AAEtB,SAAK,YAAY;AAEjB,SAAK,YAAY;AAEjB,SAAK,gCAAgC,MAAM;AAAA,IAAC;AAK5C,SAAK,YAAY,MAAM;AAAA,IAAC;AAMxB,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AAInB,SAAK,iBAAiB;AAKtB,SAAK,iBAAiB,KAAK,QAAQ,QAAQ,UAAU,MAAM;AACzD,UAAI,KAAK,YAAY,CAAC,KAAK,QAAQ,KAAK,WAAS,UAAU,KAAK,QAAQ,GAAG;AACzE,aAAK,YAAY;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,YAAY;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ,WAAS;AAC5B,cAAM,OAAO,KAAK;AAClB,cAAM,cAAc;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,gCAAgC;AAE9B,UAAM,oBAAoB,KAAK,cAAc,QAAQ,KAAK,UAAU,UAAU,KAAK;AACnF,QAAI,KAAK,WAAW,CAAC,mBAAmB;AACtC,WAAK,YAAY;AACjB,WAAK,QAAQ,QAAQ,WAAS;AAC5B,cAAM,UAAU,KAAK,UAAU,MAAM;AACrC,YAAI,MAAM,SAAS;AACjB,eAAK,YAAY;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,KAAK,gBAAgB;AACvB,WAAK,OAAO,KAAK,IAAI,eAAe,KAAK,WAAW,KAAK,MAAM,CAAC;AAAA,IAClE;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ,WAAS,MAAM,cAAc,CAAC;AAAA,IACrD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,SAAK,QAAQ;AACb,SAAK,gBAAgB,aAAa;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,IAAI;AACnB,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAChB,SAAK,gBAAgB,aAAa;AAAA,EACpC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,kBAAqB,iBAAiB,CAAC;AAAA,IAC5F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,gBAAgB,SAAS,6BAA6B,IAAI,KAAK,UAAU;AACvE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,gBAAgB,CAAC;AAAA,QAC/C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAAA,QAC7D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,QAAQ,cAAc,GAAG,qBAAqB;AAAA,MAC1D,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,eAAe;AAAA,QACf,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,QACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,QACtD,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,MACzF;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,wCAAwC;AAAA,QACxE,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,wCAAwC;AAAA,QAClD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,cAAc,GAAG;AAAA,QACvC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA,EAEnB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,KAAK,aAAa,OAAO;AAC3B,WAAK,WAAW;AAChB,UAAI,SAAS,KAAK,cAAc,KAAK,WAAW,UAAU,KAAK,OAAO;AACpE,aAAK,WAAW,WAAW;AAAA,MAC7B,WAAW,CAAC,SAAS,KAAK,cAAc,KAAK,WAAW,UAAU,KAAK,OAAO;AAG5E,aAAK,WAAW,WAAW;AAAA,MAC7B;AACA,UAAI,OAAO;AAET,aAAK,iBAAiB,OAAO,KAAK,IAAI,KAAK,IAAI;AAAA,MACjD;AACA,WAAK,gBAAgB,aAAa;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,QAAI,KAAK,WAAW,OAAO;AACzB,WAAK,SAAS;AACd,UAAI,KAAK,eAAe,MAAM;AAC5B,YAAI,CAAC,KAAK,SAAS;AAEjB,eAAK,UAAU,KAAK,WAAW,UAAU;AAAA,QAC3C;AACA,YAAI,KAAK,SAAS;AAChB,eAAK,WAAW,WAAW;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK,kBAAkB,KAAK,cAAc,KAAK,WAAW,iBAAiB;AAAA,EACpF;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,eAAe,QAAQ,KAAK,WAAW;AAAA,EACvE;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,cAAc,KAAK,WAAW;AAAA,EAC9D;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,QAAQ;AAGV,WAAO,KAAK,UAAU,KAAK,cAAc,KAAK,WAAW,SAAS,KAAK,mBAAmB,KAAK,gBAAgB,SAAS;AAAA,EAC1H;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAEA,IAAI,sBAAsB;AACxB,WAAO,KAAK,wBAAwB,KAAK,eAAe,QAAQ,KAAK,WAAW;AAAA,EAClF;AAAA,EACA,IAAI,oBAAoB,OAAO;AAC7B,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,GAAG,KAAK,MAAM,KAAK,SAAS;AAAA,EACrC;AAAA,EACA,YAAY,YAAY,aAAa,iBAAiB,eAAe,kBAAkB,eAAe,iBAAiB,UAAU;AAC/H,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,UAAU,OAAO,MAAM;AAC5B,SAAK,YAAY,aAAa,EAAE,YAAY;AAE5C,SAAK,KAAK,KAAK;AAEf,SAAK,gBAAgB;AAErB,SAAK,WAAW;AAMhB,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,WAAW;AAEhB,SAAK,SAAS;AAEd,SAAK,iCAAiC,MAAM;AAAA,IAAC;AAC7C,SAAK,YAAY,OAAO,QAAQ;AAEhC,SAAK,gBAAgB,WAAS;AAQ5B,UAAI,KAAK,YAAY,KAAK,qBAAqB;AAC7C,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAGA,SAAK,aAAa;AAClB,SAAK,kBAAkB,kBAAkB;AACzC,SAAK,uBAAuB,iBAAiB,uBAAuB;AACpE,QAAI,UAAU;AACZ,WAAK,WAAW,gBAAgB,UAAU,CAAC;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA,EAEA,MAAM,SAAS,QAAQ;AACrB,QAAI,QAAQ;AACV,WAAK,cAAc,SAAS,KAAK,eAAe,QAAQ,OAAO;AAAA,IACjE,OAAO;AACL,WAAK,cAAc,cAAc,MAAM,OAAO;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AAGd,SAAK,gBAAgB,aAAa;AAAA,EACpC;AAAA,EACA,WAAW;AACT,QAAI,KAAK,YAAY;AAEnB,WAAK,UAAU,KAAK,WAAW,UAAU,KAAK;AAC9C,UAAI,KAAK,SAAS;AAChB,aAAK,WAAW,WAAW;AAAA,MAC7B;AAEA,WAAK,OAAO,KAAK,WAAW;AAAA,IAC9B;AACA,SAAK,iCAAiC,KAAK,iBAAiB,OAAO,CAAC,IAAI,SAAS;AAC/E,UAAI,OAAO,KAAK,MAAM,SAAS,KAAK,MAAM;AACxC,aAAK,UAAU;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,SAAK,gBAAgB;AACrB,SAAK,cAAc,QAAQ,KAAK,aAAa,IAAI,EAAE,UAAU,iBAAe;AAC1E,UAAI,CAAC,eAAe,KAAK,YAAY;AACnC,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,IACF,CAAC;AAID,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,cAAc,cAAc,iBAAiB,SAAS,KAAK,aAAa;AAAA,IAC/E,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AAEZ,SAAK,eAAe,cAAc,oBAAoB,SAAS,KAAK,aAAa;AACjF,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,SAAK,+BAA+B;AAAA,EACtC;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,OAAO,KAAK,IAAI,eAAe,MAAM,KAAK,MAAM,CAAC;AAAA,EACxD;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,iBAAiB,KAAK;AAAA,EACpC;AAAA;AAAA,EAEA,oBAAoB,OAAO;AAIzB,UAAM,gBAAgB;AACtB,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK,UAAU;AACnC,YAAM,oBAAoB,KAAK,cAAc,KAAK,UAAU,KAAK,WAAW;AAC5E,WAAK,UAAU;AACf,WAAK,iBAAiB;AACtB,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,8BAA8B,KAAK,KAAK;AACxD,YAAI,mBAAmB;AACrB,eAAK,WAAW,iBAAiB;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,OAAO;AACzB,SAAK,oBAAoB,KAAK;AAC9B,QAAI,CAAC,KAAK,YAAY,KAAK,qBAAqB;AAG9C,WAAK,eAAe,cAAc,MAAM;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,YAAY;AACjB,WAAK,gBAAgB,aAAa;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,QAAQ,KAAK;AACnB,QAAI;AAKJ,QAAI,CAAC,SAAS,CAAC,MAAM,YAAY,KAAK,UAAU;AAC9C,cAAQ,KAAK;AAAA,IACf,OAAO;AACL,cAAQ,MAAM,aAAa,OAAO,KAAK,WAAW;AAAA,IACpD;AACA,QAAI,UAAU,KAAK,mBAAmB;AAGpC,YAAM,QAAQ,KAAK,eAAe;AAClC,UAAI,OAAO;AACT,cAAM,aAAa,YAAY,QAAQ,EAAE;AACzC,aAAK,oBAAoB;AAEzB,wBAAgB,MAAM;AACpB,yBAAe,MAAM;AAKnB,gBAAI,SAAS,MAAM,YAAY,MAAM,aAAa,QAAQ,SAAS,kBAAkB,OAAO;AAC1F,oBAAM,UAAU,cAAc,cAAc,MAAM;AAGlD,kBAAI,SAAS,kBAAkB,OAAO;AACpC,qBAAK,cAAc,cAAc,KAAK;AAAA,cACxC;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH,GAAG;AAAA,UACD,UAAU,KAAK;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,kBAAkB,iBAAiB,CAAC,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,yBAAyB,GAAM,kBAAkB,uBAAuB,CAAC,GAAM,kBAAkB,2BAA2B,CAAC,GAAM,kBAAkB,UAAU,CAAC;AAAA,IAC7Y;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,WAAW,SAAS,qBAAqB,IAAI,KAAK;AAChD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,KAAK,GAAG,UAAU;AAAA,QACnC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,QACvE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,sBAAsB;AAAA,MACrC,UAAU;AAAA,MACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,0CAA0C;AACxE,mBAAO,IAAI,cAAc,cAAc,MAAM;AAAA,UAC/C,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,MAAM,IAAI,EAAE,EAAE,YAAY,IAAI,EAAE,cAAc,IAAI,EAAE,mBAAmB,IAAI,EAAE,oBAAoB,IAAI;AACpH,UAAG,YAAY,eAAe,IAAI,UAAU,SAAS,EAAE,cAAc,IAAI,UAAU,QAAQ,EAAE,YAAY,IAAI,UAAU,MAAM,EAAE,yBAAyB,IAAI,OAAO,EAAE,0BAA0B,IAAI,QAAQ,EAAE,sCAAsC,IAAI,mBAAmB,EAAE,2BAA2B,IAAI,eAAe;AAAA,QAC5T;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,QACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,QACvD,iBAAiB,CAAC,GAAG,oBAAoB,iBAAiB;AAAA,QAC1D,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,QACrE,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,QACzF,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,QACnD,OAAO;AAAA,QACP,eAAe;AAAA,QACf,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,QACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,QACtD,OAAO;AAAA,QACP,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,MACzF;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,mBAAmB;AAAA,MAC9D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,2BAA2B,IAAI,GAAG,eAAe,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,8BAA8B,GAAG,OAAO,GAAG,CAAC,QAAQ,SAAS,GAAG,6BAA6B,GAAG,UAAU,MAAM,WAAW,YAAY,UAAU,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,cAAc,IAAI,GAAG,oBAAoB,2BAA2B,GAAG,oBAAoB,qBAAqB,mBAAmB,GAAG,CAAC,GAAG,sBAAsB,6BAA6B,GAAG,CAAC,GAAG,aAAa,GAAG,KAAK,CAAC;AAAA,MACnkB,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1D,UAAG,WAAW,SAAS,SAAS,6CAA6C,QAAQ;AACnF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,oBAAoB,MAAM,CAAC;AAAA,UACvD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,UAAG,WAAW,UAAU,SAAS,gDAAgD,QAAQ;AACvF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,oBAAoB,MAAM,CAAC;AAAA,UACvD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACrC,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,SAAS,EAAE;AACjC,UAAG,aAAa,EAAE;AAClB,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,iBAAiB,IAAI,aAAa;AAChD,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,uBAAuB,IAAI,QAAQ;AAClD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,MAAM,IAAI,OAAO,EAAE,WAAW,IAAI,OAAO,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,mBAAmB,EAAE,YAAY,IAAI,QAAQ;AACvI,UAAG,YAAY,QAAQ,IAAI,IAAI,EAAE,SAAS,IAAI,KAAK,EAAE,cAAc,IAAI,SAAS,EAAE,mBAAmB,IAAI,cAAc,EAAE,oBAAoB,IAAI,eAAe,EAAE,iBAAiB,IAAI,YAAY,IAAI,sBAAsB,SAAS,IAAI;AAC1O,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,IAAI,eAAe,aAAa,EAAE,qBAAqB,IAAI,kBAAkB,CAAC,EAAE,qBAAqB,IAAI;AAC3I,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,OAAO,IAAI,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,WAAW,qBAAqB;AAAA,MAC/C,QAAQ,CAAC,q/SAAy/S;AAAA,MAClgT,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,uBAAuB;AAAA,QACvB,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,iCAAiC;AAAA,QACjC,kCAAkC;AAAA,QAClC,8CAA8C;AAAA,QAC9C,mCAAmC;AAAA;AAAA,QAEnC,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA;AAAA;AAAA;AAAA,QAI3B,WAAW;AAAA,MACb;AAAA,MACA,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,SAAS,CAAC,WAAW,qBAAqB;AAAA,MAC1C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,q/SAAy/S;AAAA,IACpgT,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,cAAc,iBAAiB,eAAe,cAAc;AAAA,MACvF,SAAS,CAAC,iBAAiB,eAAe,cAAc;AAAA,IAC1D,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB,cAAc,iBAAiB,gBAAgB,eAAe;AAAA,IAC3F,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,cAAc,iBAAiB,eAAe,cAAc;AAAA,MACvF,SAAS,CAAC,iBAAiB,eAAe,cAAc;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
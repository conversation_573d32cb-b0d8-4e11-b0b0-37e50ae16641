// src/app/features/marketplace/components/price-history.component.ts
import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MarketPriceService } from '../core/auth/services/market-price.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-price-history',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatProgressSpinnerModule,
  ],
  template: `
    <h2 mat-dialog-title>Price History for {{ cropName }}</h2>
    <mat-dialog-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <div *ngIf="!isLoading && priceHistory.length === 0" class="no-data">
        No price history available for this product.
      </div>

      <div
        *ngIf="!isLoading && priceHistory.length > 0"
        class="history-content"
      >
        <table class="history-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Price (₹)</th>
              <th>Change</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let price of priceHistory; let i = index">
              <td>{{ price.price_date | date : 'MMM d, yyyy' }}</td>
              <td>
                ₹{{ price.price_per_unit | number : '1.2-2' }}/{{
                  price.unit_type
                }}
              </td>
              <td class="change-cell">
                <span
                  *ngIf="i < priceHistory.length - 1"
                  [ngClass]="
                    getChangeClass(
                      price.price_per_unit,
                      priceHistory[i + 1].price_per_unit
                    )
                  "
                >
                  <span
                    *ngIf="
                      getPercentChange(
                        price.price_per_unit,
                        priceHistory[i + 1].price_per_unit
                      ) !== 0
                    "
                  >
                    {{
                      getPercentChange(
                        price.price_per_unit,
                        priceHistory[i + 1].price_per_unit
                      ) > 0
                        ? '+'
                        : ''
                    }}
                    {{
                      getPercentChange(
                        price.price_per_unit,
                        priceHistory[i + 1].price_per_unit
                      ) | number : '1.2-2'
                    }}%
                  </span>
                  <span
                    *ngIf="
                      getPercentChange(
                        price.price_per_unit,
                        priceHistory[i + 1].price_per_unit
                      ) === 0
                    "
                  >
                    No change
                  </span>
                </span>
                <span *ngIf="i === priceHistory.length - 1">-</span>
              </td>
              <td>{{ price.notes || '-' }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="close()">Close</button>
    </mat-dialog-actions>
  `,
  styles: [
    `
      .loading-container {
        display: flex;
        justify-content: center;
        padding: 20px;
      }

      .no-data {
        text-align: center;
        padding: 20px;
        color: rgba(0, 0, 0, 0.54);
      }

      .history-content {
        max-height: 400px;
        overflow-y: auto;
      }

      .history-table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
          padding: 12px 8px;
          text-align: left;
          border-bottom: 1px solid #e0e0e0;
        }

        th {
          font-weight: 500;
          color: rgba(0, 0, 0, 0.87);
          background-color: #f5f5f5;
        }

        .change-cell {
          .increase {
            color: #4caf50;
          }

          .decrease {
            color: #f44336;
          }

          .no-change {
            color: #757575;
          }
        }
      }
    `,
  ],
})
export class PriceHistoryComponent implements OnInit {
  priceHistory: any[] = [];
  isLoading = true;
  cropName = 'Product';

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { productId: number },
    private dialogRef: MatDialogRef<PriceHistoryComponent>,
    private marketPriceService: MarketPriceService
  ) {}

  ngOnInit(): void {
    this.loadPriceHistory();
  }

  loadPriceHistory(): void {
    this.isLoading = true;
    this.marketPriceService.getPriceHistory(this.data.productId).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          // Sort by date, newest first
          this.priceHistory = response.data.prices.sort(
            (a: any, b: any) =>
              new Date(b.price_date).getTime() -
              new Date(a.price_date).getTime()
          );
          this.cropName = response.data.crop_name || 'Product';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading price history:', error);
        this.isLoading = false;
      },
    });
  }

  getPercentChange(current: number, previous: number): number {
    if (!previous) return 0;
    const change = ((current - previous) / previous) * 100;
    return parseFloat(change.toFixed(2));
  }

  getChangeClass(current: number, previous: number): string {
    const change = this.getPercentChange(current, previous);
    if (change > 0) return 'increase';
    if (change < 0) return 'decrease';
    return 'no-change';
  }

  close(): void {
    this.dialogRef.close();
  }
}

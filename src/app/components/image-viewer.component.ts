// src/app/shared/components/image-viewer/image-viewer.component.ts
import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-image-viewer',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule, MatIconModule],
  template: `
    <div class="image-viewer-container">
      <div class="image-header">
        <h2>Image Preview</h2>
        <button mat-icon-button (click)="close()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <div class="image-content">
        <img [src]="data.imageUrl" alt="Full size image" />
      </div>
    </div>
  `,
  styles: [
    `
      .image-viewer-container {
        display: flex;
        flex-direction: column;
      }

      .image-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #eee;
      }

      .image-content {
        padding: 16px;
        display: flex;
        justify-content: center;

        img {
          max-width: 100%;
          max-height: 70vh;
          object-fit: contain;
        }
      }
    `,
  ],
})
export class ImageViewerComponent {
  constructor(
    public dialogRef: MatDialogRef<ImageViewerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { imageUrl: string }
  ) {}

  close(): void {
    this.dialogRef.close();
  }
}

<div class="transaction-detail-container">
    <mat-card>
      <!-- Header -->
      <div class="card-header">
        <div class="header-title">
          <button mat-icon-button (click)="goBack()" matTooltip="Go Back">
            <mat-icon>arrow_back</mat-icon>
          </button>
          <h2>Transaction #{{transaction?.id}}</h2>
        </div>
        <div class="header-actions" *ngIf="transaction">
          <button mat-raised-button color="primary" [matMenuTriggerFor]="statusMenu" 
                  *ngIf="transaction.status !== 'cancelled' && transaction.status !== 'closed'">
            <mat-icon>update</mat-icon> Update Status
          </button>
          <mat-menu #statusMenu="matMenu">
            <button mat-menu-item (click)="updateStatus('accepted')" 
                   *ngIf="transaction.status !== 'accepted'">
              <mat-icon>check_circle</mat-icon>
              <span>Accept Transaction</span>
            </button>
            <button mat-menu-item (click)="updateStatus('in_progress')" 
                   *ngIf="transaction.status !== 'in_progress'">
              <mat-icon>sync</mat-icon>
              <span>Mark as In Progress</span>
            </button>
            <button mat-menu-item (click)="updateStatus('delivered')" 
                   *ngIf="transaction.status !== 'delivered'">
              <mat-icon>local_shipping</mat-icon>
              <span>Mark as Delivered</span>
            </button>
            <button mat-menu-item (click)="updateStatus('closed')" 
                   *ngIf="transaction.status !== 'closed'">
              <mat-icon>task_alt</mat-icon>
              <span>Close Transaction</span>
            </button>
            <button mat-menu-item (click)="updateStatus('cancelled')" 
                   *ngIf="transaction.status !== 'cancelled'">
              <mat-icon>cancel</mat-icon>
              <span>Cancel Transaction</span>
            </button>
          </mat-menu>
        </div>
      </div>
  
      <!-- Loading spinner -->
      <div class="loading-container" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
  
      <!-- Error message -->
      <div class="error-container" *ngIf="error && !isLoading">
        <mat-icon color="warn">error</mat-icon>
        <p>{{error}}</p>
        <button mat-raised-button color="primary" (click)="loadTransaction()">
          <mat-icon>refresh</mat-icon> Try Again
        </button>
      </div>
  
      <!-- Transaction content -->
      <div class="card-content" *ngIf="transaction && !isLoading">
        <mat-tab-group animationDuration="0ms">
          <!-- Overview Tab -->
          <mat-tab label="Overview">
            <div class="overview-container">
              <!-- Transaction Status and Type -->
              <div class="transaction-header">
                <div class="status-section">
                  <div class="status-chip" [ngClass]="getStatusClass(transaction.status)">
                    {{transaction.status | titlecase}}
                  </div>
                  <div class="transaction-date">
                    Created on: {{formatDate(transaction.created_at)}}
                  </div>
                </div>
                <div class="transaction-type">
                  <mat-chip [color]="transaction.transaction_type === 'requirement_based' ? 'primary' : 'accent'" selected>
                    {{ transaction.transaction_type === 'requirement_based' ? 'Requirement Based' : 'Direct Purchase' }}
                  </mat-chip>
                </div>
              </div>
  
              <mat-divider></mat-divider>
  
              <!-- Farmer Section -->
              <div class="detail-section">
                <div class="section-header">
                  <h3>Farmer Information</h3>
                  <button mat-stroked-button color="primary" (click)="viewFarmerProfile(transaction.farmer_id)">
                    <mat-icon>person</mat-icon> View Farmer
                  </button>
                </div>
                <div class="detail-grid">
                  <div class="detail-item">
                    <span class="label">Name</span>
                    <span class="value">{{transaction.farmer_name}}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Farmer ID</span>
                    <span class="value">{{transaction.farmer_id}}</span>
                  </div>
                  <div class="detail-item" *ngIf="transaction.farmer_mobile">
                    <span class="label">Mobile</span>
                    <span class="value">{{transaction.farmer_mobile}}</span>
                  </div>
                </div>
              </div>
  
              <mat-divider></mat-divider>
  
              <!-- Produce Section -->
              <div class="detail-section">
                <div class="section-header">
                  <h3>Produce Details</h3>
                </div>
                <div class="produce-container">
                  <div class="produce-image" *ngIf="transaction.produce_image_url">
                    <img [src]="getFullImageUrl(transaction.produce_image_url)" 
                         (click)="viewImage(transaction.produce_image_url)"
                         (error)="handleImageError($event)"
                         alt="{{transaction.crop_name}}">
                  </div>
                  <div class="produce-details">
                    <div class="detail-grid">
                      <div class="detail-item full-width">
                        <span class="label">Crop</span>
                        <span class="value">{{transaction.crop_name}}</span>
                      </div>
                      <div class="detail-item" *ngIf="transaction.variety">
                        <span class="label">Variety</span>
                        <span class="value">{{transaction.variety}}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">Quantity</span>
                        <span class="value">{{transaction.quantity}} {{transaction.unit_type}}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">Price per Unit</span>
                        <span class="value">{{formatCurrency(transaction.price_per_unit)}}/{{transaction.unit_type}}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">Total Amount</span>
                        <span class="value total-amount">{{formatCurrency(transaction.total_amount)}}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
  
              <mat-divider *ngIf="transaction.fpo_requirement_id"></mat-divider>
  
              <!-- Requirement Section (if applicable) -->
              <div class="detail-section" *ngIf="transaction.fpo_requirement_id">
                <div class="section-header">
                  <h3>Requirement Information</h3>
                  <button mat-stroked-button color="primary" (click)="viewRequirement(transaction.fpo_requirement_id)">
                    <mat-icon>assignment</mat-icon> View Requirement
                  </button>
                </div>
                <div class="detail-grid">
                  <div class="detail-item">
                    <span class="label">Requirement ID</span>
                    <span class="value">#{{transaction.fpo_requirement_id}}</span>
                  </div>
                  <div class="detail-item" *ngIf="transaction.requirement_status">
                    <span class="label">Requirement Status</span>
                    <span class="value">{{transaction.requirement_status}}</span>
                  </div>
                  <div class="detail-item" *ngIf="transaction.requirement_quantity">
                    <span class="label">Required Quantity</span>
                    <span class="value">{{transaction.requirement_quantity}} {{transaction.unit_type}}</span>
                  </div>
                  <div class="detail-item" *ngIf="transaction.required_by">
                    <span class="label">Required By</span>
                    <span class="value">{{transaction.required_by | date}}</span>
                  </div>
                </div>
              </div>
  
              <mat-divider></mat-divider>
  
              <!-- Notes Section -->
              <div class="detail-section">
                <div class="section-header">
                  <h3>Notes</h3>
                </div>
                <div class="notes-container">
                  <p *ngIf="transaction.notes">{{transaction.notes}}</p>
                  <p *ngIf="!transaction.notes" class="no-notes">No notes added to this transaction.</p>
                </div>
              </div>
            </div>
          </mat-tab>
  
          <!-- History Tab -->
          <mat-tab label="Status History">
            <div class="history-container">
              <div class="timeline">
                <div class="timeline-item">
                  <div class="timeline-marker"></div>
                  <div class="timeline-content">
                    <h4>Transaction Created</h4>
                    <p class="timeline-date">{{formatDate(transaction.created_at)}}</p>
                    <p>Transaction was created with status: <span class="status-highlight">{{transaction.status | titlecase}}</span></p>
                  </div>
                </div>
                
                <!-- Add more timeline items here, if you have status history data -->
                <!-- Example:
                <div class="timeline-item" *ngFor="let history of transaction.status_history">
                  <div class="timeline-marker"></div>
                  <div class="timeline-content">
                    <h4>Status Changed to {{history.status | titlecase}}</h4>
                    <p class="timeline-date">{{formatDate(history.updated_at)}}</p>
                    <p>{{history.notes}}</p>
                  </div>
                </div>
                -->
                
                <div class="timeline-item current">
                  <div class="timeline-marker"></div>
                  <div class="timeline-content">
                    <h4>Current Status: {{transaction.status | titlecase}}</h4>
                    <p class="timeline-date">
                      {{transaction.status_updated_at ? formatDate(transaction.status_updated_at) : formatDate(transaction.created_at)}}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </mat-card>
  </div>
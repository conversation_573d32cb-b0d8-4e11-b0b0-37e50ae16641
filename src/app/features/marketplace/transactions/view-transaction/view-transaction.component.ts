import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { TransactionService } from '../../../../core/auth/services/transaction.service';
import { ImageViewerComponent } from '../../../../components/image-viewer.component';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-view-transaction',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatTabsModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatMenuModule,
    MatDialogModule,
  ],
  templateUrl: './view-transaction.component.html',
  styleUrls: ['./view-transaction.component.scss'],
})
export class ViewTransactionComponent implements OnInit {
  transaction: any = null;
  isLoading = true;
  error: string | null = null;
  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private transactionService: TransactionService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadTransaction();
  }

  loadTransaction(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.error = 'Transaction ID is required';
      this.isLoading = false;
      return;
    }

    this.transactionService.getTransactionById(parseInt(id)).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.transaction = response.data;
        } else {
          this.error = 'Failed to load transaction details';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.error =
          error.message || 'An error occurred while loading transaction';
        this.isLoading = false;
      },
    });
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    event.target.src = 'assets/images/placeholder-image.png';
  }

  viewImage(imageUrl: string): void {
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl: this.getFullImageUrl(imageUrl) },
    });
  }

  goBack(): void {
    this.router.navigate(['/marketplace/transactions']);
  }

  viewFarmerProfile(farmerId: number): void {
    this.router.navigate(['/farmers/view', farmerId]);
  }

  viewRequirement(requirementId: number): void {
    this.router.navigate([
      '/marketplace/requirements/view-requirement',
      requirementId,
    ]);
  }

  updateStatus(newStatus: string): void {
    if (!this.transaction) return;

    this.transactionService
      .updateTransactionStatus(this.transaction.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open(
              `Transaction status updated to ${newStatus}`,
              'Close',
              {
                duration: 3000,
              }
            );
            this.loadTransaction(); // Reload to get updated data
          } else {
            this.snackBar.open('Failed to update transaction status', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error) => {
          this.snackBar.open(
            error.message || 'Error updating transaction status',
            'Close',
            {
              duration: 3000,
            }
          );
        },
      });
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'accepted':
        return 'status-accepted';
      case 'in_progress':
        return 'status-in-progress';
      case 'delivered':
        return 'status-delivered';
      case 'closed':
        return 'status-closed';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleString();
  }
}

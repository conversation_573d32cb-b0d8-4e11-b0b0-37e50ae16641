.transaction-detail-container {
    padding: 24px;
  
    mat-card {
      overflow: hidden;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
  
      .header-title {
        display: flex;
        align-items: center;
        gap: 8px;
  
        h2 {
          margin: 0;
          font-size: 24px;
          font-weight: 500;
        }
      }
  
      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 48px 0;
    }
  
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 48px 0;
      text-align: center;
  
      mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
      }
  
      p {
        margin-bottom: 24px;
        font-size: 16px;
        color: #e53935;
      }
    }
  
    .card-content {
      padding: 24px;
  
      .overview-container {
        padding: 16px 0;
  
        .transaction-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;
  
          .status-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
  
            .status-chip {
              padding: 6px 12px;
              border-radius: 16px;
              display: inline-block;
              text-align: center;
              min-width: 100px;
              font-size: 14px;
              font-weight: 500;
              
              &.status-accepted {
                background-color: #e3f2fd;
                color: #1976d2;
              }
              
              &.status-in-progress {
                background-color: #fff8e1;
                color: #ffa000;
              }
              
              &.status-delivered {
                background-color: #e8f5e9;
                color: #388e3c;
              }
              
              &.status-closed {
                background-color: #fafafa;
                color: #757575;
              }
              
              &.status-cancelled {
                background-color: #ffebee;
                color: #d32f2f;
              }
            }
  
            .transaction-date {
              font-size: 14px;
              color: #757575;
            }
          }
  
          .transaction-type {
            mat-chip {
              font-size: 14px;
            }
          }
        }
      }
  
      .detail-section {
        margin: 24px 0;
  
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
  
          h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
          }
        }
  
        .detail-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 16px;
  
          .detail-item {
            &.full-width {
              grid-column: 1 / -1;
            }
  
            .label {
              display: block;
              font-size: 12px;
              color: #757575;
              margin-bottom: 4px;
            }
  
            .value {
              font-size: 16px;
              font-weight: 500;
  
              &.total-amount {
                color: #1976d2;
                font-weight: 600;
              }
            }
          }
        }
  
        .produce-container {
          display: flex;
          gap: 24px;
  
          .produce-image {
            flex: 0 0 200px;
  
            img {
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 8px;
              cursor: pointer;
              transition: transform 0.2s;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
              &:hover {
                transform: scale(1.05);
              }
            }
          }
  
          .produce-details {
            flex: 1;
          }
        }
  
        .notes-container {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 16px;
          border-left: 4px solid #1976d2;
  
          p {
            margin: 0;
            white-space: pre-line;
          }
  
          .no-notes {
            color: #757575;
            font-style: italic;
          }
        }
      }
  
      .history-container {
        padding: 24px 0;
  
        .timeline {
          position: relative;
          margin-left: 20px;
          padding: 16px 0;
  
          &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 2px;
            background-color: #e0e0e0;
          }
  
          .timeline-item {
            position: relative;
            margin-bottom: 32px;
  
            &:last-child {
              margin-bottom: 0;
            }
  
            &.current {
              .timeline-marker {
                background-color: #1976d2;
                border-color: #e3f2fd;
              }
  
              .timeline-content {
                h4 {
                  color: #1976d2;
                }
              }
            }
  
            .timeline-marker {
              position: absolute;
              left: -8px;
              top: 5px;
              width: 14px;
              height: 14px;
              border-radius: 50%;
              background-color: white;
              border: 2px solid #1976d2;
              z-index: 1;
            }
  
            .timeline-content {
              padding-left: 20px;
  
              h4 {
                margin: 0 0 8px;
                font-size: 16px;
                font-weight: 500;
              }
  
              .timeline-date {
                font-size: 14px;
                color: #757575;
                margin-bottom: 8px;
              }
  
              p {
                margin: 0;
              }
  
              .status-highlight {
                font-weight: 500;
                color: #1976d2;
              }
            }
          }
        }
      }
    }
  
    // Styling for mat-tabs to match your application's design
    ::ng-deep {
      .mat-mdc-tab-header {
        margin-bottom: 16px;
      }
  
      .mat-mdc-tab-body-content {
        overflow: visible;
      }
  
      // Make dividers more subtle
      .mat-divider {
        margin: 16px 0;
      }
  
      // Style chips for better visibility
      .mat-mdc-chip {
        font-weight: 500;
      }
  
      // Ensure buttons have proper spacing
      .mat-mdc-button, .mat-mdc-raised-button, .mat-mdc-stroked-button {
        .mat-icon {
          margin-right: 4px;
        }
      }
    }
  }
  
  // Add print-friendly styles
  @media print {
    .transaction-detail-container {
      padding: 0;
  
      mat-card {
        box-shadow: none;
      }
  
      .card-header {
        .header-actions {
          display: none;
        }
      }
  
      // Hide the tabs navigation
      ::ng-deep {
        .mat-mdc-tab-header {
          display: none;
        }
  
        // Show all tab content when printing
        .mat-mdc-tab-body {
          display: block !important;
          opacity: 1 !important;
        }
  
        // Hide buttons when printing
        button {
          display: none;
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .transaction-detail-container {
      padding: 16px 8px;
  
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
  
        .header-actions {
          width: 100%;
        }
      }
  
      .card-content {
        padding: 16px;
  
        .overview-container {
          .transaction-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
          }
        }
  
        .detail-section {
          .section-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
  
          .produce-container {
            flex-direction: column;
  
            .produce-image {
              flex: 0 0 auto;
              width: 100%;
  
              img {
                height: 180px;
              }
            }
          }
  
          .detail-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
  
  // For extra small screens
  @media (max-width: 480px) {
    .transaction-detail-container {
      padding: 8px 4px;
  
      .card-header {
        padding: 12px 16px;
  
        .header-title {
          h2 {
            font-size: 20px;
          }
        }
      }
  
      .card-content {
        padding: 12px;
  
        .detail-section {
          margin: 16px 0;
  
          .section-header {
            h3 {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
.transactions-container {
    padding: 24px;
    
    mat-card {
      overflow: hidden;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        button {
          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }
    
    .filter-container {
      padding: 16px 24px;
      border-bottom: 1px solid #e0e0e0;
      
      .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;
        
        mat-form-field {
          flex: 1;
          min-width: 200px;
        }
        
        .filter-buttons {
          display: flex;
          gap: 8px;
        }
      }
    }
    
    .table-container {
      position: relative;
      min-height: 400px;
      overflow: auto;
      
      .loading-shade {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .transactions-table {
        width: 100%;
        
        .mat-column-actions {
          width: 100px;
          text-align: right;
        }
        
        .mat-column-status {
          width: 120px;
        }
        
        .mat-column-id {
          width: 60px;
        }
        
        .mat-column-created_at {
          width: 120px;
        }
        
        .mat-column-type {
          width: 150px;
        }
        
        .status-chip {
          padding: 4px 8px;
          border-radius: 16px;
          display: inline-block;
          text-align: center;
          min-width: 80px;
          font-size: 12px;
          font-weight: 500;
          
          &.status-accepted {
            background-color: #e3f2fd;
            color: #1976d2;
          }
          
          &.status-in-progress {
            background-color: #fff8e1;
            color: #ffa000;
          }
          
          &.status-delivered {
            background-color: #e8f5e9;
            color: #388e3c;
          }
          
          &.status-closed {
            background-color: #fafafa;
            color: #757575;
          }
          
          &.status-cancelled {
            background-color: #ffebee;
            color: #d32f2f;
          }
        }
        
        .mat-mdc-row:hover {
          background-color: rgba(0, 0, 0, 0.04);
        }
        
        .farmer-info {
          display: flex;
          flex-direction: column;
          
          .farmer-name {
            font-weight: 500;
            margin-bottom: 4px;
          }
          
          .farmer-actions {
            display: flex;
            gap: 4px;
          }
        }
        
        .crop-details {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .crop-image-container {
            flex-shrink: 0;
            width: 40px;
            height: 40px;
          }
          
          .crop-thumbnail {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.2s;
            
            &:hover {
              transform: scale(1.1);
            }
          }
          
          .crop-info {
            display: flex;
            flex-direction: column;
            
            .crop-name {
              font-weight: 500;
            }
            
            .crop-variety {
              font-size: 12px;
              color: #757575;
              font-style: italic;
            }
            
            .requirement-link {
              margin-top: 4px;
              
              a {
                padding: 0;
                line-height: 1;
                min-width: unset;
              }
            }
          }
        }
        
        .price-info {
          display: flex;
          flex-direction: column;
          
          .price-per-unit {
            font-weight: 500;
          }
          
          .total-amount {
            margin-top: 4px;
            
            small {
              color: #757575;
            }
          }
        }
        
        .type-tag {
          mat-chip {
            height: 24px;
            font-size: 12px;
          }
        }
      }
      
      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 32px;
        color: rgba(0, 0, 0, 0.54);
        
        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          margin-bottom: 16px;
        }
      }
    }
  }
  
  // Make table layout more compact
  ::ng-deep {
    .mat-mdc-table {
      .mat-mdc-row, .mat-mdc-header-row {
        height: 48px; // Reduced row height
      }
      
      .mat-mdc-cell, .mat-mdc-header-cell {
        padding: 0 12px; // Reduced horizontal padding
      }
    }
    
    // Ensure proper alignment for paginator
    .mat-mdc-paginator {
      border-top: 1px solid rgba(0, 0, 0, 0.12);
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .transactions-container {
      padding: 16px 8px;
      
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        
        .header-actions {
          width: 100%;
        }
      }
      
      .filter-container {
        padding: 16px;
        
        .filter-row {
          flex-direction: column;
          align-items: stretch;
          
          .filter-buttons {
            margin-top: 8px;
          }
        }
      }
    }
  }
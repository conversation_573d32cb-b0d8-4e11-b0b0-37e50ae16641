import { <PERSON>mpo<PERSON>, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { Router } from '@angular/router';

import {
  Transaction,
  TransactionService,
} from '../../../../core/auth/services/transaction.service';
import { FarmerService } from '../../../../core/auth/services/farmer.service';
import { CropService } from '../../../../core/auth/services/masters/crop.service';
import { RequirementService } from '../../../../core/auth/services/requirement.service';
import { ImageViewerComponent } from '../../../../components/image-viewer.component';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-list-transactions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatMenuModule,
    MatDialogModule,
    MatTooltipModule,
  ],
  templateUrl: './list-transactions.component.html',
  styleUrls: ['./list-transactions.component.scss'],
})
export class ListTransactionsComponent implements OnInit {
  displayedColumns: string[] = [
    'id',
    'farmer_info',
    'crop_details',
    'type',
    'quantity',
    'price',
    'created_at',
    'status',
    'actions',
  ];

  dataSource = new MatTableDataSource<Transaction>([]);
  filterForm: FormGroup;
  isLoading = false;

  farmers: any[] = [];
  crops: any[] = [];
  requirements: any[] = [];

  statusOptions = [
    { value: 'accepted', label: 'Accepted' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'closed', label: 'Closed' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  transactionTypes = [
    { value: 'requirement_based', label: 'Requirement Based' },
    { value: 'direct_purchase', label: 'Direct Purchase' },
  ];

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private transactionService: TransactionService,
    private farmerService: FarmerService,
    private cropService: CropService,
    private requirementService: RequirementService,
    private fb: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {
    this.filterForm = this.fb.group({
      farmer_id: [''],
      requirement_id: [''],
      produce_id: [''],
      status: [''],
      transaction_type: [''],
    });
  }

  ngOnInit(): void {
    this.loadReferenceData();
    this.loadTransactions();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadReferenceData(): void {
    // Load farmers for filter
    this.farmerService.getAllFarmers().subscribe({
      next: (farmers) => {
        this.farmers = farmers;
      },
      error: (error) => {
        console.error('Error loading farmers:', error);
      },
    });

    // Load crops for filter
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.crops = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading crops:', error);
      },
    });

    // Load requirements for filter
    this.requirementService.getAllRequirements({}).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.requirements = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading requirements:', error);
      },
    });
  }

  loadTransactions(): void {
    this.isLoading = true;

    const filters = this.filterForm.value;

    this.transactionService.getAllTransactions(filters).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.dataSource.data = response.data;
        } else {
          this.snackBar.open('Failed to load transactions', 'Close', {
            duration: 3000,
          });
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading transactions:', error);
        this.snackBar.open(
          error.message || 'Failed to load transactions',
          'Close',
          { duration: 3000 }
        );
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    this.loadTransactions();
  }

  resetFilter(): void {
    this.filterForm.reset();
    this.loadTransactions();
  }

  exportTransactions(): void {
    const filters = this.filterForm.value;

    this.transactionService.exportTransactions(filters).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `transactions_${
          new Date().toISOString().split('T')[0]
        }.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
      },
      error: (error) => {
        console.error('Error exporting transactions:', error);
        this.snackBar.open('Failed to export transactions', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  updateTransactionStatus(transactionId: number, newStatus: string): void {
    this.transactionService
      .updateTransactionStatus(transactionId, newStatus)
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open(
              `Transaction status updated to ${newStatus}`,
              'Close',
              { duration: 3000 }
            );
            this.loadTransactions();
          } else {
            this.snackBar.open('Failed to update transaction status', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error) => {
          console.error('Error updating transaction status:', error);
          this.snackBar.open(
            error.message || 'Failed to update transaction status',
            'Close',
            { duration: 3000 }
          );
        },
      });
  }

  viewTransactionDetails(transactionId: number): void {
    if (transactionId) {
      this.router.navigate(['/marketplace/transactions/view', transactionId]);
    }
  }

  viewFarmerProfile(farmerId: number): void {
    if (farmerId) {
      const baseUrl = window.location.origin;
      window.open(`${baseUrl}/farmers/view/${farmerId}`, '_blank');
    }
  }

  viewRequirementDetails(requirementId: number): void {
    if (requirementId) {
      const baseUrl = window.location.origin;
      window.open(
        `${baseUrl}/marketplace/requirements/view-requirement/${requirementId}`,
        '_blank'
      );
    }
  }

  viewImage(imageUrl: string): void {
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl: this.getFullImageUrl(imageUrl) },
    });
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    event.target.src = 'assets/images/placeholder-image.png';
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'accepted':
        return 'status-accepted';
      case 'in_progress':
        return 'status-in-progress';
      case 'delivered':
        return 'status-delivered';
      case 'closed':
        return 'status-closed';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }
}

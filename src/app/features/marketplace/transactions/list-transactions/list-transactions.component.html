<div class="transactions-container">
    <mat-card>
      <div class="card-header">
        <h2>Marketplace Transactions</h2>
        <div class="header-actions">
          <button mat-raised-button color="accent" (click)="exportTransactions()">
            <mat-icon>download</mat-icon> Export to Excel
          </button>
        </div>
      </div>
  
      <!-- Filter Form -->
      <div class="filter-container">
        <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
          <div class="filter-row">
            <mat-form-field appearance="outline">
              <mat-label>Farmer</mat-label>
              <mat-select formControlName="farmer_id">
                <mat-option [value]="">All Farmers</mat-option>
                <mat-option *ngFor="let farmer of farmers" [value]="farmer.id">
                  {{farmer.full_name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Requirement</mat-label>
              <mat-select formControlName="requirement_id">
                <mat-option [value]="">All Requirements</mat-option>
                <mat-option *ngFor="let req of requirements" [value]="req.id">
                  #{{req.id}} - {{req.crop_name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Crop</mat-label>
              <mat-select formControlName="produce_id">
                <mat-option [value]="">All Crops</mat-option>
                <mat-option *ngFor="let crop of crops" [value]="crop.id">
                  {{crop.crop_name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select formControlName="status">
                <mat-option [value]="">All Statuses</mat-option>
                <mat-option *ngFor="let option of statusOptions" [value]="option.value">
                  {{option.label}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Transaction Type</mat-label>
              <mat-select formControlName="transaction_type">
                <mat-option [value]="">All Types</mat-option>
                <mat-option *ngFor="let type of transactionTypes" [value]="type.value">
                  {{type.label}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <div class="filter-buttons">
              <button mat-raised-button color="primary" type="submit">
                <mat-icon>search</mat-icon> Filter
              </button>
              <button mat-stroked-button color="warn" type="button" (click)="resetFilter()">
                <mat-icon>clear</mat-icon> Reset
              </button>
            </div>
          </div>
        </form>
      </div>
  
      <!-- Transactions Table -->
      <div class="table-container">
        <div *ngIf="isLoading" class="loading-shade">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
  
        <table mat-table [dataSource]="dataSource" matSort class="transactions-table">
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
            <td mat-cell *matCellDef="let transaction"> {{transaction.id}} </td>
          </ng-container>
  
          <!-- Farmer Info Column -->
          <ng-container matColumnDef="farmer_info">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Farmer </th>
            <td mat-cell *matCellDef="let transaction">
              <div class="farmer-info">
                <div class="farmer-name">{{transaction.farmer_name}}</div>
                <div class="farmer-actions">
                  <button mat-icon-button color="primary" matTooltip="View Farmer Profile" (click)="viewFarmerProfile(transaction.farmer_id)">
                    <mat-icon>person</mat-icon>
                  </button>
                </div>
              </div>
            </td>
          </ng-container>
  
          <!-- Crop Details Column -->
          <ng-container matColumnDef="crop_details">
            <th mat-header-cell *matHeaderCellDef> Crop / Requirement </th>
            <td mat-cell *matCellDef="let transaction">
              <div class="crop-details">
                <div class="crop-image-container" *ngIf="transaction.produce_image_url">
                  <img [src]="getFullImageUrl(transaction.produce_image_url)" 
                      (click)="viewImage(transaction.produce_image_url)" 
                      class="crop-thumbnail" 
                      alt="{{transaction.crop_name}}"
                      (error)="handleImageError($event)">
                </div>
                <div class="crop-info">
                  <div class="crop-name">{{transaction.crop_name}}</div>
                  <div class="crop-variety" *ngIf="transaction.variety">{{transaction.variety}}</div>
                  <div class="requirement-link" *ngIf="transaction.fpo_requirement_id">
                    <a mat-button color="primary" (click)="viewRequirementDetails(transaction.fpo_requirement_id)">
                      <small>Requirement #{{transaction.fpo_requirement_id}}</small>
                    </a>
                  </div>
                </div>
              </div>
            </td>
          </ng-container>
  
          <!-- Transaction Type Column -->
          <ng-container matColumnDef="type">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Type </th>
            <td mat-cell *matCellDef="let transaction">
              <div class="type-tag">
                <mat-chip [color]="transaction.transaction_type === 'requirement_based' ? 'primary' : 'accent'" selected>
                  {{ transaction.transaction_type === 'requirement_based' ? 'Requirement' : 'Direct Purchase' }}
                </mat-chip>
              </div>
            </td>
          </ng-container>
  
          <!-- Quantity Column -->
          <ng-container matColumnDef="quantity">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Quantity </th>
            <td mat-cell *matCellDef="let transaction">
              {{transaction.quantity}} {{transaction.unit_type}}
            </td>
          </ng-container>
  
          <!-- Price Column -->
          <ng-container matColumnDef="price">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Price </th>
            <td mat-cell *matCellDef="let transaction">
              <div class="price-info">
                <div class="price-per-unit">{{formatCurrency(transaction.price_per_unit)}}/{{transaction.unit_type}}</div>
                <div class="total-amount">
                  <small>Total: {{formatCurrency(transaction.total_amount)}}</small>
                </div>
              </div>
            </td>
          </ng-container>
  
          <!-- Created At Column -->
          <ng-container matColumnDef="created_at">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Date </th>
            <td mat-cell *matCellDef="let transaction"> {{transaction.created_at | date}} </td>
          </ng-container>
          
          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td mat-cell *matCellDef="let transaction">
              <div class="status-chip" [ngClass]="getStatusClass(transaction.status)">
                {{transaction.status | titlecase}}
              </div>
            </td>
          </ng-container>
  
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let transaction">
              <button mat-icon-button color="primary" 
                      (click)="viewTransactionDetails(transaction.id)" 
                      matTooltip="View Details">
                <mat-icon>visibility</mat-icon>
              </button>
              
              <button mat-icon-button [matMenuTriggerFor]="statusMenu" 
                      matTooltip="Change Status"
                      *ngIf="transaction.status !== 'cancelled' && transaction.status !== 'closed'">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #statusMenu="matMenu">
                <button mat-menu-item (click)="updateTransactionStatus(transaction.id, 'accepted')" 
                       *ngIf="transaction.status !== 'accepted'">
                  <mat-icon>check_circle</mat-icon>
                  <span>Accept Transaction</span>
                </button>
                <button mat-menu-item (click)="updateTransactionStatus(transaction.id, 'in_progress')" 
                       *ngIf="transaction.status !== 'in_progress'">
                  <mat-icon>sync</mat-icon>
                  <span>Mark as In Progress</span>
                </button>
                <button mat-menu-item (click)="updateTransactionStatus(transaction.id, 'delivered')" 
                       *ngIf="transaction.status !== 'delivered'">
                  <mat-icon>local_shipping</mat-icon>
                  <span>Mark as Delivered</span>
                </button>
                <button mat-menu-item (click)="updateTransactionStatus(transaction.id, 'closed')" 
                       *ngIf="transaction.status !== 'closed'">
                  <mat-icon>task_alt</mat-icon>
                  <span>Close Transaction</span>
                </button>
                <button mat-menu-item (click)="updateTransactionStatus(transaction.id, 'cancelled')" 
                       *ngIf="transaction.status !== 'cancelled'">
                  <mat-icon>cancel</mat-icon>
                  <span>Cancel Transaction</span>
                </button>
              </mat-menu>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  
          <!-- No Data Row -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="8">
              <div class="no-data">
                <mat-icon>search_off</mat-icon>
                <span>No transactions found</span>
              </div>
            </td>
          </tr>
        </table>
  
        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </div>
    </mat-card>
  </div>
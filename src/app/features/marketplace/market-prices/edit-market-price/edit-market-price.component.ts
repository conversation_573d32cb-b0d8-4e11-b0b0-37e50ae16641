// src/app/features/marketplace/market-prices/edit-market-price/edit-market-price.component.ts

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MarketPriceService } from '../../../../core/auth/services/market-price.service';
import { CropService } from '../../../../core/auth/services/masters/crop.service';
import { environment } from '../../../../../environments/environment';

// Utility function for date conversion to IST
function toISTDateString(date: Date): string {
  // Add 5 hours and 30 minutes to get IST
  const istDate = new Date(date.getTime() + 5.5 * 60 * 60 * 1000);
  return istDate.toISOString().split('T')[0];
}

@Component({
  selector: 'app-edit-market-price',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCardModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './edit-market-price.component.html',
  styleUrls: ['./edit-market-price.component.scss'],
})
export class EditMarketPriceComponent implements OnInit {
  priceForm: FormGroup;
  crops: any[] = [];
  isLoading = true;
  isSubmitting = false;
  priceId: number | null = null;
  selectedCropImage: string | null = null;
  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    private fb: FormBuilder,
    private marketPriceService: MarketPriceService,
    private cropService: CropService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.priceForm = this.fb.group({
      product_id: ['', Validators.required],
      price_per_unit: ['', [Validators.required, Validators.min(0.01)]],
      unit_type: ['', Validators.required],
      price_date: ['', Validators.required],
      notes: [''],
    });
  }

  ngOnInit(): void {
    this.loadCrops();

    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.priceId = +params['id'];
        this.loadPrice(this.priceId);
      } else {
        this.isLoading = false;
        this.snackBar.open('Invalid market price ID', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.goBack();
      }
    });
  }

  loadCrops(): void {
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.crops = response.data.filter((crop: any) => crop.is_active);
        }
      },
      error: (error) => {
        this.snackBar.open('Error loading crops', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
      },
    });
  }

  loadPrice(id: number): void {
    this.isLoading = true;
    this.marketPriceService.getPriceById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          const price = response.data;

          // Format the date
          const priceDateObj = new Date(price.price_date);

          this.priceForm.patchValue({
            product_id: price.product_id,
            price_per_unit: price.price_per_unit,
            unit_type: price.unit_type,
            price_date: priceDateObj,
            notes: price.notes,
          });

          // Set the crop image if available
          if (price.crop_image_url) {
            this.selectedCropImage = price.crop_image_url.startsWith('http')
              ? price.crop_image_url
              : this.apiBaseUrl + price.crop_image_url;
          }

          this.onCropSelected({ value: price.product_id });
        } else {
          this.snackBar.open('Market price not found', 'Close', {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          });
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error loading market price', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
        this.goBack();
      },
    });
  }

  onCropSelected(event: any): void {
    const selectedCropId = event.value;
    const selectedCrop = this.crops.find((crop) => crop.id === selectedCropId);
    if (selectedCrop && selectedCrop.image_url) {
      this.selectedCropImage = selectedCrop.image_url;
    } else {
      this.selectedCropImage = null;
    }
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    // Otherwise, prepend the API URL
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  onSubmit(): void {
    if (this.priceForm.valid && this.priceId) {
      this.isSubmitting = true;

      // Format the date properly using IST conversion
      const formValue = { ...this.priceForm.value };
      if (formValue.price_date instanceof Date) {
        formValue.price_date = toISTDateString(formValue.price_date);
      }

      this.marketPriceService.updatePrice(this.priceId, formValue).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open('Market price updated successfully', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
            this.goBack();
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || 'Error updating market price',
            'Close',
            {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            }
          );
          this.isSubmitting = false;
        },
      });
    } else {
      // Mark all form controls as touched to trigger validation messages
      Object.keys(this.priceForm.controls).forEach((key) => {
        this.priceForm.get(key)?.markAsTouched();
      });
    }
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }
}

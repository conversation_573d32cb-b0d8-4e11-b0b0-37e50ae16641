// src/app/features/marketplace/market-prices/edit-market-price/edit-market-price.component.scss
.edit-price-container {
    padding: 24px;
    
    mat-card {
      overflow: hidden;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }
    
    .card-content {
      padding: 24px;
      position: relative;
    }
    
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.7);
      z-index: 10;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
      
      .full-width {
        grid-column: 1 / -1;
      }
    }
    
    .image-upload-section {
      margin-bottom: 24px;
      
      h3 {
        margin-top: 0;
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 500;
      }
      
      .image-upload-container {
        border: 2px dashed #ccc;
        border-radius: 4px;
        padding: 16px;
        min-height: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
        
        .image-preview {
          position: relative;
          max-width: 300px;
          
          img {
            width: 100%;
            max-height: 300px;
            object-fit: contain;
            border-radius: 4px;
          }
          
          .remove-image-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background-color: rgba(255, 255, 255, 0.8);
          }
        }
        
        .upload-button-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .hint-text {
            margin-top: 8px;
            color: rgba(0, 0, 0, 0.54);
            font-size: 12px;
          }
        }
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
      
      button {
        min-width: 100px;
        
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .edit-price-container {
      padding: 16px 8px;
      
      .card-content {
        padding: 16px;
      }
      
      .form-grid {
        grid-template-columns: 1fr;
      }
      
      .form-actions {
        flex-direction: column-reverse;
        
        button {
          width: 100%;
        }
      }
    }
  }
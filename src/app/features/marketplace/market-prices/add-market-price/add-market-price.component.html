<!-- src/app/features/marketplace/market-prices/add-market-price/add-market-price.component.html -->
<div class="add-price-container">
  <mat-card>
    <div class="card-header">
      <h2>Add Market Price</h2>
      <button mat-icon-button (click)="goBack()" matTooltip="Back">
        <mat-icon>arrow_back</mat-icon>
      </button>
    </div>

    <div class="card-content">
      <form [formGroup]="priceForm" (ngSubmit)="onSubmit()">
        <div class="form-grid">
          <!-- Product/Crop Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Product</mat-label>
            <mat-select formControlName="product_id" required (selectionChange)="onCropSelected($event)">
              <mat-option *ngFor="let crop of crops" [value]="crop.id">
                {{crop.crop_name}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="priceForm.get('product_id')?.hasError('required')">
              Product is required
            </mat-error>
          </mat-form-field>

          <!-- Price Per Unit -->
          <mat-form-field appearance="outline">
            <mat-label>Price Per Unit</mat-label>
            <input matInput type="number" formControlName="price_per_unit" required min="0.01" step="0.01">
            <span matSuffix>₹</span>
            <mat-error *ngIf="priceForm.get('price_per_unit')?.hasError('required')">
              Price is required
            </mat-error>
            <mat-error *ngIf="priceForm.get('price_per_unit')?.hasError('min')">
              Price must be greater than 0
            </mat-error>
          </mat-form-field>

          <!-- Unit Type -->
          <mat-form-field appearance="outline">
            <mat-label>Unit Type</mat-label>
            <mat-select formControlName="unit_type" required>
              <mat-option value="kg">Kilogram (kg)</mat-option>
              <mat-option value="g">Gram (g)</mat-option>
              <mat-option value="quintal">Quintal</mat-option>
              <mat-option value="ton">Ton</mat-option>
              <mat-option value="piece">Piece</mat-option>
              <mat-option value="dozen">Dozen</mat-option>
            </mat-select>
            <mat-error *ngIf="priceForm.get('unit_type')?.hasError('required')">
              Unit type is required
            </mat-error>
          </mat-form-field>

          <!-- Price Date -->
          <mat-form-field appearance="outline">
            <mat-label>Price Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="price_date" required>
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="priceForm.get('price_date')?.hasError('required')">
              Date is required
            </mat-error>
          </mat-form-field>

          <!-- Notes -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Notes</mat-label>
            <textarea matInput formControlName="notes" rows="3"></textarea>
          </mat-form-field>
        </div>

        <!-- Crop Image Preview -->
        <div class="crop-image-section" *ngIf="selectedCropImage">
          <h3>Crop Image</h3>
          <div class="crop-image-container">
            <img [src]="getFullImageUrl(selectedCropImage)" alt="Crop image" class="crop-image">
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button type="button" mat-stroked-button (click)="goBack()">Cancel</button>
          <button type="submit" mat-raised-button color="primary" [disabled]="isLoading">
            <mat-icon>save</mat-icon>
            Save
          </button>
        </div>
      </form>
    </div>
  </mat-card>
</div>
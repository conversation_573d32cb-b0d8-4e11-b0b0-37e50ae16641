// src/app/features/marketplace/market-prices/add-market-price/add-market-price.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MarketPriceService } from '../../../../core/auth/services/market-price.service';
import { CropService } from '../../../../core/auth/services/masters/crop.service';
import { environment } from '../../../../../environments/environment';

// Utility function for date conversion to IST
function toISTDateString(date: Date): string {
  // Add 5 hours and 30 minutes to get IST
  const istDate = new Date(date.getTime() + 5.5 * 60 * 60 * 1000);
  return istDate.toISOString().split('T')[0];
}

@Component({
  selector: 'app-add-market-price',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCardModule,
    MatSnackBarModule,
    MatTooltipModule,
  ],
  templateUrl: './add-market-price.component.html',
  styleUrls: ['./add-market-price.component.scss'],
})
export class AddMarketPriceComponent implements OnInit {
  priceForm: FormGroup;
  crops: any[] = [];
  isLoading = false;
  selectedCropImage: string | null = null;
  apiUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    private fb: FormBuilder,
    private marketPriceService: MarketPriceService,
    private cropService: CropService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.priceForm = this.fb.group({
      product_id: ['', Validators.required],
      price_per_unit: ['', [Validators.required, Validators.min(0.01)]],
      unit_type: ['kg', Validators.required],
      price_date: [new Date(), Validators.required],
      notes: [''],
    });
  }

  ngOnInit(): void {
    this.loadCrops();
  }

  loadCrops(): void {
    this.isLoading = true;
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.crops = response.data.filter((crop: any) => crop.is_active);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error loading crops', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
      },
    });
  }

  onCropSelected(event: any): void {
    const selectedCropId = event.value;
    const selectedCrop = this.crops.find((crop) => crop.id === selectedCropId);
    if (selectedCrop && selectedCrop.image_url) {
      this.selectedCropImage = selectedCrop.image_url;
    } else {
      this.selectedCropImage = null;
    }
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    // Otherwise, prepend the API URL
    return `${this.apiUrl}${imageUrl}`;
  }

  onSubmit(): void {
    if (this.priceForm.valid) {
      this.isLoading = true;

      // Format the date properly using IST conversion
      const formValue = { ...this.priceForm.value };
      if (formValue.price_date instanceof Date) {
        formValue.price_date = toISTDateString(formValue.price_date);
      }

      this.marketPriceService.createPrice(formValue).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open('Market price added successfully', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
            this.router.navigate(['../'], { relativeTo: this.route });
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || 'Error adding market price',
            'Close',
            {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            }
          );
          this.isLoading = false;
        },
      });
    } else {
      // Mark all form controls as touched to trigger validation messages
      Object.keys(this.priceForm.controls).forEach((key) => {
        this.priceForm.get(key)?.markAsTouched();
      });
    }
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }
}

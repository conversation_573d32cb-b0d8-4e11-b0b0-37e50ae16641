// src/app/features/marketplace/market-prices/add-market-price/add-market-price.component.scss
.add-price-container {
  padding: 24px;
  
  mat-card {
    overflow: hidden;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
  }
  
  .card-content {
    padding: 24px;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    
    .full-width {
      grid-column: 1 / -1;
    }
  }
  
  .crop-image-section {
    margin-bottom: 24px;
    
    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;
    }
    
    .crop-image-container {
      display: flex;
      justify-content: center;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background-color: #f9f9f9;
      
      .crop-image {
        max-width: 200px;
        max-height: 200px;
        object-fit: contain;
        border-radius: 4px;
      }
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    
    button {
      min-width: 100px;
      
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .add-price-container {
    padding: 16px 8px;
    
    .card-content {
      padding: 16px;
    }
    
    .form-grid {
      grid-template-columns: 1fr;
    }
    
    .form-actions {
      flex-direction: column-reverse;
      
      button {
        width: 100%;
      }
    }
  }
}
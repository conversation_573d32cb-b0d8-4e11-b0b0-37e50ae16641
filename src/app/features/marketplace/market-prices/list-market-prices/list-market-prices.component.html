<!-- src/app/features/marketplace/market-prices/list-market-prices.component.html -->
<div class="market-prices-container">
  <mat-card>
    <div class="card-header">
      <h2>Market Prices</h2>
      <div class="header-actions">
        <button mat-raised-button color="accent" (click)="showTodaysPrices()">
          <mat-icon>today</mat-icon> Today's Prices
        </button>
        <button mat-raised-button color="primary" routerLink="/marketplace/market-prices/add-market-price">
          <mat-icon>add</mat-icon> Add Market Price
        </button>
        <button mat-raised-button color="accent" (click)="downloadTemplate()">
          <mat-icon>cloud_download</mat-icon> Download Template
        </button>
        <button mat-raised-button color="accent" (click)="fileInput.click()">
          <mat-icon>cloud_upload</mat-icon> Upload Prices
        </button>
        <input hidden type="file" #fileInput (change)="uploadPriceSheet($event)" accept=".xlsx,.xls">
        <button mat-raised-button color="accent" (click)="exportPrices()">
          <mat-icon>download</mat-icon> Export
        </button>
      </div>
    </div>

    <!-- Filter Form -->
    <div class="filter-container">
      <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
        <div class="filter-row">
          <mat-form-field appearance="outline">
            <mat-label>Product</mat-label>
            <mat-select formControlName="product_id">
              <mat-option [value]="">All Products</mat-option>
              <mat-option *ngFor="let crop of crops" [value]="crop.id">
                {{crop.crop_name}}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>From Date</mat-label>
            <input matInput [matDatepicker]="fromPicker" formControlName="date_from">
            <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
            <mat-datepicker #fromPicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>To Date</mat-label>
            <input matInput [matDatepicker]="toPicker" formControlName="date_to">
            <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
            <mat-datepicker #toPicker></mat-datepicker>
          </mat-form-field>

          <div class="filter-buttons">
            <button mat-raised-button color="primary" type="submit">
              <mat-icon>search</mat-icon> Filter
            </button>
            <button mat-stroked-button color="warn" type="button" (click)="resetFilter()">
              <mat-icon>clear</mat-icon> Reset
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- Market Prices Table -->
    <div class="table-container">
      <div *ngIf="isLoading" class="loading-shade">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <table mat-table [dataSource]="dataSource" matSort class="prices-table">
        <!-- Crop Image Column -->
        <ng-container matColumnDef="image_url">
          <th mat-header-cell *matHeaderCellDef>Image</th>
          <td mat-cell *matCellDef="let price">
            <div *ngIf="price.crop_image_url" class="price-image-container">
              <img 
                [src]="getFullImageUrl(price.crop_image_url)" 
                alt="{{price.crop_name}}" 
                class="price-thumbnail" 
                (click)="openImageDialog(getFullImageUrl(price.crop_image_url))"
                (error)="handleImageError($event)"
              >
            </div>
            <span *ngIf="!price.crop_image_url" class="no-image">
              <mat-icon>image_not_supported</mat-icon>
            </span>
          </td>
        </ng-container>

        <!-- Price Date Column -->
        <ng-container matColumnDef="price_date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
          <td mat-cell *matCellDef="let price">
            <div class="date-display">
              <span class="date-main">{{price.price_date | date:'MMM d, yyyy'}}</span>
              <span class="date-relative" *ngIf="getDaysAgo(price.price_date) <= 30">
                {{getDaysAgo(price.price_date) === 0 ? 'Today' : 
                  getDaysAgo(price.price_date) === 1 ? 'Yesterday' : 
                  getDaysAgo(price.price_date) + ' days ago'}}
              </span>
            </div>
          </td>
        </ng-container>

        <!-- Crop Name Column -->
        <ng-container matColumnDef="crop_name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Product</th>
          <td mat-cell *matCellDef="let price">
            <div class="crop-name-display">
              <span class="crop-name">{{price.crop_name}}</span>
              <span class="crop-info" *ngIf="price.priceCount && price.priceCount > 1" 
                    matTooltip="{{price.priceCount}} price records in database">
                <mat-icon class="info-icon">info</mat-icon>
              </span>
            </div>
          </td>
        </ng-container>

        <!-- Price Column -->
        <ng-container matColumnDef="price_per_unit">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Price</th>
          <td mat-cell *matCellDef="let price">
            <div class="price-display">
              <span class="price-value">₹{{price.price_per_unit | number:'1.2-2'}}</span>
              <span *ngIf="price.priceTrend > 0" class="trend-up" 
                    matTooltip="Price increased by {{price.priceTrend | number:'1.2-2'}}% from previous">
                <mat-icon>trending_up</mat-icon>
                <small>+{{price.priceTrend | number:'1.2-2'}}%</small>
              </span>
              <span *ngIf="price.priceTrend < 0" class="trend-down" 
                    matTooltip="Price decreased by {{price.priceTrend * -1 | number:'1.2-2'}}% from previous">
                <mat-icon>trending_down</mat-icon>
                <small>{{price.priceTrend | number:'1.2-2'}}%</small>
              </span>
              <span *ngIf="price.priceTrend === 0 && price.previousPrice" class="trend-stable" 
                    matTooltip="Price unchanged from previous">
                <mat-icon>trending_flat</mat-icon>
              </span>
            </div>
          </td>
        </ng-container>

        <!-- Unit Type Column -->
        <ng-container matColumnDef="unit_type">
          <th mat-header-cell *matHeaderCellDef>Unit</th>
          <td mat-cell *matCellDef="let price">{{price.unit_type}}</td>
        </ng-container>

        <!-- Notes Column -->
        <ng-container matColumnDef="notes">
          <th mat-header-cell *matHeaderCellDef>Notes</th>
          <td mat-cell *matCellDef="let price">
            <div *ngIf="price.notes" class="notes-content" [matTooltip]="price.notes">
              {{price.notes}}
            </div>
            <span *ngIf="!price.notes" class="no-notes">-</span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let price">
            <button mat-icon-button color="primary" [routerLink]="['/marketplace/market-prices/edit-market-price', price.id]" matTooltip="Edit">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button color="accent" (click)="viewPriceHistory(price.product_id)" matTooltip="View History">
              <mat-icon>history</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deletePrice(price.id)" matTooltip="Delete">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        <!-- No Data Row -->
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="7">
            <div class="no-data">
              <mat-icon>search_off</mat-icon>
              <span>No market prices found</span>
            </div>
          </td>
        </tr>
      </table>

      <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
    </div>
  </mat-card>
</div>
// src/app/features/marketplace/market-prices/list-market-prices.component.ts
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import {
  MarketPriceService,
  MarketPrice,
} from '../../../../core/auth/services/market-price.service';
import { CropService } from '../../../../core/auth/services/masters/crop.service';
import { saveAs } from 'file-saver';
import { ImageViewerComponent } from '../../../../components/image-viewer.component';
import { environment } from '../../../../../environments/environment';
import { PriceHistoryComponent } from '../../../../components/price-history.component';

// Extended MarketPrice interface with trend information
interface EnhancedMarketPrice extends MarketPrice {
  priceTrend?: number;
  previousPrice?: number;
  priceCount?: number;
}

// Utility function for date conversion to IST
function toISTDateString(date: Date): string {
  // Add 5 hours and 30 minutes to get IST
  const istDate = new Date(date.getTime() + 5.5 * 60 * 60 * 1000);
  return istDate.toISOString().split('T')[0];
}

@Component({
  selector: 'app-list-market-prices',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSelectModule,
    MatSnackBarModule,
    MatCardModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDialogModule,
  ],
  templateUrl: './list-market-prices.component.html',
  styleUrls: ['./list-market-prices.component.scss'],
})
export class ListMarketPricesComponent implements OnInit {
  displayedColumns: string[] = [
    'image_url',
    'price_date',
    'crop_name',
    'price_per_unit',
    'unit_type',
    'notes',
    'actions',
  ];

  dataSource = new MatTableDataSource<EnhancedMarketPrice>([]);
  isLoading = false;
  filterForm: FormGroup;
  crops: any[] = [];

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private marketPriceService: MarketPriceService,
    private cropService: CropService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.filterForm = this.fb.group({
      product_id: [''],
      date_from: [''],
      date_to: [''],
    });
  }

  ngOnInit(): void {
    this.loadCrops();
    this.loadMarketPricesWithTrend();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadCrops(): void {
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.crops = response.data.filter((crop: any) => crop.is_active);
        }
      },
      error: (error) => {
        this.snackBar.open('Error loading crops', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
      },
    });
  }

  showTodaysPrices(): void {
    // Get today's date in IST
    const now = new Date();
    const today = toISTDateString(now);

    // Update the filter form
    this.filterForm.patchValue({
      date_from: today,
      date_to: today,
    });

    // Apply the filter
    this.applyFilter();

    // Show feedback to the user
    this.snackBar.open('Showing prices for today', 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  handleImageError(event: any): void {
    // Replace broken image with a placeholder
    event.target.src = 'assets/images/placeholder-image.png';

    // Log the error for debugging
    console.error('Image failed to load:', event.target.src);
  }
  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return 'assets/images/placeholder-image.png';
    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    // Otherwise, prepend the API URL
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  openImageDialog(imageUrl: string): void {
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl: imageUrl },
      width: '80%',
      maxWidth: '1000px',
    });
  }

  groupByProductId(prices: MarketPrice[]): { [key: string]: MarketPrice[] } {
    return prices.reduce((acc, price) => {
      const productId = price.product_id.toString();
      if (!acc[productId]) {
        acc[productId] = [];
      }
      acc[productId].push(price);
      return acc;
    }, {} as { [key: string]: MarketPrice[] });
  }

  loadMarketPricesWithTrend(): void {
    this.isLoading = true;
    const filters = this.filterForm.value;

    // Convert dates to ISO format if they exist
    if (filters.date_from instanceof Date) {
      filters.date_from = toISTDateString(filters.date_from);
    }
    if (filters.date_to instanceof Date) {
      filters.date_to = toISTDateString(filters.date_to);
    }

    // First get all market prices
    this.marketPriceService.getAllPrices(filters).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          // Group by product_id to find trends
          const productGroups = this.groupByProductId(response.data);
          const enhancedPrices: EnhancedMarketPrice[] = [];

          // Calculate trends for each product
          Object.keys(productGroups).forEach((productId) => {
            const prices = productGroups[productId];
            // Sort by date, newest first
            prices.sort(
              (a, b) =>
                new Date(b.price_date).getTime() -
                new Date(a.price_date).getTime()
            );

            // Add trend information
            for (let i = 0; i < prices.length; i++) {
              const price = { ...prices[i] } as EnhancedMarketPrice;
              price.priceCount = prices.length;

              if (i < prices.length - 1) {
                const currentPrice = parseFloat(
                  price.price_per_unit.toString()
                );
                const previousPrice = parseFloat(
                  prices[i + 1].price_per_unit.toString()
                );

                // Add the previousPrice property to the object before using it
                price.previousPrice = previousPrice;

                // Calculate percentage change
                if (previousPrice > 0) {
                  const change =
                    ((currentPrice - previousPrice) / previousPrice) * 100;
                  price.priceTrend = parseFloat(change.toFixed(2));
                } else {
                  price.priceTrend = 0;
                }
              } else {
                price.priceTrend = 0; // No previous price to compare
                price.previousPrice = undefined; // Set it explicitly as undefined
              }

              enhancedPrices.push(price);
            }
          });

          // Sort back to original order if needed
          this.dataSource.data = enhancedPrices.sort(
            (a, b) =>
              new Date(b.price_date).getTime() -
              new Date(a.price_date).getTime()
          );
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error loading market prices', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    this.loadMarketPricesWithTrend();
  }

  resetFilter(): void {
    this.filterForm.reset();
    this.loadMarketPricesWithTrend();
  }

  deletePrice(id: number): void {
    if (confirm('Are you sure you want to delete this market price?')) {
      this.marketPriceService.deletePrice(id).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open('Market price deleted successfully', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
            this.loadMarketPricesWithTrend();
          }
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || 'Error deleting market price',
            'Close',
            {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            }
          );
        },
      });
    }
  }

  downloadTemplate(): void {
    this.marketPriceService.downloadPriceTemplate().subscribe({
      next: (blob) => {
        saveAs(blob, 'market_prices_template.xlsx');
      },
      error: (error) => {
        this.snackBar.open('Error downloading template', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
      },
    });
  }

  exportPrices(): void {
    const filters = this.filterForm.value;

    // Convert dates to ISO format if they exist
    if (filters.date_from instanceof Date) {
      filters.date_from = toISTDateString(filters.date_from);
    }
    if (filters.date_to instanceof Date) {
      filters.date_to = toISTDateString(filters.date_to);
    }

    this.marketPriceService.exportPrices(filters).subscribe({
      next: (blob) => {
        saveAs(
          blob,
          `market_prices_${new Date().toISOString().slice(0, 10)}.xlsx`
        );
      },
      error: (error) => {
        this.snackBar.open('Error exporting market prices', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
      },
    });
  }

  uploadPriceSheet(event: Event): void {
    const element = event.target as HTMLInputElement;
    const file = element.files?.[0];

    if (!file) return;

    this.isLoading = true;
    this.marketPriceService.uploadPriceSheet(file).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.snackBar.open(
            response.message || 'Prices uploaded successfully',
            'Close',
            {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            }
          );
          this.loadMarketPricesWithTrend();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open(
          error.error?.message || 'Error uploading prices',
          'Close',
          {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          }
        );
        this.isLoading = false;
      },
    });

    // Clear the input
    element.value = '';
  }

  getDaysAgo(dateString: string): number {
    const date = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const diffTime = today.getTime() - date.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  }

  viewPriceHistory(productId: number): void {
    // Create a dialog to show price history for this product
    this.dialog.open(PriceHistoryComponent, {
      data: { productId: productId },
      width: '800px',
      maxWidth: '90vw',
    });
  }
}

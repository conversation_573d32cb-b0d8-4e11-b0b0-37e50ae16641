// src/app/features/marketplace/market-prices/list-market-prices.component.scss
.market-prices-container {
  padding: 24px;
  
  mat-card {
    overflow: hidden;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      
      button {
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
  
  .filter-container {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    
    .filter-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;
      
      mat-form-field {
        flex: 1;
        min-width: 200px;
      }
      
      .filter-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }
  
  .table-container {
    position: relative;
    min-height: 400px;
    
    .loading-shade {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.7);
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .prices-table {
      width: 100%;
      
      .mat-column-actions {
        width: 150px;
        text-align: center;
      }
      
      .mat-column-image_url {
        width: 60px;
        text-align: center;
      }
      
      .mat-mdc-row:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
    
    .price-image-container {
      display: flex;
      justify-content: center;
      padding: 5px;
    }
    
    .price-thumbnail {
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 4px;
      cursor: pointer;
      transition: transform 0.2s;
      
      &:hover {
        transform: scale(1.1);
      }
    }
    
    .no-image {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
      background-color: #f0f0f0;
      border-radius: 4px;
      margin: 0 auto;
      
      mat-icon {
        font-size: 20px;
        color: #bdbdbd;
      }
    }
    
    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      color: rgba(0, 0, 0, 0.54);
      
      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        margin-bottom: 16px;
      }
    }
    
    .price-display {
      display: flex;
      align-items: center;
      
      .price-value {
        font-weight: 500;
        margin-right: 8px;
      }
      
      .trend-up {
        color: #4caf50;
        display: flex;
        align-items: center;
        font-size: 12px;
        
        mat-icon {
          font-size: 16px;
          height: 16px;
          width: 16px;
          margin-right: 2px;
        }
      }
      
      .trend-down {
        color: #f44336;
        display: flex;
        align-items: center;
        font-size: 12px;
        
        mat-icon {
          font-size: 16px;
          height: 16px;
          width: 16px;
          margin-right: 2px;
        }
      }
      
      .trend-stable {
        color: #757575;
        display: flex;
        align-items: center;
        font-size: 12px;
        
        mat-icon {
          font-size: 16px;
          height: 16px;
          width: 16px;
        }
      }
    }
    
    .date-display {
      display: flex;
      flex-direction: column;
      
      .date-main {
        font-weight: 500;
      }
      
      .date-relative {
        font-size: 12px;
        color: #757575;
      }
    }
    
    .notes-content {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .crop-name-display {
      display: flex;
      align-items: center;
      
      .crop-name {
        margin-right: 8px;
      }
      
      .crop-info {
        display: flex;
        align-items: center;
        
        .info-icon {
          font-size: 16px;
          height: 16px;
          width: 16px;
          color: #1976d2;
        }
      }
    }
  }
}

// Make table layout more compact
::ng-deep {
  .mat-mdc-table {
    .mat-mdc-row, .mat-mdc-header-row {
      height: 48px; // Reduced row height
    }
    
    .mat-mdc-cell, .mat-mdc-header-cell {
      padding: 0 12px; // Reduced horizontal padding
    }
  }
  
  // Ensure proper alignment for paginator
  .mat-mdc-paginator {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .market-prices-container {
    padding: 16px 8px;
    
    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      .header-actions {
        width: 100%;
        flex-wrap: wrap;
      }
    }
    
    .filter-container {
      padding: 16px;
      
      .filter-row {
        flex-direction: column;
        align-items: stretch;
        
        .filter-buttons {
          margin-top: 8px;
        }
      }
    }
    
    .price-thumbnail {
      width: 30px;
      height: 30px;
    }
    
    .mat-column-image_url {
      width: 40px;
    }
  }

  .price-image-container {
    display: flex;
    justify-content: center;
    padding: 5px;
  }
  
  .price-thumbnail {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
    
    &:hover {
      transform: scale(1.1);
    }
  }
  
  .no-image {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin: 0 auto;
    
    mat-icon {
      font-size: 20px;
      color: #bdbdbd;
    }
  }
}
// src/app/features/marketplace/interests/interest-detail-dialog/interest-detail-dialog.component.ts
import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
  MatDialog,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { environment } from '../../../../../environments/environment';
import { ImageViewerComponent } from '../../../../components/image-viewer.component';
import { InterestService } from '../../../../core/auth/services/interest.service';

@Component({
  selector: 'app-interest-detail-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule,
    MatChipsModule,
    MatTooltipModule,
    MatMenuModule,
    MatSnackBarModule,
  ],
  templateUrl: './interest-detail-dialog.component.html',
  styleUrls: ['./interest-detail-dialog.component.scss'],
})
export class InterestDetailDialogComponent {
  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    public dialogRef: MatDialogRef<InterestDetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { interest: any },
    private interestService: InterestService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  close(): void {
    this.dialogRef.close();
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    // Otherwise, prepend the API URL
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    // Replace broken image with a placeholder
    event.target.src = 'assets/images/placeholder-image.png';
  }

  viewImage(imageUrl: string): void {
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl: this.getFullImageUrl(imageUrl) },
    });
  }

  viewFarmerProfile(farmerId: number): void {
    // Navigate to farmer profile or open in dialog
    window.open(`/farmers/view/${farmerId}`, '_blank');
  }

  viewRequirementDetails(requirementId: number): void {
    if (requirementId) {
      window.open(
        `/marketplace/requirements/view-requirement/${requirementId}`,
        '_blank'
      );
    } else {
      this.snackBar.open('Requirement ID is not available', 'Close', {
        duration: 3000,
      });
    }
  }

  updateStatus(newStatus: string): void {
    this.interestService
      .updateInterestStatus(this.data.interest.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open(`Status updated to ${newStatus}`, 'Close', {
              duration: 3000,
            });
            // Update the local data
            this.data.interest.status = newStatus;
            // Optional: Close the dialog
            // this.dialogRef.close({ updated: true });
          }
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || 'Error updating status',
            'Close',
            { duration: 3000 }
          );
        },
      });
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'accepted':
        return 'status-accepted';
      case 'in_progress':
        return 'status-in-progress';
      case 'delivered':
        return 'status-delivered';
      case 'closed':
        return 'status-closed';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  getRequirementStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'status-active';
      case 'fulfilled':
        return 'status-fulfilled';
      case 'expired':
        return 'status-expired';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }
}

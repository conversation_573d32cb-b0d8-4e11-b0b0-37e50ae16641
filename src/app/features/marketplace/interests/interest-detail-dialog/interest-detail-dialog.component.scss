// src/app/features/marketplace/interests/interest-detail-dialog/interest-detail-dialog.component.scss
.interest-detail-container {
    max-width: 800px;
    
    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 500;
      }
    }
    
    .dialog-content {
      padding: 24px;
      max-height: 70vh;
      overflow-y: auto;
  
      .detail-section {
        margin-bottom: 24px;
        
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          
          h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
            color: #333;
          }
        }
        
        .detail-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 16px;
          
          .detail-item {
            display: flex;
            flex-direction: column;
            
            &.full-width {
              grid-column: 1 / -1;
            }
            
            .label {
              font-size: 12px;
              color: #666;
              margin-bottom: 4px;
            }
            
            .value {
              font-size: 16px;
              
              &.notes {
                white-space: pre-line;
                font-size: 14px;
                padding: 8px;
                background-color: #f9f9f9;
                border-radius: 4px;
                border-left: 3px solid #ccc;
              }
            }
            
            .status-chip {
              padding: 4px 8px;
              border-radius: 16px;
              display: inline-block;
              text-align: center;
              min-width: 80px;
              font-size: 12px;
              font-weight: 500;
              
              &.status-accepted {
                background-color: #e3f2fd;
                color: #1976d2;
              }
              
              &.status-in-progress {
                background-color: #fff8e1;
                color: #ffa000;
              }
              
              &.status-delivered {
                background-color: #e8f5e9;
                color: #388e3c;
              }
              
              &.status-closed {
                background-color: #fafafa;
                color: #757575;
              }
              
              &.status-cancelled {
                background-color: #ffebee;
                color: #d32f2f;
              }
              
              &.status-active {
                background-color: #e3f2fd;
                color: #1976d2;
              }
              
              &.status-fulfilled {
                background-color: #e8f5e9;
                color: #388e3c;
              }
              
              &.status-expired {
                background-color: #f5f5f5;
                color: #757575;
              }
            }
          }
        }
      }
      
      // Images Tab
      .images-tab-content {
        padding: 16px 0;
        
        .image-section {
          h3 {
            margin-bottom: 16px;
            font-size: 18px;
            font-weight: 500;
          }
          
          .image-container {
            width: 100%;
            text-align: center;
            
            img {
              max-width: 100%;
              max-height: 400px;
              object-fit: contain;
              border-radius: 4px;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
              cursor: pointer;
              transition: transform 0.2s;
              
              &:hover {
                transform: scale(1.02);
              }
            }
          }
          
          .image-caption {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin-top: 12px;
          }
        }
        
        .no-images {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 48px 0;
          color: #999;
          
          mat-icon {
            font-size: 48px;
            width: 48px;
            height: 48px;
            margin-bottom: 16px;
          }
        }
      }
      
      // Notes Tab
      .notes-tab-content {
        padding: 16px 0;
        
        .activity-timeline {
          margin-bottom: 32px;
          
          .timeline-item {
            display: flex;
            margin-bottom: 24px;
            position: relative;
            
            &:not(:last-child):after {
              content: '';
              position: absolute;
              left: 15px;
              top: 30px;
              bottom: -20px;
              width: 2px;
              background-color: #e0e0e0;
            }
            
            .timeline-icon {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background-color: #f5f5f5;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 16px;
              z-index: 1;
              
              &.status-icon {
                background-color: #e3f2fd;
                
                mat-icon {
                  color: #1976d2;
                }
              }
              
              mat-icon {
                font-size: 18px;
                color: #757575;
              }
            }
            
            .timeline-content {
              flex: 1;
              
              h4 {
                margin: 0 0 8px;
                font-size: 16px;
                font-weight: 500;
              }
              
              p {
                margin: 0 0 4px;
                color: #757575;
                font-size: 14px;
              }
            }
          }
        }
        
        .admin-notes-section {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 16px;
          
          h3 {
            margin-top: 0;
            margin-bottom: 16px;
            font-size: 18px;
            font-weight: 500;
          }
          
          .admin-notes-content {
            min-height: 100px;
            margin-bottom: 16px;
            padding: 8px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
          }
          
          .admin-notes-actions {
            display: flex;
            justify-content: flex-end;
          }
        }
      }
    }
    
    .dialog-actions {
      display: flex;
      justify-content: flex-end;
      padding: 16px 24px;
      gap: 16px;
    }
  }
  
  // Make tabs more compact for dialog
  ::ng-deep {
    .mat-mdc-tab-header {
      margin-bottom: 16px;
    }
    
    .mat-mdc-tab-body-content {
      padding: 0;
    }
  }
  
  // Responsive adjustments
  @media (max-width: 600px) {
    .interest-detail-container {
      .dialog-content {
        padding: 16px;
        
        .detail-section {
          .detail-grid {
            grid-template-columns: 1fr;
          }
        }
      }
      
      .dialog-actions {
        flex-direction: column-reverse;
        
        button {
          width: 100%;
        }
      }
    }
  }
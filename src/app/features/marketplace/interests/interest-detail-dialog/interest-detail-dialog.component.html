<!-- src/app/features/marketplace/interests/interest-detail-dialog/interest-detail-dialog.component.html -->
<div class="interest-detail-container">
    <div class="dialog-header">
      <h2>Interest Details #{{data.interest.id}}</h2>
      <button mat-icon-button (click)="close()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  
    <mat-divider></mat-divider>
  
    <div class="dialog-content">
      <mat-tab-group animationDuration="0ms">
        <!-- Overview Tab -->
        <mat-tab label="Overview">
          <div class="detail-section">
            <div class="section-header">
              <h3>Interest Information</h3>
            </div>
            
            <div class="detail-grid">
              <div class="detail-item">
                <span class="label">Status</span>
                <span class="value">
                  <span class="status-chip" [ngClass]="getStatusClass(data.interest.status)">
                    {{data.interest.status | titlecase}}
                  </span>
                </span>
              </div>
              
              <div class="detail-item">
                <span class="label">Date Created</span>
                <span class="value">{{data.interest.created_at | date:'medium'}}</span>
              </div>
              
              <div class="detail-item">
                <span class="label">Quantity Offered</span>
                <span class="value">{{data.interest.quantity}} {{data.interest.unit_type}}</span>
              </div>
              
              <div class="detail-item">
                <span class="label">Price per Unit</span>
                <span class="value">{{formatCurrency(data.interest.price_per_unit)}}/{{data.interest.unit_type}}</span>
              </div>
              
              <div class="detail-item">
                <span class="label">Total Amount</span>
                <span class="value">{{formatCurrency(data.interest.total_amount)}}</span>
              </div>
              
              <div class="detail-item full-width" *ngIf="data.interest.notes">
                <span class="label">Notes</span>
                <span class="value notes">{{data.interest.notes}}</span>
              </div>
            </div>
          </div>
          
          <mat-divider></mat-divider>
          
          <div class="detail-section">
            <div class="section-header">
              <h3>Farmer Information</h3>
              <button mat-stroked-button color="primary" (click)="viewFarmerProfile(data.interest.farmer_id)">
                <mat-icon>person</mat-icon>View Profile
              </button>
            </div>
            
            <div class="detail-grid">
              <div class="detail-item">
                <span class="label">Name</span>
                <span class="value">{{data.interest.farmer_name}}</span>
              </div>
              
              <div class="detail-item">
                <span class="label">Farmer ID</span>
                <span class="value">{{data.interest.farmer_id}}</span>
              </div>
              
              <div class="detail-item">
                <span class="label">Contact</span>
                <span class="value">
                  <button mat-stroked-button color="accent">
                    <mat-icon>phone</mat-icon>Contact
                  </button>
                </span>
              </div>
            </div>
          </div>
          
          <mat-divider></mat-divider>
          
          <div class="detail-section">
            <div class="section-header">
              <h3>Requirement Information</h3>
              <button mat-stroked-button color="primary" (click)="viewRequirementDetails(data.interest.requirement_id)">
                <mat-icon>assignment</mat-icon>View Requirement
              </button>
            </div>
            
            <div class="detail-grid">
              <div class="detail-item">
                <span class="label">Requirement ID</span>
                <span class="value">#{{data.interest.requirement_id}}</span>
              </div>
              
              <div class="detail-item">
                <span class="label">Product</span>
                <span class="value">{{data.interest.crop_name}}</span>
              </div>
              
              <div class="detail-item">
                <span class="label">Variety</span>
                <span class="value">{{data.interest.variety || 'Not specified'}}</span>
              </div>
              
              <div class="detail-item">
                <span class="label">Required Quantity</span>
                <span class="value">{{data.interest.requirement_quantity}} {{data.interest.unit_type}}</span>
              </div>
              
              <div class="detail-item">
                <span class="label">Requirement Status</span>
                <span class="value">
                  <span class="status-chip" [ngClass]="getRequirementStatusClass(data.interest.requirement_status)">
                    {{data.interest.requirement_status | titlecase}}
                  </span>
                </span>
              </div>
            </div>
          </div>
        </mat-tab>
        
        <!-- Images Tab -->
        <mat-tab label="Images">
          <div class="images-tab-content">
            <div class="image-section" *ngIf="data.interest.produce_image_url">
              <h3>Produce Images</h3>
              <div class="image-container">
                <img [src]="getFullImageUrl(data.interest.produce_image_url)" 
                    alt="Produce Image" 
                    (click)="viewImage(data.interest.produce_image_url)"
                    (error)="handleImageError($event)">
              </div>
              <p class="image-caption">Primary image of the produce</p>
            </div>
            
            <div class="no-images" *ngIf="!data.interest.produce_image_url">
              <mat-icon>image_not_supported</mat-icon>
              <p>No images available for this interest</p>
            </div>
          </div>
        </mat-tab>
        
        <!-- Notes Tab -->
        <mat-tab label="Notes & Activity">
          <div class="notes-tab-content">
            <div class="activity-timeline">
              <div class="timeline-item">
                <div class="timeline-icon">
                  <mat-icon>add_circle</mat-icon>
                </div>
                <div class="timeline-content">
                  <h4>Interest Created</h4>
                  <p>{{data.interest.created_at | date:'medium'}}</p>
                  <p>Farmer {{data.interest.farmer_name}} expressed interest in requirement #{{data.interest.requirement_id}}</p>
                </div>
              </div>
              
              <!-- Additional status updates would go here -->
              
              <div class="timeline-item">
                <div class="timeline-icon status-icon">
                  <mat-icon>update</mat-icon>
                </div>
                <div class="timeline-content">
                  <h4>Current Status: {{data.interest.status | titlecase}}</h4>
                  <p>Last updated: {{data.interest.created_at | date:'medium'}}</p>
                </div>
              </div>
            </div>
            
            <div class="admin-notes-section">
              <h3>Admin Notes</h3>
              <div class="admin-notes-content">
                <p *ngIf="!data.interest.admin_notes">No admin notes added yet.</p>
                <p *ngIf="data.interest.admin_notes">{{data.interest.admin_notes}}</p>
              </div>
              <div class="admin-notes-actions">
                <button mat-raised-button color="primary">
                  <mat-icon>edit</mat-icon>Add Notes
                </button>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  
    <mat-divider></mat-divider>
    
    <div class="dialog-actions">
      <button mat-stroked-button (click)="close()">Close</button>
      <button mat-raised-button color="primary" [matMenuTriggerFor]="statusMenu" 
              *ngIf="data.interest.status !== 'cancelled' && data.interest.status !== 'closed'">
        Update Status
      </button>
      <mat-menu #statusMenu="matMenu">
        <button mat-menu-item (click)="updateStatus('accepted')" 
               *ngIf="data.interest.status !== 'accepted'">
          <mat-icon>check_circle</mat-icon>
          <span>Accept Interest</span>
        </button>
        <button mat-menu-item (click)="updateStatus('in_progress')" 
               *ngIf="data.interest.status !== 'in_progress'">
          <mat-icon>sync</mat-icon>
          <span>Mark as In Progress</span>
        </button>
        <button mat-menu-item (click)="updateStatus('delivered')" 
               *ngIf="data.interest.status !== 'delivered'">
          <mat-icon>local_shipping</mat-icon>
          <span>Mark as Delivered</span>
        </button>
        <button mat-menu-item (click)="updateStatus('closed')" 
               *ngIf="data.interest.status !== 'closed'">
          <mat-icon>task_alt</mat-icon>
          <span>Close Interest</span>
        </button>
        <button mat-menu-item (click)="updateStatus('cancelled')" 
               *ngIf="data.interest.status !== 'cancelled'">
          <mat-icon>cancel</mat-icon>
          <span>Cancel Interest</span>
        </button>
      </mat-menu>
    </div>
  </div>
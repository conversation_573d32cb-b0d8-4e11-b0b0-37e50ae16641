<!-- src/app/features/marketplace/interests/requirement-interests/requirement-interests.component.html -->
<div class="interests-container">
    <mat-card>
      <div class="card-header">
        <h2>Farmer Interests for Requirements</h2>
        <div class="header-actions">
          <button mat-raised-button color="primary" routerLink="/marketplace/requirements">
            <mat-icon>assignment</mat-icon> View Requirements
          </button>
        </div>
      </div>
  
      <!-- Filter Form -->
      <div class="filter-container">
        <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
          <div class="filter-row">
            <mat-form-field appearance="outline">
              <mat-label>Requirement</mat-label>
              <mat-select formControlName="requirement_id">
                <mat-option [value]="">All Requirements</mat-option>
                <mat-option *ngFor="let req of requirements" [value]="req.id">
                  #{{req.id}} - {{req.crop_name}} {{req.variety ? '(' + req.variety + ')' : ''}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Product</mat-label>
              <mat-select formControlName="product_id">
                <mat-option [value]="">All Products</mat-option>
                <mat-option *ngFor="let crop of crops" [value]="crop.id">
                  {{crop.crop_name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select formControlName="status">
                <mat-option [value]="">All Statuses</mat-option>
                <mat-option *ngFor="let option of statusOptions" [value]="option.value">
                  {{option.label}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Farmer Name</mat-label>
              <input matInput formControlName="farmer_name">
            </mat-form-field>
  
            <div class="filter-buttons">
              <button mat-raised-button color="primary" type="submit">
                <mat-icon>search</mat-icon> Filter
              </button>
              <button mat-stroked-button color="warn" type="button" (click)="resetFilter()">
                <mat-icon>clear</mat-icon> Reset
              </button>
            </div>
          </div>
        </form>
      </div>
  
      <!-- Interests Table -->
      <div class="table-container">
        <div *ngIf="isLoading" class="loading-shade">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
  
        <table mat-table [dataSource]="dataSource" matSort class="interests-table">
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
            <td mat-cell *matCellDef="let interest"> {{interest.id}} </td>
          </ng-container>
  
          <!-- Farmer Info Column -->
          <ng-container matColumnDef="farmer_info">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Farmer </th>
            <td mat-cell *matCellDef="let interest">
                <div class="farmer-info">
                    <div class="farmer-name">{{interest.farmer_name}}</div>
                    <div class="farmer-actions">
                      <button mat-icon-button color="primary" matTooltip="View Farmer Profile" (click)="viewFarmerProfile(interest.farmer_id)">
                        <mat-icon>person</mat-icon>
                      </button>
                      <button mat-icon-button color="accent" matTooltip="Contact Farmer" (click)="contactFarmer(interest.farmer_id, interest.farmer_name)">
                        <mat-icon>message</mat-icon>
                      </button>
                    </div>
                  </div>
                  
            </td>
          </ng-container>
  
          <!-- Crop Details Column -->
          <ng-container matColumnDef="crop_details">
            <th mat-header-cell *matHeaderCellDef> Crop / Requirement </th>
            <td mat-cell *matCellDef="let interest">
                <div class="crop-details">
                    <div class="crop-image-container" *ngIf="interest.produce_image_url">
                      <img [src]="getFullImageUrl(interest.produce_image_url)" 
                          (click)="viewProduceImage(interest.produce_image_url)" 
                          class="crop-thumbnail" 
                          alt="{{interest.crop_name}}"
                          (error)="handleImageError($event)">
                    </div>
                    <div class="crop-info">
                      <div class="crop-name">{{interest.crop_name}}</div>
                      <div class="crop-variety" *ngIf="interest.variety">{{interest.variety}}</div>
                      <div class="requirement-link">
                        <a mat-button color="primary" (click)="viewRequirementDetails(interest.fpo_requirement_id)">
                          <small>Requirement #{{interest.fpo_requirement_id}}</small>
                        </a>
                      </div>
                    </div>
                </div>
            </td>
          </ng-container>
  
          <!-- Quantity Column -->
          <ng-container matColumnDef="quantity">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Quantity </th>
            <td mat-cell *matCellDef="let interest">
              <div class="quantity-info">
                <div class="quantity-offered">{{interest.quantity}} {{interest.unit_type}}</div>
                <div class="quantity-required">
                  <small>Req: {{interest.requirement_quantity}} {{interest.unit_type}}</small>
                </div>
              </div>
            </td>
          </ng-container>
  
          <!-- Price Column -->
          <ng-container matColumnDef="price">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Price </th>
            <td mat-cell *matCellDef="let interest">
              <div class="price-info">
                <div class="price-per-unit">{{formatCurrency(interest.price_per_unit)}}/{{interest.unit_type}}</div>
                <div class="total-amount">
                  <small>Total: {{formatCurrency(interest.total_amount)}}</small>
                </div>
              </div>
            </td>
          </ng-container>
  
          <!-- Created At Column -->
          <ng-container matColumnDef="created_at">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Date </th>
            <td mat-cell *matCellDef="let interest"> {{interest.created_at | date}} </td>
          </ng-container>
          
          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td mat-cell *matCellDef="let interest">
              <div class="status-chip" [ngClass]="getStatusClass(interest.status)">
                {{interest.status | titlecase}}
              </div>
            </td>
          </ng-container>
  
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let interest">
              <button mat-icon-button color="primary" 
                      (click)="openDetailDialog(interest)" 
                      matTooltip="View Details">
                <mat-icon>visibility</mat-icon>
              </button>
              
              <button mat-icon-button [matMenuTriggerFor]="statusMenu" 
                      matTooltip="Change Status"
                      *ngIf="interest.status !== 'cancelled' && interest.status !== 'closed'">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #statusMenu="matMenu">
                <button mat-menu-item (click)="updateInterestStatus(interest.id, 'accepted')" 
                       *ngIf="interest.status !== 'accepted'">
                  <mat-icon>check_circle</mat-icon>
                  <span>Accept Interest</span>
                </button>
                <button mat-menu-item (click)="updateInterestStatus(interest.id, 'in_progress')" 
                       *ngIf="interest.status !== 'in_progress'">
                  <mat-icon>sync</mat-icon>
                  <span>Mark as In Progress</span>
                </button>
                <button mat-menu-item (click)="updateInterestStatus(interest.id, 'delivered')" 
                       *ngIf="interest.status !== 'delivered'">
                  <mat-icon>local_shipping</mat-icon>
                  <span>Mark as Delivered</span>
                </button>
                <button mat-menu-item (click)="updateInterestStatus(interest.id, 'closed')" 
                       *ngIf="interest.status !== 'closed'">
                  <mat-icon>task_alt</mat-icon>
                  <span>Close Interest</span>
                </button>
                <button mat-menu-item (click)="updateInterestStatus(interest.id, 'cancelled')" 
                       *ngIf="interest.status !== 'cancelled'">
                  <mat-icon>cancel</mat-icon>
                  <span>Cancel Interest</span>
                </button>
              </mat-menu>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  
          <!-- No Data Row -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="8">
              <div class="no-data">
                <mat-icon>search_off</mat-icon>
                <span>No farmer interests found</span>
              </div>
            </td>
          </tr>
        </table>
  
        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </div>
    </mat-card>
  </div>
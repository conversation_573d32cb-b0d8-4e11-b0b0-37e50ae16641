// src/app/features/marketplace/interests/requirement-interests/requirement-interests.component.ts
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { InterestService } from '../../../../core/auth/services/interest.service';
import { RequirementService } from '../../../../core/auth/services/requirement.service';
import { CropService } from '../../../../core/auth/services/masters/crop.service';
import { FarmerService } from '../../../../core/auth/services/farmer.service';
import { environment } from '../../../../../environments/environment';
import { ImageViewerComponent } from '../../../../components/image-viewer.component';

import { InterestDetailDialogComponent } from '../interest-detail-dialog/interest-detail-dialog.component';

interface FarmerInterest {
  id: number;
  fpo_requirement_id: number; // Changed from requirement_id
  farmer_id: number;
  produce_id: number;
  quantity: number;
  unit_type: string;
  price_per_unit: number;
  total_amount: number;
  status: string;
  notes?: string;
  created_at: string;
  farmer_name: string;
  crop_name: string;
  crop_image_url?: string;
  produce_image_url?: string;
  variety?: string;
  requirement_quantity: number;
  requirement_status: string;
}

@Component({
  selector: 'app-requirement-interests',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatSnackBarModule,
    MatCardModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatMenuModule,
    MatDialogModule,
  ],
  templateUrl: './requirement-interests.component.html',
  styleUrls: ['./requirement-interests.component.scss'],
})
export class RequirementInterestsComponent implements OnInit {
  displayedColumns: string[] = [
    'id',
    'farmer_info',
    'crop_details',
    'quantity',
    'price',
    'created_at',
    'status',
    'actions',
  ];

  dataSource = new MatTableDataSource<FarmerInterest>([]);
  isLoading = false;
  filterForm: FormGroup;
  crops: any[] = [];
  requirements: any[] = [];
  statusOptions = [
    { value: 'accepted', label: 'Accepted' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'closed', label: 'Closed' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private interestService: InterestService,
    private requirementService: RequirementService,
    private cropService: CropService,
    private farmerService: FarmerService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.filterForm = this.fb.group({
      requirement_id: [''],
      product_id: [''],
      status: [''],
      farmer_name: [''],
    });
  }

  ngOnInit(): void {
    this.loadCrops();
    this.loadRequirements();
    this.loadInterests();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadCrops(): void {
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.crops = response.data.filter((crop: any) => crop.is_active);
        }
      },
      error: (error) => {
        this.snackBar.open('Error loading crops', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
      },
    });
  }

  loadRequirements(): void {
    this.requirementService.getAllRequirements({}).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.requirements = response.data;
        }
      },
      error: (error) => {
        this.snackBar.open('Error loading requirements', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
      },
    });
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    // Otherwise, prepend the API URL
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    // Replace broken image with a placeholder
    event.target.src = 'assets/images/placeholder-image.png';
  }

  loadInterests(): void {
    this.isLoading = true;
    const filters = this.filterForm.value;

    this.interestService.getAllInterests(filters).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.dataSource.data = response.data;
        } else {
          this.dataSource.data = [];
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error loading interests', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    this.loadInterests();
  }

  resetFilter(): void {
    this.filterForm.reset();
    this.loadInterests();
  }

  updateInterestStatus(interestId: number, newStatus: string): void {
    this.interestService.updateInterestStatus(interestId, newStatus).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.snackBar.open(`Status updated to ${newStatus}`, 'Close', {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          });
          this.loadInterests();
        }
      },
      error: (error) => {
        this.snackBar.open(
          error.error?.message || 'Error updating status',
          'Close',
          {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          }
        );
      },
    });
  }

  openDetailDialog(interest: FarmerInterest): void {
    this.dialog.open(InterestDetailDialogComponent, {
      width: '800px',
      data: { interest: interest },
    });
  }

  viewProduceImage(imageUrl: string): void {
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl: this.getFullImageUrl(imageUrl) },
    });
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'accepted':
        return 'status-accepted';
      case 'in_progress':
        return 'status-in-progress';
      case 'delivered':
        return 'status-delivered';
      case 'closed':
        return 'status-closed';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  viewFarmerProfile(farmerId: number): void {
    if (farmerId) {
      // Use absolute path with current base URL
      const baseUrl = window.location.origin;
      window.open(`${baseUrl}/#/farmers/view/${farmerId}`, '_blank');
    } else {
      this.snackBar.open('Farmer ID is not available', 'Close', {
        duration: 3000,
      });
    }
  }
  viewRequirementDetails(requirementId: number): void {
    // Check if requirementId is valid before navigating
    if (requirementId) {
      const baseUrl = window.location.origin;

      window.open(
        `${baseUrl}/#/marketplace/requirements/view-requirement/${requirementId}`,
        '_blank'
      );
    } else {
      this.snackBar.open('Requirement ID is not available', 'Close', {
        duration: 3000,
        horizontalPosition: 'end',
        verticalPosition: 'top',
      });
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  contactFarmer(farmerId: number, farmerName: string): void {
    // Implementation for contacting the farmer
    this.snackBar.open(`Contacting ${farmerName}...`, 'Close', {
      duration: 2000,
    });
    // Could open a dialog with contact options
  }
}

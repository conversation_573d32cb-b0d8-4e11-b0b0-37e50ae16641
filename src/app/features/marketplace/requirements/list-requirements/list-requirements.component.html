<div class="requirements-container">
  <!-- Error notification -->
  <div class="error-notification" *ngIf="errorMessage">
    <span>{{errorMessage}}</span>
    <button mat-button (click)="dismissError()">Close</button>
  </div>

  <mat-card>
    <!-- Card Header with Stats Summary -->
    <div class="dashboard-header">
      <div class="title-section">
        <h2>FPO Requirements</h2>
      </div>
      <div class="stats-section" *ngIf="requirementStats">
        <div class="stat-item">
          <span class="stat-label">Active</span>
          <span class="stat-value">{{requirementStats.active || 0}}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Fulfilled</span>
          <span class="stat-value">{{requirementStats.fulfilled || 0}}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Total Value</span>
          <span class="stat-value">₹{{requirementStats.totalValue | number:'1.2-2'}}</span>
        </div>
      </div>
      <div class="action-buttons">
        <button mat-raised-button color="accent" (click)="exportRequirements()">
          <mat-icon>download</mat-icon> Export Data
        </button>
        <button mat-raised-button color="primary" routerLink="/marketplace/requirements/add-requirement">
          <mat-icon>add</mat-icon> Add New Requirement
        </button>
      </div>
    </div>

    <!-- Filter Form -->
    <div class="filter-container">
      <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
        <div class="filter-controls">
          <mat-form-field appearance="outline">
            <mat-label>Product</mat-label>
            <mat-select formControlName="product_id">
              <mat-option [value]="">All Products</mat-option>
              <mat-option *ngFor="let crop of crops" [value]="crop.id">
                {{crop.crop_name}}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Status</mat-label>
            <mat-select formControlName="status">
              <mat-option [value]="">All Statuses</mat-option>
              <mat-option *ngFor="let option of statusOptions" [value]="option.value">
                {{option.label}}
              </mat-option>
            </mat-select>
          </mat-form-field>
          
          <mat-form-field appearance="outline">
            <mat-label>Required By (From)</mat-label>
            <input matInput [matDatepicker]="fromPicker" formControlName="required_by_from">
            <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
            <mat-datepicker #fromPicker></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Required By (To)</mat-label>
            <input matInput [matDatepicker]="toPicker" formControlName="required_by_to">
            <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
            <mat-datepicker #toPicker></mat-datepicker>
          </mat-form-field>
        </div>

        <div class="filter-actions">
          <button mat-raised-button color="primary" type="submit">
            <mat-icon>search</mat-icon> Filter
          </button>
          <button mat-stroked-button color="warn" type="button" (click)="resetFilter()">
            <mat-icon>clear</mat-icon> Reset
          </button>
        </div>
      </form>
    </div>

    <!-- Requirements Table -->
    <div class="table-container">
      <div *ngIf="isLoading" class="loading-shade">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <table mat-table [dataSource]="dataSource" matSort class="requirements-table">
        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()"
                          [aria-label]="checkboxLabel()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                          (change)="$event ? selection.toggle(row) : null"
                          [checked]="selection.isSelected(row)"
                          [aria-label]="checkboxLabel(row)"
                          [disabled]="row.status !== 'active'">
            </mat-checkbox>
          </td>
        </ng-container>
        
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
          <td mat-cell *matCellDef="let requirement"> {{requirement.id}} </td>
        </ng-container>

        <!-- Image Column -->
        <ng-container matColumnDef="image">
          <th mat-header-cell *matHeaderCellDef>Image</th>
          <td mat-cell *matCellDef="let requirement">
            <div class="crop-image-container">
              <img *ngIf="requirement.crop_image_url" 
                [src]="getFullImageUrl(requirement.crop_image_url)" 
                alt="{{ requirement.crop_name }}" 
                class="crop-thumbnail"
                (click)="openImageDialog(getFullImageUrl(requirement.crop_image_url))"
                (error)="handleImageError($event)">
              <div *ngIf="!requirement.crop_image_url" class="no-image">
                <mat-icon>image_not_supported</mat-icon>
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Product Name Column -->
        <ng-container matColumnDef="product_name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Product </th>
          <td mat-cell *matCellDef="let requirement"> {{requirement.crop_name}} 
            <span *ngIf="requirement.variety" class="variety-text">({{requirement.variety}})</span>
          </td>
        </ng-container>

        <!-- Quantity Column -->
        <ng-container matColumnDef="quantity">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Quantity </th>
          <td mat-cell *matCellDef="let requirement"> {{requirement.quantity}} </td>
        </ng-container>

        <!-- Unit Type Column -->
        <ng-container matColumnDef="unit_type">
          <th mat-header-cell *matHeaderCellDef> Unit </th>
          <td mat-cell *matCellDef="let requirement"> {{requirement.unit_type}} </td>
        </ng-container>

        <!-- Price Offered Column -->
        <ng-container matColumnDef="price_offered">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Price Offered </th>
          <td mat-cell *matCellDef="let requirement"> ₹{{requirement.price_offered | number:'1.2-2'}} </td>
        </ng-container>

        <!-- Market Price Column -->
        <ng-container matColumnDef="market_price">
          <th mat-header-cell *matHeaderCellDef>Market Price</th>
          <td mat-cell *matCellDef="let requirement">
            <ng-container *ngIf="requirement.market_price !== null">
              ₹{{requirement.market_price | number:'1.2-2'}} / {{requirement.market_price_unit || requirement.unit_type}}
              <div class="price-difference" 
                   [ngClass]="{'negative': requirement.price_difference < 0, 'positive': requirement.price_difference > 0}">
                <mat-icon class="trend-icon">
                  {{requirement.price_difference > 0 ? 'arrow_upward' : 'arrow_downward'}}
                </mat-icon>
                {{requirement.price_difference | number:'1.2-2'}}
              </div>
              <small *ngIf="requirement.is_historical_price" class="historical-note">
                (at time of {{requirement.status}})
              </small>
            </ng-container>
            <span *ngIf="requirement.market_price === null">Not available</span>
          </td>
        </ng-container>
        
        <!-- Matches Column -->
        <ng-container matColumnDef="matches">
          <th mat-header-cell *matHeaderCellDef>Matches</th>
          <td mat-cell *matCellDef="let requirement">
            <div class="matches-count">
              <ng-container *ngIf="requirement.availableProduceCount !== null; else checkingMatches">
                <span class="count">{{requirement.availableProduceCount || 0}}</span>
                <span class="label">matches</span>
              </ng-container>
              <ng-template #checkingMatches>
                <mat-spinner diameter="24"></mat-spinner>
              </ng-template>
            </div>
            
            <button mat-icon-button color="primary" *ngIf="requirement.status === 'active'"
                    (click)="checkMatchingProduce(requirement.id)"
                    matTooltip="Check Matching Produce"
                    [disabled]="requirement.availableProduceCount === null">
              <mat-icon>search</mat-icon>
            </button>
          </td>
        </ng-container>
        
        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
          <td mat-cell *matCellDef="let requirement">
            <div class="status-chip" [ngClass]="getStatusClass(requirement.status)">
              {{requirement.status | titlecase}}
            </div>
          </td>
        </ng-container>

        <!-- Required By Column -->
        <ng-container matColumnDef="required_by">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Required By </th>
          <td mat-cell *matCellDef="let requirement"> 
            {{requirement.required_by ? (requirement.required_by | date) : 'No deadline'}} 
            <span *ngIf="requirement.required_by && isDueSoon(requirement.required_by)" class="due-tag">
              Due Soon
            </span>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let requirement">
            <button mat-icon-button color="primary" 
                    [routerLink]="['/marketplace/requirements/view-requirement', requirement.id]" 
                    matTooltip="View Details">
              <mat-icon>visibility</mat-icon>
            </button>
            <button mat-icon-button color="primary" 
                    [routerLink]="['/marketplace/requirements/edit-requirement', requirement.id]" 
                    matTooltip="Edit"
                    *ngIf="requirement.status === 'active'">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button [matMenuTriggerFor]="statusMenu" 
                    matTooltip="More Options"
                    *ngIf="requirement.status === 'active'">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #statusMenu="matMenu">
              <button mat-menu-item (click)="updateStatus(requirement.id, 'fulfilled')" 
                     *ngIf="requirement.status !== 'fulfilled'">
                <mat-icon>check_circle</mat-icon>
                <span>Mark as Fulfilled</span>
              </button>
              <button mat-menu-item (click)="updateStatus(requirement.id, 'expired')" 
                     *ngIf="requirement.status !== 'expired'">
                <mat-icon>schedule</mat-icon>
                <span>Mark as Expired</span>
              </button>
              <button mat-menu-item (click)="updateStatus(requirement.id, 'cancelled')" 
                     *ngIf="requirement.status !== 'cancelled'">
                <mat-icon>cancel</mat-icon>
                <span>Cancel Requirement</span>
              </button>
              <button mat-menu-item (click)="checkMatchingProduce(requirement.id)">
                <mat-icon>search</mat-icon>
                <span>Check Matching Produce</span>
              </button>
            </mat-menu>
            <button mat-icon-button color="warn" 
                    (click)="deleteRequirement(requirement.id)" 
                    matTooltip="Delete"
                    *ngIf="requirement.status === 'active'">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
            [ngClass]="{'active-row': row.status === 'active'}"></tr>

        <!-- No Data Row -->
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="12">
            <div class="no-data">
              <mat-icon>search_off</mat-icon>
              <span>No requirements found</span>
            </div>
          </td>
        </tr>
      </table>

      <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
    </div>
    
    <!-- Batch Actions Footer -->
    <div class="batch-actions-bar" *ngIf="selection.selected.length > 0">
      <div class="selected-count">
        {{selection.selected.length}} items selected
      </div>
      <div class="batch-buttons">
        <button mat-raised-button color="primary" (click)="batchUpdateStatus('fulfilled')">
          <mat-icon>check_circle</mat-icon> Mark as Fulfilled
        </button>
        <button mat-raised-button color="warn" (click)="batchUpdateStatus('expired')">
          <mat-icon>schedule</mat-icon> Mark as Expired
        </button>
        <button mat-raised-button color="accent" (click)="batchUpdateStatus('cancelled')">
          <mat-icon>cancel</mat-icon> Cancel Selected
        </button>
      </div>
    </div>
  </mat-card>
</div>
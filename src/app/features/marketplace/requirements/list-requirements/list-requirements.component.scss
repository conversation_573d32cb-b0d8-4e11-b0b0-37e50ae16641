// src/app/features/marketplace/requirements/list-requirements.component.scss

.requirements-container {
  padding: 16px;
  
  mat-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    overflow: hidden;
  }
  
  .dashboard-header {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    
    .title-section {
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 500;
        color: #333;
      }
    }
    
    .stats-section {
      display: flex;
      margin-left: 48px;
      
      .stat-item {
        margin-right: 24px;
        display: flex;
        flex-direction: column;
        
        .stat-label {
          font-size: 14px;
          color: #666;
        }
        
        .stat-value {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }
    }
    
    .action-buttons {
      margin-left: auto;
      display: flex;
      gap: 12px;
      
      button {
        display: flex;
        align-items: center;
        
        mat-icon {
          margin-right: 6px;
        }
      }
    }
  }
  
  .filter-container {
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    gap: 16px;
    
    .filter-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      flex: 1;
      
      mat-form-field {
        width: calc(25% - 12px);
        min-width: 200px;
        margin-bottom: 0;
      }
    }
    
    .filter-actions {
      display: flex;
      gap: 8px;
      
      button {
        height: 40px;
      }
    }
  }
  
  .table-container {
    position: relative;
    overflow: auto;
    
    .loading-shade {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.7);
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .requirements-table {
      width: 100%;
      
      .mat-column-select {
        width: 50px;
        padding-left: 16px;
      }
      
      .mat-column-id {
        width: 60px;
      }
      
      .mat-column-image {
        width: 80px;
      }
      
      .mat-column-matches {
        width: 120px;
        text-align: center;
      }
      
      .mat-column-actions {
        width: 140px;
        text-align: right;
      }
      
      .mat-column-status {
        width: 120px;
      }
      
      .status-chip {
        padding: 4px 8px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
        display: inline-block;
        min-width: 70px;
        text-align: center;
        
        &.status-active {
          background-color: #e3f2fd;
          color: #1976d2;
        }
        
        &.status-fulfilled {
          background-color: #e8f5e9;
          color: #388e3c;
        }
        
        &.status-expired {
          background-color: #f5f5f5;
          color: #757575;
        }
        
        &.status-cancelled {
          background-color: #ffebee;
          color: #d32f2f;
        }
      }
      
      .crop-image-container {
        width: 48px;
        height: 48px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      
      .crop-thumbnail {
        max-width: 48px;
        max-height: 48px;
        object-fit: cover;
        border-radius: 4px;
        cursor: pointer;
        
        &:hover {
          transform: scale(1.1);
          transition: transform 0.2s;
        }
      }
      
      .no-image {
        width: 48px;
        height: 48px;
        background-color: #f5f5f5;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        
        mat-icon {
          color: #bdbdbd;
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }
      
      .price-difference {
        display: flex;
        align-items: center;
        font-size: 12px;
        margin-top: 4px;
        
        &.negative {
          color: #f44336;
        }
        
        &.positive {
          color: #4caf50;
        }
        
        .trend-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
          margin-right: 2px;
        }
      }
      
      .historical-note {
        font-size: 10px;
        color: #757575;
        display: block;
      }
      
      .variety-text {
        font-size: 12px;
        color: #757575;
        font-style: italic;
      }
      
      // Styling for matches column
      .matches-count {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 4px;
        
        .count {
          font-weight: 500;
          font-size: 16px;
        }
        
        .label {
          font-size: 12px;
          color: #757575;
        }
      }
      
      .active-row {
        background-color: #fafafa;
      }

      .due-tag {
        background-color: #ffecb3;
        color: #ff8f00;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        margin-left: 6px;
      }
    }
    
    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      color: rgba(0, 0, 0, 0.54);
      
      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        margin-bottom: 16px;
      }
    }
  }
  
  .batch-actions-bar {
    display: flex;
    align-items: center;
    background-color: rgba(33, 150, 243, 0.08);
    padding: 8px 16px;
    border-top: 1px solid rgba(33, 150, 243, 0.2);
    
    .selected-count {
      font-size: 14px;
      color: #1976d2;
      font-weight: 500;
    }
    
    .batch-buttons {
      margin-left: auto;
      display: flex;
      gap: 8px;
    }
  }
}

// Error notification styling
.error-notification {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #212121;
  color: white;
  padding: 12px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  
  button {
    color: #90caf9;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

// Override mat-snackbar styles for error messages
::ng-deep .mat-mdc-snack-bar-container.error-snackbar {
  .mdc-snackbar__surface {
    background-color: #212121;
    color: white;
  }
  
  .mat-mdc-snack-bar-actions .mdc-button {
    color: #90caf9;
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .requirements-container {
    .dashboard-header {
      flex-direction: column;
      align-items: flex-start;
      
      .stats-section {
        margin-left: 0;
        margin-top: 16px;
      }
      
      .action-buttons {
        margin-left: 0;
        margin-top: 16px;
        width: 100%;
      }
    }
    
    .filter-container {
      flex-direction: column;
      
      .filter-controls {
        width: 100%;
        
        mat-form-field {
          width: calc(50% - 8px);
        }
      }
      
      .filter-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}

@media (max-width: 768px) {
  .requirements-container {
    padding: 8px;
    
    .filter-container .filter-controls mat-form-field {
      width: 100%;
    }
    
    .table-container {
      overflow-x: auto;
      
      .requirements-table {
        .mat-column-market_price,
        .mat-column-matches {
          display: none;
        }
      }
    }
  }
}
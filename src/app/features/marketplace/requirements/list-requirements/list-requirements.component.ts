// src/app/features/marketplace/requirements/list-requirements/list-requirements.component.ts
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { SelectionModel } from '@angular/cdk/collections';
import { saveAs } from 'file-saver';
import { retry, finalize } from 'rxjs/operators';

import {
  RequirementService,
  Requirement,
} from '../../../../core/auth/services/requirement.service';
import { CropService } from '../../../../core/auth/services/masters/crop.service';
import { MarketPriceService } from '../../../../core/auth/services/market-price.service';
import { FarmerProduceService } from '../../../../core/auth/services/farmer-produce.service';
import { environment } from '../../../../../environments/environment';
import { ImageViewerComponent } from '../../../../components/image-viewer.component';
import { MatchingProduceDialogComponent } from '../matching-produce-dialog.component';

interface EnhancedRequirement extends Requirement {
  market_price?: number | null;
  price_difference?: number | null;
  crop_image_url?: string | null;
  is_historical_price?: boolean;
  market_price_unit?: string;
  availableProduceCount?: number | null;
}

interface RequirementStats {
  active: number;
  fulfilled: number;
  expired: number;
  cancelled: number;
  totalValue: number;
}

@Component({
  selector: 'app-list-requirements',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSelectModule,
    MatSnackBarModule,
    MatCardModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatMenuModule,
    MatDialogModule,
    MatCheckboxModule,
  ],
  templateUrl: './list-requirements.component.html',
  styleUrls: ['./list-requirements.component.scss'],
})
export class ListRequirementsComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'id',
    'image',
    'product_name',
    'quantity',
    'unit_type',
    'price_offered',
    'market_price',
    'matches',
    'status',
    'required_by',
    'actions',
  ];

  dataSource = new MatTableDataSource<EnhancedRequirement>([]);
  selection = new SelectionModel<EnhancedRequirement>(true, []);
  isLoading = false;
  filterForm: FormGroup;
  crops: any[] = [];
  requirementStats: RequirementStats | null = null;
  errorMessage: string | null = null;

  statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'fulfilled', label: 'Fulfilled' },
    { value: 'expired', label: 'Expired' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private requirementService: RequirementService,
    private cropService: CropService,
    private marketPriceService: MarketPriceService,
    private produceService: FarmerProduceService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.filterForm = this.fb.group({
      product_id: [''],
      status: [''],
      required_by_from: [''],
      required_by_to: [''],
    });
  }

  ngOnInit(): void {
    this.loadCrops();
    this.loadRequirements();
    this.calculateStats();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadCrops(): void {
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.crops = response.data.filter((crop: any) => crop.is_active);
        }
      },
      error: (error) => {
        this.snackBar.open('Error loading crops', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
      },
    });
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    // Otherwise, prepend the API URL
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    // Replace broken image with a placeholder
    event.target.src = 'assets/images/placeholder-image.png';
  }

  openImageDialog(imageUrl: string): void {
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl },
      width: '80%',
      maxWidth: '1000px',
    });
  }

  /**
   * Dismisses the current error message
   */
  dismissError(): void {
    this.errorMessage = null;
  }

  loadRequirements(): void {
    this.isLoading = true;
    const filters = this.filterForm.value;

    // Format dates if present
    if (filters.required_by_from instanceof Date) {
      filters.required_by_from = filters.required_by_from
        .toISOString()
        .split('T')[0];
    }
    if (filters.required_by_to instanceof Date) {
      filters.required_by_to = filters.required_by_to
        .toISOString()
        .split('T')[0];
    }

    // Get the latest market prices
    this.marketPriceService.getLatestPrices().subscribe({
      next: (priceResponse) => {
        const latestPrices = priceResponse.data || [];

        // Get requirements
        this.requirementService.getAllRequirements(filters).subscribe({
          next: (response) => {
            if (response.status === 'success' && response.data) {
              // Enhance requirements with market price data and crop images
              const enhancedRequirements = response.data.map((req) => {
                // If requirement is not active, use the stored historical price if available
                if (req.status !== 'active' && req.market_price_at_creation) {
                  return {
                    ...req,
                    market_price: req.market_price_at_creation,
                    price_difference:
                      req.price_offered - req.market_price_at_creation,
                    market_price_unit: req.market_price_unit || req.unit_type,
                    is_historical_price: true,
                  };
                } else {
                  // For active requirements, use current market price
                  const marketPrice = latestPrices.find(
                    (p) => p.product_id === req.product_id
                  );

                  return {
                    ...req,
                    market_price: marketPrice
                      ? marketPrice.price_per_unit
                      : null,
                    price_difference: marketPrice
                      ? req.price_offered - marketPrice.price_per_unit
                      : null,
                    is_historical_price: false,
                  };
                }
              });

              this.dataSource.data = enhancedRequirements;

              // Reset selection when data changes
              this.selection.clear();

              // Calculate stats
              this.calculateStats();
            }
            this.isLoading = false;
          },
          error: (error) => {
            this.snackBar.open('Error loading requirements', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
            this.isLoading = false;
          },
        });
      },
      error: (error) => {
        // Continue loading requirements even if market prices fail
        this.loadRequirementsWithoutPrices(filters);
      },
    });
  }

  // Helper method for loading without prices
  loadRequirementsWithoutPrices(filters: any): void {
    this.requirementService.getAllRequirements(filters).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.dataSource.data = response.data;

          // Reset selection when data changes
          this.selection.clear();

          // Calculate stats
          this.calculateStats();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error loading requirements', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
      },
    });
  }

  // Calculate summary statistics
  calculateStats(): void {
    const stats: RequirementStats = {
      active: 0,
      fulfilled: 0,
      expired: 0,
      cancelled: 0,
      totalValue: 0,
    };

    this.dataSource.data.forEach((req) => {
      // Count by status
      if (req.status) {
        stats[req.status as keyof RequirementStats]++;
      }

      // Calculate total value
      if (req.status === 'active' && req.price_offered && req.quantity) {
        stats.totalValue += req.price_offered * req.quantity;
      }
    });

    this.requirementStats = stats;
  }

  applyFilter(): void {
    this.loadRequirements();
  }

  resetFilter(): void {
    this.filterForm.reset();
    this.loadRequirements();
  }

  deleteRequirement(id: number): void {
    if (confirm('Are you sure you want to delete this requirement?')) {
      this.requirementService.deleteRequirement(id).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open('Requirement deleted successfully', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
            this.loadRequirements();
          }
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || 'Error deleting requirement',
            'Close',
            {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            }
          );
        },
      });
    }
  }

  updateStatus(id: number, newStatus: string): void {
    this.requirementService.updateRequirementStatus(id, newStatus).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.snackBar.open(`Status updated to ${newStatus}`, 'Close', {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          });
          this.loadRequirements();
        }
      },
      error: (error) => {
        this.snackBar.open(
          error.error?.message || 'Error updating status',
          'Close',
          {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          }
        );
      },
    });
  }

  // Batch status update
  batchUpdateStatus(newStatus: string): void {
    const selectedIds = this.selection.selected.map((req) => req.id);
    if (!selectedIds.length) return;

    // Confirm before proceeding
    if (
      confirm(
        `Are you sure you want to mark ${selectedIds.length} requirements as ${newStatus}?`
      )
    ) {
      // This would need a batch update API endpoint to be implemented
      // For now, we'll update each requirement individually
      let completedCount = 0;
      let errorCount = 0;

      selectedIds.forEach((id) => {
        if (id) {
          this.requirementService
            .updateRequirementStatus(id, newStatus)
            .subscribe({
              next: (response) => {
                if (response.status === 'success') {
                  completedCount++;
                  if (completedCount + errorCount === selectedIds.length) {
                    this.finalizeBatchUpdate(
                      completedCount,
                      errorCount,
                      newStatus
                    );
                  }
                }
              },
              error: (error) => {
                errorCount++;
                if (completedCount + errorCount === selectedIds.length) {
                  this.finalizeBatchUpdate(
                    completedCount,
                    errorCount,
                    newStatus
                  );
                }
              },
            });
        }
      });
    }
  }

  // Helper to finalize batch update
  finalizeBatchUpdate(succeeded: number, failed: number, status: string): void {
    let message = `Updated ${succeeded} requirements to ${status}`;
    if (failed > 0) {
      message += `, ${failed} failed`;
    }

    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });

    this.loadRequirements();
  }

  /**
   * Improved check matching produce method with robust error handling
   */
  checkMatchingProduce(requirementId: number | undefined): void {
    if (!requirementId) return;

    // Clear any existing error
    this.dismissError();
    this.isLoading = true;

    // Immediately update UI to show we're checking
    const dataIndex = this.dataSource.data.findIndex(
      (req) => req.id === requirementId
    );

    if (dataIndex >= 0) {
      const updatedData = [...this.dataSource.data];
      updatedData[dataIndex] = {
        ...updatedData[dataIndex],
        availableProduceCount: null, // null indicates "checking"
      };
      this.dataSource.data = updatedData;
    }

    // Make the API call with proper error handling
    this.produceService
      .getProduceForRequirement(requirementId)
      .pipe(
        // Add retry logic
        retry(1),
        // Ensure loading state is cleared even on error
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            const matchingProduce = response.data?.matching_produce || [];

            // Update the data
            if (dataIndex >= 0) {
              const updatedData = [...this.dataSource.data];
              updatedData[dataIndex] = {
                ...updatedData[dataIndex],
                availableProduceCount: matchingProduce.length,
              };
              this.dataSource.data = updatedData;
            }

            // If we have matches, show the notification
            if (matchingProduce.length > 0) {
              this.snackBar
                .open(
                  `Found ${matchingProduce.length} matching produce items`,
                  'View',
                  { duration: 5000 }
                )
                .onAction()
                .subscribe(() => {
                  this.viewMatchingProduce(requirementId, matchingProduce);
                });
            }
          } else {
            this.handleMatchingProduceError(
              requirementId,
              response.message || 'Failed to retrieve matching produce'
            );
          }
        },
        error: (error) => {
          console.error('Error checking matching produce:', error);

          // Handle different error types
          let errorMessage = 'Error checking matching produce';

          if (error.status === 500) {
            errorMessage =
              'Server error while retrieving matching produce. Please try again later.';
          } else if (error.status === 404) {
            errorMessage = 'No matching produce found for this requirement.';
          } else if (error.error?.message) {
            errorMessage = error.error.message;
          }

          this.handleMatchingProduceError(requirementId, errorMessage);
        },
      });
  }

  /**
   * Helper method to handle errors with matching produce
   */
  private handleMatchingProduceError(
    requirementId: number,
    errorMessage: string
  ): void {
    // Update the global error message
    this.errorMessage = errorMessage;

    // Auto-dismiss after 7 seconds
    setTimeout(() => {
      if (this.errorMessage === errorMessage) {
        this.dismissError();
      }
    }, 7000);

    // Update the UI to show 0 matches
    const dataIndex = this.dataSource.data.findIndex(
      (req) => req.id === requirementId
    );

    if (dataIndex >= 0) {
      const updatedData = [...this.dataSource.data];
      updatedData[dataIndex] = {
        ...updatedData[dataIndex],
        availableProduceCount: 0, // Show 0 on error
      };
      this.dataSource.data = updatedData;
    }

    // Also show in console for debugging
    console.warn(`Error with requirement ${requirementId}:`, errorMessage);
  }

  // View matching produce in dialog
  viewMatchingProduce(
    requirementId: number | undefined,
    preloadedProduce?: any[]
  ): void {
    if (!requirementId) return;

    if (preloadedProduce) {
      // If we already have the matching produce data
      this.openMatchingProduceDialog(requirementId, preloadedProduce);
    } else {
      // Otherwise fetch it
      this.isLoading = true;
      this.produceService.getProduceForRequirement(requirementId).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            const matchingProduce = response.data.matching_produce || [];
            this.openMatchingProduceDialog(requirementId, matchingProduce);
          } else {
            this.snackBar.open('Failed to load matching produce', 'Close', {
              duration: 3000,
            });
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.snackBar.open('Error loading matching produce', 'Close', {
            duration: 3000,
          });
          this.isLoading = false;
        },
      });
    }
  }

  // Helper to open matching produce dialog
  openMatchingProduceDialog(
    requirementId: number,
    matchingProduce: any[]
  ): void {
    // Find the requirement data
    const requirement = this.dataSource.data.find(
      (req) => req.id === requirementId
    );

    this.dialog.open(MatchingProduceDialogComponent, {
      width: '80%',
      maxWidth: '1200px',
      data: {
        requirement,
        matchingProduce,
      },
    });
  }

  // Export requirements data
  exportRequirements(): void {
    this.isLoading = true;
    const filters = this.filterForm.value;

    // This would need an API endpoint for exporting
    // For now, we'll export the current data as CSV
    try {
      const dataToExport = this.dataSource.data.map((req) => ({
        ID: req.id,
        Product: req.crop_name,
        Variety: req.variety || '',
        Quantity: req.quantity,
        Unit: req.unit_type,
        'Price Offered': req.price_offered,
        'Market Price': req.market_price || 'N/A',
        Status: req.status,
        'Required By': req.required_by
          ? new Date(req.required_by).toLocaleDateString()
          : 'No deadline',
        'Created At': req.created_at
          ? new Date(req.created_at).toLocaleDateString()
          : '',
      }));

      // Convert to CSV
      const headers = Object.keys(dataToExport[0]);
      const csvRows = [
        headers.join(','),
        ...dataToExport.map((row) =>
          headers
            .map((header) =>
              JSON.stringify(row[header as keyof typeof row] || '')
            )
            .join(',')
        ),
      ];
      const csvString = csvRows.join('\n');

      // Create and download the file
      const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
      saveAs(blob, `requirements_${new Date().toISOString().slice(0, 10)}.csv`);

      this.snackBar.open('Requirements exported successfully', 'Close', {
        duration: 3000,
      });
    } catch (error) {
      console.error('Error exporting requirements:', error);
      this.snackBar.open('Error exporting requirements', 'Close', {
        duration: 3000,
      });
    }

    this.isLoading = false;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'active':
        return 'status-active';
      case 'fulfilled':
        return 'status-fulfilled';
      case 'expired':
        return 'status-expired';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  // Check if required date is within 7 days
  isDueSoon(date: string | Date): boolean {
    const requiredDate = new Date(date);
    const today = new Date();
    const diffTime = requiredDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays >= 0 && diffDays <= 7;
  }

  // Selection methods for batch operations
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.filter(
      (row) => row.status === 'active'
    ).length;
    return numSelected === numRows;
  }

  masterToggle() {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.dataSource.data
        .filter((row) => row.status === 'active')
        .forEach((row) => this.selection.select(row));
    }
  }

  checkboxLabel(row?: EnhancedRequirement): string {
    if (!row) {
      return `${this.isAllSelected() ? 'deselect' : 'select'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${
      row.id
    }`;
  }
}

// src/app/features/marketplace/requirements/matching-produce-dialog.component.ts
import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { environment } from '../../../../environments/environment';
import { FarmerProduceService } from '../../../core/auth/services/farmer-produce.service';

export interface DialogData {
  requirement: any;
  matchingProduce: any[];
}

@Component({
  selector: 'app-matching-produce-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatTooltipModule,
    MatSnackBarModule,
  ],
  template: `
    <h2 mat-dialog-title>
      Matching Produce for Requirement #{{ data.requirement?.id }}
    </h2>
    <div mat-dialog-content>
      <!-- Requirement details summary -->
      <div class="requirement-summary">
        <h3>Requirement Details</h3>
        <div class="summary-details">
          <div class="summary-item">
            <span class="label">Product:</span>
            <span class="value"
              >{{ data.requirement?.crop_name }}
              <span *ngIf="data.requirement?.variety"
                >({{ data.requirement?.variety }})</span
              >
            </span>
          </div>
          <div class="summary-item">
            <span class="label">Quantity:</span>
            <span class="value"
              >{{ data.requirement?.quantity }}
              {{ data.requirement?.unit_type }}</span
            >
          </div>
          <div class="summary-item">
            <span class="label">Price Offered:</span>
            <span class="value"
              >₹{{ data.requirement?.price_offered | number : '1.2-2' }}</span
            >
          </div>
          <div class="summary-item">
            <span class="label">Required By:</span>
            <span class="value">
              {{
                data.requirement?.required_by
                  ? (data.requirement?.required_by | date)
                  : 'No deadline'
              }}
            </span>
          </div>
        </div>
      </div>

      <!-- Matching produce table -->
      <div class="matching-produce">
        <h3>{{ data.matchingProduce.length }} Matching Produce Listings</h3>
        <table
          mat-table
          [dataSource]="data.matchingProduce"
          class="mat-elevation-z1 produce-table"
        >
          <!-- Image Column -->
          <ng-container matColumnDef="image">
            <th mat-header-cell *matHeaderCellDef>Image</th>
            <td mat-cell *matCellDef="let produce">
              <div class="produce-image-container">
                <img
                  *ngIf="produce.primary_image_url"
                  [src]="getFullImageUrl(produce.primary_image_url)"
                  alt="{{ produce.crop_name }}"
                  class="produce-thumbnail"
                />
                <div *ngIf="!produce.primary_image_url" class="no-image">
                  <mat-icon>image_not_supported</mat-icon>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Farmer Column -->
          <ng-container matColumnDef="farmer">
            <th mat-header-cell *matHeaderCellDef>Farmer</th>
            <td mat-cell *matCellDef="let produce">
              {{ produce.farmer_name }}
              <div class="secondary-info">
                <mat-icon class="small-icon">phone</mat-icon>
                {{ produce.farmer_mobile }}
              </div>
            </td>
          </ng-container>

          <!-- Produce Details Column -->
          <ng-container matColumnDef="details">
            <th mat-header-cell *matHeaderCellDef>Details</th>
            <td mat-cell *matCellDef="let produce">
              <div class="primary-info">{{ produce.crop_name }}</div>
              <div *ngIf="produce.variety" class="secondary-info">
                Variety: {{ produce.variety }}
              </div>
              <div
                *ngIf="produce.description"
                class="secondary-info description"
              >
                {{ produce.description }}
              </div>
            </td>
          </ng-container>

          <!-- Quantity Column -->
          <ng-container matColumnDef="quantity">
            <th mat-header-cell *matHeaderCellDef>Quantity</th>
            <td mat-cell *matCellDef="let produce">
              {{ produce.quantity }} {{ produce.unit_type }}
            </td>
          </ng-container>

          <!-- Availability Column -->
          <ng-container matColumnDef="availability">
            <th mat-header-cell *matHeaderCellDef>Availability</th>
            <td mat-cell *matCellDef="let produce">
              <div>From: {{ formatDate(produce.available_from) }}</div>
              <div *ngIf="produce.available_until">
                To: {{ formatDate(produce.available_until) }}
              </div>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let produce">
              <button
                mat-icon-button
                color="primary"
                (click)="viewProduce(produce.id)"
                matTooltip="View Details"
              >
                <mat-icon>visibility</mat-icon>
              </button>
              <button
                mat-icon-button
                color="accent"
                (click)="
                  linkProduceToRequirement(produce.id, data.requirement?.id)
                "
                matTooltip="Link to Requirement"
              >
                <mat-icon>link</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </div>
    </div>
    <div mat-dialog-actions align="end">
      <button mat-button (click)="close()">Close</button>
    </div>
  `,
  styles: [
    `
      .requirement-summary {
        margin-bottom: 20px;
        padding: 16px;
        background-color: #f5f5f5;
        border-radius: 4px;
      }

      .requirement-summary h3 {
        margin-top: 0;
        font-size: 18px;
        margin-bottom: 12px;
      }

      .summary-details {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
      }

      .summary-item {
        flex: 1;
        min-width: 150px;
      }

      .summary-item .label {
        font-weight: 500;
        display: block;
        margin-bottom: 4px;
        color: rgba(0, 0, 0, 0.6);
      }

      .summary-item .value {
        font-size: 16px;
      }

      .matching-produce h3 {
        font-size: 18px;
        margin-bottom: 12px;
      }

      .produce-table {
        width: 100%;
      }

      .produce-image-container {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .produce-thumbnail {
        max-width: 50px;
        max-height: 50px;
        object-fit: cover;
        border-radius: 4px;
      }

      .no-image {
        width: 50px;
        height: 50px;
        background-color: #f0f0f0;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
      }

      .primary-info {
        font-weight: 500;
      }

      .secondary-info {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        margin-top: 4px;
      }

      .small-icon {
        font-size: 16px;
        height: 16px;
        width: 16px;
        margin-right: 4px;
      }

      .description {
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      mat-dialog-content {
        max-height: 80vh;
        min-height: 300px;
      }
    `,
  ],
})
export class MatchingProduceDialogComponent {
  displayedColumns: string[] = [
    'image',
    'farmer',
    'details',
    'quantity',
    'availability',
    'actions',
  ];

  apiBaseUrl: string;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    private dialogRef: MatDialogRef<MatchingProduceDialogComponent>,
    private snackBar: MatSnackBar,
    private router: Router,
    private produceService: FarmerProduceService
  ) {
    this.apiBaseUrl = environment.apiUrl.endsWith('/api')
      ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
      : environment.apiUrl;
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  formatDate(date: string): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  }

  viewProduce(produceId: number): void {
    this.router.navigate(['/marketplace/produce/view', produceId]);
    this.dialogRef.close();
  }

  linkProduceToRequirement(
    produceId: number,
    requirementId: number | undefined
  ): void {
    if (!requirementId) {
      this.snackBar.open('Requirement ID is missing', 'Close', {
        duration: 3000,
      });
      return;
    }

    // Prefill quantity with requirement's quantity
    const quantity = this.data.requirement?.quantity || 0;

    this.produceService
      .createTransactionFromProduce(
        produceId,
        requirementId,
        quantity,
        `Linked from matching produce dialog`
      )
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar
              .open(
                'Successfully linked produce to requirement',
                'View Transaction',
                {
                  duration: 5000,
                }
              )
              .onAction()
              .subscribe(() => {
                // Navigate to transaction view if user clicks action button
                if (response.data?.id) {
                  this.router.navigate([
                    '/marketplace/transactions/view',
                    response.data.id,
                  ]);
                  this.dialogRef.close(true); // Close with true to indicate successful operation
                }
              });

            // Close dialog with success status
            this.dialogRef.close(true);
          } else {
            this.snackBar.open(
              'Failed to link produce to requirement',
              'Close',
              { duration: 3000 }
            );
          }
        },
        error: (error) => {
          this.snackBar.open(
            error.message || 'Error linking produce to requirement',
            'Close',
            {
              duration: 3000,
            }
          );
        },
      });
  }

  close(): void {
    this.dialogRef.close();
  }
}

// src/app/features/marketplace/requirements/edit-requirement/edit-requirement.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RequirementService } from '../../../../core/auth/services/requirement.service';
import { CropService } from '../../../../core/auth/services/masters/crop.service';
import { MarketPriceService } from '../../../../core/auth/services/market-price.service';
import { environment } from '../../../../../environments/environment';

function toISTDateString(date: Date): string {
  // Add 5 hours and 30 minutes to get IST
  const istDate = new Date(date.getTime() + 5.5 * 60 * 60 * 1000);
  return istDate.toISOString().split('T')[0];
}

@Component({
  selector: 'app-edit-requirement',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatSnackBarModule,
    MatTooltipModule,
  ],
  templateUrl: './edit-requirement.component.html',
  styleUrls: ['./edit-requirement.component.scss'],
})
export class EditRequirementComponent implements OnInit {
  requirementForm: FormGroup;
  crops: any[] = [];
  isLoading = true;
  isSubmitting = false;
  requirementId: number | null = null;
  isEditMode = false;
  selectedCropImage: string | null = null;
  currentMarketPrice: number | null = null;
  selectedUnitType: string = 'kg';
  isHistoricalPrice: boolean = false;

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    private fb: FormBuilder,
    private requirementService: RequirementService,
    private cropService: CropService,
    private marketPriceService: MarketPriceService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.requirementForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadCrops();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.requirementId = +id;
        this.isEditMode = true;
        this.loadRequirement(this.requirementId);
      }
    });

    // Listen for unit type changes to update market price
    this.requirementForm.get('unit_type')?.valueChanges.subscribe((value) => {
      this.selectedUnitType = value;
      // Update market price if a crop is already selected and it's an active requirement
      if (
        this.requirementForm.get('product_id')?.value &&
        (!this.isEditMode ||
          this.requirementForm.get('status')?.value === 'active')
      ) {
        this.fetchMarketPrice(this.requirementForm.get('product_id')?.value);
      }
    });

    // Listen for product_id changes
    this.requirementForm.get('product_id')?.valueChanges.subscribe((value) => {
      if (value) {
        this.onCropSelected(value);
      }
    });

    // Listen for status changes to decide whether to show live or historical prices
    this.requirementForm.get('status')?.valueChanges.subscribe((value) => {
      if (value === 'active') {
        // If changed to active, fetch the latest market price
        if (this.requirementForm.get('product_id')?.value) {
          this.isHistoricalPrice = false;
          this.fetchMarketPrice(this.requirementForm.get('product_id')?.value);
        }
      } else {
        // For non-active statuses, try to use the historical price if available
        this.isHistoricalPrice = true;
      }
    });
  }

  private createForm(): FormGroup {
    return this.fb.group({
      product_id: ['', Validators.required],
      variety: [''],
      quantity: ['', [Validators.required, Validators.min(0.01)]],
      unit_type: ['kg', Validators.required],
      price_offered: ['', [Validators.required, Validators.min(0.01)]],
      description: [''],
      required_by: ['', Validators.required],
      status: ['active'],
      market_price_at_creation: [null],
      market_price_unit: [''],
    });
  }

  loadCrops(): void {
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.crops = response.data.filter((crop: any) => crop.is_active);
        }
        // We don't set isLoading = false here because we're waiting for the requirement data too
      },
      error: (error) => {
        this.snackBar.open('Error loading crops', 'Close', {
          duration: 3000,
        });
        this.isLoading = false;
      },
    });
  }

  loadRequirement(id: number): void {
    this.isLoading = true;
    this.requirementService.getRequirementById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.patchFormValues(response.data);

          // Get the crop image regardless of status
          this.updateCropImage(response.data.product_id);

          // For active requirements, fetch current market price
          if (response.data.status === 'active') {
            this.isHistoricalPrice = false;
            this.fetchMarketPrice(response.data.product_id);
          }
          // For closed requirements, use the stored historical price if available
          else if (response.data.market_price_at_creation) {
            this.isHistoricalPrice = true;
            this.currentMarketPrice = response.data.market_price_at_creation;
            this.selectedUnitType =
              response.data.market_price_unit || response.data.unit_type;
          }
          // If no historical price is available but it's a closed requirement, try to fetch latest price anyway
          else {
            this.fetchMarketPrice(response.data.product_id);
            this.isHistoricalPrice = false;
          }
        } else {
          this.snackBar.open('Failed to load requirement details', 'Close', {
            duration: 3000,
          });
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open(
          error.error?.message || 'Error loading requirement details',
          'Close',
          { duration: 3000 }
        );
        this.isLoading = false;
        this.goBack();
      },
    });
  }

  patchFormValues(requirement: any): void {
    // Convert required_by to Date object if it's a string
    let requiredBy = requirement.required_by;
    if (typeof requiredBy === 'string') {
      requiredBy = new Date(requiredBy);
    }

    this.requirementForm.patchValue({
      product_id: requirement.product_id,
      variety: requirement.variety,
      quantity: requirement.quantity,
      unit_type: requirement.unit_type,
      price_offered: requirement.price_offered,
      description: requirement.description,
      required_by: requiredBy,
      status: requirement.status,
      market_price_at_creation: requirement.market_price_at_creation,
      market_price_unit: requirement.market_price_unit,
    });

    // Set the selected unit type
    this.selectedUnitType = requirement.unit_type;
  }

  updateCropImage(cropId: number): void {
    // Update the crop image
    const selectedCrop = this.crops.find((crop) => crop.id === cropId);
    if (selectedCrop && selectedCrop.image_url) {
      this.selectedCropImage = selectedCrop.image_url;
    } else {
      this.selectedCropImage = null;
    }
  }

  onCropSelected(cropId: number): void {
    // Update the crop image
    this.updateCropImage(cropId);

    // Only fetch market price for active requirements or when creating new requirements
    if (
      !this.isEditMode ||
      this.requirementForm.get('status')?.value === 'active'
    ) {
      this.fetchMarketPrice(cropId);
      this.isHistoricalPrice = false;
    } else {
      // For closed requirements (non-active), don't update the market price
      // Keep using the historical price if available
      const historicalPrice = this.requirementForm.get(
        'market_price_at_creation'
      )?.value;
      if (historicalPrice) {
        this.currentMarketPrice = historicalPrice;
        this.selectedUnitType =
          this.requirementForm.get('market_price_unit')?.value ||
          this.requirementForm.get('unit_type')?.value;
        this.isHistoricalPrice = true;
      } else {
        // If no historical price exists, fetch current market price but mark it as not historical
        this.fetchMarketPrice(cropId);
        this.isHistoricalPrice = false;
      }
    }
  }

  fetchMarketPrice(cropId: number): void {
    // Clear current price
    this.currentMarketPrice = null;

    // Fetch latest market prices
    this.marketPriceService.getLatestPrices().subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          const cropPrice = response.data.find(
            (price) => price.product_id === cropId
          );
          if (cropPrice) {
            // Store the market price
            this.currentMarketPrice = cropPrice.price_per_unit;
          }
        }
      },
      error: (error) => {
        console.error('Error fetching market prices:', error);
      },
    });
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';

    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // Otherwise, prepend the API URL
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    // Replace broken image with a placeholder
    event.target.src = 'assets/images/placeholder-image.png';
  }

  onSubmit(): void {
    if (this.requirementForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.requirementForm.controls).forEach((key) => {
        this.requirementForm.get(key)?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const formData = { ...this.requirementForm.value };

    // Format the required_by date to ISO string if it's a Date object
    if (formData.required_by instanceof Date) {
      formData.required_by = toISTDateString(formData.required_by);
    }

    // If it's an active requirement, update the market price at creation
    if (!this.isEditMode || formData.status === 'active') {
      if (this.currentMarketPrice !== null) {
        formData.market_price_at_creation = this.currentMarketPrice;
        formData.market_price_unit = this.selectedUnitType;
      }
    }

    // Determine if we're creating or updating
    if (this.isEditMode && this.requirementId) {
      this.requirementService
        .updateRequirement(this.requirementId, formData)
        .subscribe({
          next: (response) => {
            if (response.status === 'success') {
              this.snackBar.open('Requirement updated successfully', 'Close', {
                duration: 3000,
              });
              this.router.navigate(['../../view', this.requirementId], {
                relativeTo: this.route,
              });
            }
            this.isSubmitting = false;
          },
          error: (error) => {
            this.snackBar.open(
              error.error?.message || 'Error updating requirement',
              'Close',
              { duration: 3000 }
            );
            this.isSubmitting = false;
          },
        });
    } else {
      this.requirementService.createRequirement(formData).subscribe({
        next: (response) => {
          if (response.status === 'success' && response.data) {
            this.snackBar.open('Requirement created successfully', 'Close', {
              duration: 3000,
            });
            this.router.navigate(['../../view', response.data.id], {
              relativeTo: this.route,
            });
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || 'Error creating requirement',
            'Close',
            { duration: 3000 }
          );
          this.isSubmitting = false;
        },
      });
    }
  }

  goBack(): void {
    if (this.isEditMode && this.requirementId) {
      this.router.navigate(['../../view-requirement', this.requirementId], {
        relativeTo: this.route,
      });
    } else {
      this.router.navigate(['../'], { relativeTo: this.route });
    }
  }
}

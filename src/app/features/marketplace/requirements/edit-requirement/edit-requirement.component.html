<!-- edit-requirement.component.html -->
<div class="edit-requirement-container">
  <mat-card>
    <!-- Header -->
    <div class="card-header">
      <div class="header-left">
        <button mat-icon-button (click)="goBack()" matTooltip="Back">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <h2>{{ isEditMode ? 'Edit' : 'Create' }} Requirement</h2>
      </div>
    </div>
    
    <mat-divider></mat-divider>
    
    <!-- Loading State -->
    <div class="card-content" *ngIf="isLoading">
      <div class="loading-message">
        <mat-icon>hourglass_empty</mat-icon>
        <p>Loading requirement details...</p>
      </div>
    </div>
    
    <!-- Form -->
    <div class="card-content" *ngIf="!isLoading">
      <form [formGroup]="requirementForm" (ngSubmit)="onSubmit()">
        <div class="form-grid">
          <!-- Product Selection -->
          <mat-form-field appearance="outline">
            <mat-label>Product</mat-label>
            <mat-select formControlName="product_id" required>
              <mat-option *ngFor="let crop of crops" [value]="crop.id">
                {{ crop.crop_name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="requirementForm.get('product_id')?.hasError('required')">
              Product is required
            </mat-error>
          </mat-form-field>
          
          <!-- Variety -->
          <mat-form-field appearance="outline">
            <mat-label>Variety (Optional)</mat-label>
            <input matInput formControlName="variety">
          </mat-form-field>
          
          <!-- Crop Image and Market Price Section -->
          <div class="crop-details-container full-width" *ngIf="selectedCropImage || currentMarketPrice">
            <div class="crop-image-section" *ngIf="selectedCropImage">
              <img [src]="getFullImageUrl(selectedCropImage)" alt="Crop image" 
                  class="crop-image" (error)="handleImageError($event)">
            </div>
            
            <div class="market-price-section" *ngIf="currentMarketPrice">
              <div class="price-label">
                {{ isHistoricalPrice ? 'Market Price at Creation:' : 'Current Market Price:' }}
              </div>
              <div class="price-value">₹{{currentMarketPrice | number:'1.2-2'}} / {{selectedUnitType}}</div>
              <div class="price-note">
                {{ isHistoricalPrice ? 'Historical data from when requirement was created' : 'Based on latest market data' }}
              </div>
            </div>
          </div>
          
          <!-- Quantity -->
          <mat-form-field appearance="outline">
            <mat-label>Quantity</mat-label>
            <input matInput type="number" formControlName="quantity" required>
            <mat-error *ngIf="requirementForm.get('quantity')?.hasError('required')">
              Quantity is required
            </mat-error>
            <mat-error *ngIf="requirementForm.get('quantity')?.hasError('min')">
              Quantity must be greater than 0
            </mat-error>
          </mat-form-field>
          
          <!-- Unit Type -->
          <mat-form-field appearance="outline">
            <mat-label>Unit Type</mat-label>
            <mat-select formControlName="unit_type" required>
              <mat-option value="kg">Kilogram (kg)</mat-option>
              <mat-option value="g">Gram (g)</mat-option>
              <mat-option value="quintal">Quintal</mat-option>
              <mat-option value="ton">Ton</mat-option>
              <mat-option value="piece">Piece</mat-option>
              <mat-option value="dozen">Dozen</mat-option>
            </mat-select>
            <mat-error *ngIf="requirementForm.get('unit_type')?.hasError('required')">
              Unit type is required
            </mat-error>
          </mat-form-field>
          
          <!-- Price Offered -->
          <mat-form-field appearance="outline">
            <mat-label>Price Offered (per unit)</mat-label>
            <input matInput type="number" formControlName="price_offered" required>
            <span matSuffix>₹</span>
            <mat-error *ngIf="requirementForm.get('price_offered')?.hasError('required')">
              Price is required
            </mat-error>
            <mat-error *ngIf="requirementForm.get('price_offered')?.hasError('min')">
              Price must be greater than 0
            </mat-error>
          </mat-form-field>
          
          <!-- Required By Date -->
          <mat-form-field appearance="outline">
            <mat-label>Required By</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="required_by" required>
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="requirementForm.get('required_by')?.hasError('required')">
              Required by date is required
            </mat-error>
          </mat-form-field>
          
          <!-- Status (show only in edit mode) -->
          <mat-form-field appearance="outline" *ngIf="isEditMode">
            <mat-label>Status</mat-label>
            <mat-select formControlName="status" required>
              <mat-option value="active">Active</mat-option>
              <mat-option value="fulfilled">Fulfilled</mat-option>
              <mat-option value="expired">Expired</mat-option>
              <mat-option value="cancelled">Cancelled</mat-option>
            </mat-select>
          </mat-form-field>
          
          <!-- Description (full width) -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Description (Optional)</mat-label>
            <textarea matInput formControlName="description" rows="4"></textarea>
          </mat-form-field>
        </div>
        
        <!-- Form Actions -->
        <div class="form-actions">
          <button type="button" mat-stroked-button (click)="goBack()">
            Cancel
          </button>
          <button type="submit" mat-raised-button color="primary" [disabled]="isSubmitting">
            <mat-icon>save</mat-icon>
            {{ isEditMode ? 'Update' : 'Create' }} Requirement
          </button>
        </div>
      </form>
    </div>
  </mat-card>
</div>
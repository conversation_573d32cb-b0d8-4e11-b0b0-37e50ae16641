// edit-requirement.component.scss
.edit-requirement-container {
  padding: 24px;
  
  mat-card {
    margin-bottom: 24px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }
  }
  
  .card-content {
    padding: 24px;
    
    .loading-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 200px;
      text-align: center;
      
      mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
      }
      
      p {
        font-size: 18px;
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 16px;
      }
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
      margin-bottom: 32px;
      
      .full-width {
        grid-column: 1 / -1;
      }
    }
    
    .crop-details-container {
      display: flex;
      gap: 24px;
      padding: 16px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #e0e0e0;
      margin-bottom: 16px;
      
      .crop-image-section {
        flex: 0 0 100px;
        
        .crop-image {
          width: 100px;
          height: 100px;
          object-fit: cover;
          border-radius: 4px;
          border: 1px solid #e0e0e0;
        }
      }
      
      .market-price-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        
        .price-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
        }
        
        .price-value {
          font-size: 24px;
          font-weight: 500;
          color: #1976d2;
          margin-bottom: 4px;
        }
        
        .price-note {
          font-size: 12px;
          color: #999;
        }
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
      
      button {
        min-width: 120px;
        
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .edit-requirement-container {
    padding: 16px 8px;
    
    .card-content {
      padding: 16px;
      
      .form-grid {
        grid-template-columns: 1fr;
      }
      
      .crop-details-container {
        flex-direction: column;
        align-items: center;
        
        .crop-image-section {
          margin-bottom: 16px;
        }
        
        .market-price-section {
          text-align: center;
        }
      }
      
      .form-actions {
        flex-direction: column-reverse;
        
        button {
          width: 100%;
        }
      }
    }
  }
}
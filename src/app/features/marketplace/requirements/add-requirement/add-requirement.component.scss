// src/app/features/marketplace/requirements/add-requirement/add-requirement.component.scss
.add-requirement-container {
    padding: 24px;
    
    mat-card {
      overflow: hidden;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }
    
    .card-content {
      padding: 24px;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
      
      .full-width {
        grid-column: 1 / -1;
      }
      
      .form-section {
        margin-top: 16px;
        margin-bottom: 8px;
        
        h3 {
          margin-top: 0;
          margin-bottom: 8px;
          font-size: 18px;
          font-weight: 500;
          color: #333;
        }
        
        mat-divider {
          margin-bottom: 16px;
        }
      }
      
      .toggle-field {
        display: flex;
        align-items: center;
        margin-top: 16px;
        margin-bottom: 16px;
      }
    }
    
    .crop-details-container {
      display: flex;
      gap: 24px;
      padding: 16px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #e0e0e0;
      margin-bottom: 16px;
      
      .crop-image-section {
        flex: 0 0 100px;
        
        .crop-image {
          width: 100px;
          height: 100px;
          object-fit: cover;
          border-radius: 4px;
          border: 1px solid #e0e0e0;
        }
      }
      
      .market-price-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        
        .price-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
        }
        
        .price-value {
          font-size: 24px;
          font-weight: 500;
          color: #1976d2;
          margin-bottom: 4px;
        }
        
        .price-note {
          font-size: 12px;
          color: #999;
        }
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
      margin-top: 24px;
      
      button {
        min-width: 120px;
        
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .add-requirement-container {
      padding: 16px 8px;
      
      .card-content {
        padding: 16px;
      }
      
      .form-grid {
        grid-template-columns: 1fr;
      }
      
      .crop-details-container {
        flex-direction: column;
        align-items: center;
        
        .crop-image-section {
          margin-bottom: 16px;
        }
        
        .market-price-section {
          text-align: center;
        }
      }
      
      .form-actions {
        flex-direction: column-reverse;
        
        button {
          width: 100%;
        }
      }
    }
  }
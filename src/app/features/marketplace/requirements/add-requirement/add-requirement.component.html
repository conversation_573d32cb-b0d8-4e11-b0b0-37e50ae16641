<!-- src/app/features/marketplace/requirements/add-requirement/add-requirement.component.html -->
<div class="add-requirement-container">
    <mat-card>
      <div class="card-header">
        <h2>Add New Requirement</h2>
        <button mat-icon-button (click)="goBack()" matTooltip="Back">
          <mat-icon>arrow_back</mat-icon>
        </button>
      </div>
  
      <div class="card-content">
        <form [formGroup]="requirementForm" (ngSubmit)="onSubmit()">
          <!-- Main form fields -->
          <div class="form-grid">
            <!-- Basic Information Section -->
            <div class="form-section full-width">
              <h3>Basic Information</h3>
              <mat-divider></mat-divider>
            </div>
  
            <!-- Product/Crop Selection -->
            <mat-form-field appearance="outline">
              <mat-label>Product</mat-label>
              <mat-select formControlName="product_id" required (selectionChange)="onCropSelected($event)">
                <mat-option *ngFor="let crop of crops" [value]="crop.id">
                  {{crop.crop_name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="requirementForm.get('product_id')?.hasError('required')">
                Product is required
              </mat-error>
            </mat-form-field>
  
            <!-- Variety -->
            <mat-form-field appearance="outline">
              <mat-label>Variety (Optional)</mat-label>
              <input matInput formControlName="variety" placeholder="e.g., Basmati, Golden, etc.">
            </mat-form-field>
  
            <!-- Crop Image and Market Price Section -->
            <div class="crop-details-container full-width" *ngIf="selectedCropImage || currentMarketPrice">
              <div class="crop-image-section" *ngIf="selectedCropImage">
                <img [src]="getFullImageUrl(selectedCropImage)" alt="Crop image" 
                     class="crop-image" (error)="handleImageError($event)">
              </div>
              
              <div class="market-price-section" *ngIf="currentMarketPrice">
                <div class="price-label">Current Market Price:</div>
                <div class="price-value">₹{{currentMarketPrice | number:'1.2-2'}} / {{selectedUnitType}}</div>
                <div class="price-note">Based on latest market data</div>
              </div>
            </div>
  
            <!-- Quantity -->
            <mat-form-field appearance="outline">
              <mat-label>Quantity</mat-label>
              <input matInput type="number" formControlName="quantity" required min="0.01" step="0.01">
              <mat-error *ngIf="requirementForm.get('quantity')?.hasError('required')">
                Quantity is required
              </mat-error>
              <mat-error *ngIf="requirementForm.get('quantity')?.hasError('min')">
                Quantity must be greater than 0
              </mat-error>
            </mat-form-field>
  
            <!-- Unit Type -->
            <mat-form-field appearance="outline">
              <mat-label>Unit Type</mat-label>
              <mat-select formControlName="unit_type" required>
                <mat-option value="kg">Kilogram (kg)</mat-option>
                <mat-option value="g">Gram (g)</mat-option>
                <mat-option value="quintal">Quintal</mat-option>
                <mat-option value="ton">Ton</mat-option>
                <mat-option value="piece">Piece</mat-option>
                <mat-option value="dozen">Dozen</mat-option>
              </mat-select>
              <mat-error *ngIf="requirementForm.get('unit_type')?.hasError('required')">
                Unit type is required
              </mat-error>
            </mat-form-field>
  
            <!-- Price Offered -->
            <mat-form-field appearance="outline">
              <mat-label>Price Offered</mat-label>
              <input matInput type="number" formControlName="price_offered" required min="0.01" step="0.01">
              <span matSuffix>₹</span>
              <mat-error *ngIf="requirementForm.get('price_offered')?.hasError('required')">
                Price is required
              </mat-error>
              <mat-error *ngIf="requirementForm.get('price_offered')?.hasError('min')">
                Price must be greater than 0
              </mat-error>
            </mat-form-field>
  
            <!-- Required By Date -->
            <mat-form-field appearance="outline">
              <mat-label>Required By (Optional)</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="required_by">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
  
            <!-- Is Negotiable -->
            <div class="toggle-field">
              <mat-slide-toggle formControlName="is_negotiable" color="primary">
                Is Price Negotiable?
              </mat-slide-toggle>
            </div>
  
            <!-- Additional Details Section -->
            <div class="form-section full-width">
              <h3>Additional Details</h3>
              <mat-divider></mat-divider>
            </div>
  
            <!-- Delivery Location -->
            <mat-form-field appearance="outline">
              <mat-label>Delivery Location (Optional)</mat-label>
              <input matInput formControlName="delivery_location">
            </mat-form-field>
  
            <!-- Quality Requirements -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Quality Requirements (Optional)</mat-label>
              <textarea matInput formControlName="quality_requirements" rows="3"></textarea>
            </mat-form-field>
  
            <!-- Additional Notes -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Additional Notes (Optional)</mat-label>
              <textarea matInput formControlName="description" rows="3"></textarea>
            </mat-form-field>
          </div>
  
          <!-- Form Actions -->
          <div class="form-actions">
            <button type="button" mat-stroked-button (click)="goBack()">
              Cancel
            </button>
            <button type="submit" mat-raised-button color="primary" [disabled]="isLoading || !requirementForm.valid">
              <mat-icon>add</mat-icon>
              Post Requirement
            </button>
          </div>
        </form>
      </div>
    </mat-card>
  </div>
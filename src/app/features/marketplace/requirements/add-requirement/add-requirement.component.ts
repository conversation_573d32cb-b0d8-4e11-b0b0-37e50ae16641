// src/app/features/marketplace/requirements/add-requirement/add-requirement.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { RequirementService } from '../../../../core/auth/services/requirement.service';
import { CropService } from '../../../../core/auth/services/masters/crop.service';
import { MarketPriceService } from '../../../../core/auth/services/market-price.service';
import { environment } from '../../../../../environments/environment';

// Utility function for date conversion to IST
function toISTDateString(date: Date): string {
  // Add 5 hours and 30 minutes to get IST
  const istDate = new Date(date.getTime() + 5.5 * 60 * 60 * 1000);
  return istDate.toISOString().split('T')[0];
}

@Component({
  selector: 'app-add-requirement',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCardModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDividerModule,
    MatSlideToggleModule,
  ],
  templateUrl: './add-requirement.component.html',
  styleUrls: ['./add-requirement.component.scss'],
})
export class AddRequirementComponent implements OnInit {
  requirementForm: FormGroup;
  crops: any[] = [];
  isLoading = false;
  selectedCropImage: string | null = null;
  currentMarketPrice: number | null = null;
  selectedUnitType: string = 'kg';

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    private fb: FormBuilder,
    private requirementService: RequirementService,
    private cropService: CropService,
    private marketPriceService: MarketPriceService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.requirementForm = this.fb.group({
      product_id: ['', Validators.required],
      variety: [''],
      quantity: ['', [Validators.required, Validators.min(0.01)]],
      unit_type: ['kg', Validators.required],
      price_offered: ['', [Validators.required, Validators.min(0.01)]],
      required_by: [''],
      delivery_location: [''],
      quality_requirements: [''],
      description: [''],
      is_negotiable: [true],
    });
  }

  ngOnInit(): void {
    this.loadCrops();

    // Listen for unit type changes to update market price
    this.requirementForm.get('unit_type')?.valueChanges.subscribe((value) => {
      this.selectedUnitType = value;
      // Update market price if a crop is already selected
      if (this.requirementForm.get('product_id')?.value) {
        this.fetchMarketPrice(this.requirementForm.get('product_id')?.value);
      }
    });
  }

  loadCrops(): void {
    this.isLoading = true;
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.crops = response.data.filter((crop: any) => crop.is_active);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error loading crops', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
      },
    });
  }

  onCropSelected(event: any): void {
    const selectedCropId = event.value;

    // Update the crop image
    const selectedCrop = this.crops.find((crop) => crop.id === selectedCropId);
    if (selectedCrop && selectedCrop.image_url) {
      this.selectedCropImage = selectedCrop.image_url;
    } else {
      this.selectedCropImage = null;
    }

    // Fetch market price for the selected crop
    this.fetchMarketPrice(selectedCropId);
  }

  fetchMarketPrice(cropId: number): void {
    // Clear current price
    this.currentMarketPrice = null;

    // Fetch latest market prices
    this.marketPriceService.getLatestPrices().subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          const cropPrice = response.data.find(
            (price) => price.product_id === cropId
          );
          if (cropPrice) {
            // Store the market price
            this.currentMarketPrice = cropPrice.price_per_unit;

            // Update the form's price_offered field with market price as default
            this.requirementForm.patchValue({
              price_offered: this.currentMarketPrice,
            });
          }
        }
      },
      error: (error) => {
        console.error('Error fetching market prices:', error);
      },
    });
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';

    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // Otherwise, prepend the API URL
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    // Replace broken image with a placeholder
    event.target.src = 'assets/images/placeholder-image.png';
  }

  onSubmit(): void {
    if (this.requirementForm.valid) {
      this.isLoading = true;

      // Format the date for required_by
      const formValue = { ...this.requirementForm.value };
      if (formValue.required_by && formValue.required_by instanceof Date) {
        formValue.required_by = toISTDateString(formValue.required_by);
      }

      // Include market price data if available
      if (this.currentMarketPrice !== null) {
        formValue.market_price_at_creation = this.currentMarketPrice;
        formValue.market_price_unit = this.selectedUnitType;
      }

      this.requirementService.createRequirement(formValue).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open('Requirement added successfully', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
            this.router.navigate(['../'], { relativeTo: this.route });
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || 'Error adding requirement',
            'Close',
            {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            }
          );
          this.isLoading = false;
        },
      });
    } else {
      // Mark all form controls as touched to trigger validation messages
      Object.keys(this.requirementForm.controls).forEach((key) => {
        this.requirementForm.get(key)?.markAsTouched();
      });
    }
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }
}

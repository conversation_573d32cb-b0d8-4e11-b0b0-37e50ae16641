// view-requirement.component.scss

.requirement-view-container {
    padding: 24px;
    
    mat-card {
      margin-bottom: 24px;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;
        
        h2 {
          margin: 0;
          font-size: 24px;
          font-weight: 500;
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
    
    .card-content {
      padding: 24px;
      
      .loading-message, .error-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        text-align: center;
        
        mat-icon {
          font-size: 48px;
          height: 48px;
          width: 48px;
          margin-bottom: 16px;
        }
        
        p {
          font-size: 18px;
          color: rgba(0, 0, 0, 0.6);
          margin-bottom: 16px;
        }
      }
      
      .error-message mat-icon {
        color: #f44336;
      }
    }
    
    .status-bar {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      
      .requirement-id {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.6);
      }
      
      .spacer {
        flex: 1;
      }
      
      .status-chip {
        padding: 6px 12px;
        border-radius: 16px;
        font-weight: 500;
        margin-right: 8px;
        
        &.status-active {
          background-color: #e3f2fd;
          color: #1565c0;
        }
        
        &.status-fulfilled {
          background-color: #e8f5e9;
          color: #2e7d32;
        }
        
        &.status-expired {
          background-color: #ffebee;
          color: #c62828;
        }
        
        &.status-cancelled {
          background-color: #f5f5f5;
          color: #616161;
        }
      }
    }
    
    .requirement-details {
      .detail-section {
        margin-bottom: 24px;
        
        h3 {
          font-size: 18px;
          margin-bottom: 16px;
          font-weight: 500;
        }
        
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          
          h3 {
            margin-bottom: 0;
          }
        }
        
        .detail-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 16px;
          
          .detail-item {
            &.full-width {
              grid-column: 1 / -1;
            }
            
            .label {
              display: block;
              font-size: 14px;
              color: rgba(0, 0, 0, 0.54);
              margin-bottom: 4px;
            }
            
            .value {
              font-size: 16px;
              
              .badge {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 4px;
                margin-left: 8px;
                font-size: 12px;
                font-weight: 500;
                
                &.Overdue {
                  background-color: #ffebee;
                  color: #c62828;
                }
                
                &.Upcoming {
                  background-color: #e8f5e9;
                  color: #2e7d32;
                }
              }
              
              .price-difference {
                font-size: 14px;
                margin-left: 4px;
                
                &.positive {
                  color: #2e7d32;
                }
                
                &.negative {
                  color: #c62828;
                }
              }
            }
          }
        }
      }
    }
    
    .no-data-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 32px;
      background-color: #f5f5f5;
      border-radius: 4px;
      
      mat-icon {
        font-size: 36px;
        height: 36px;
        width: 36px;
        color: rgba(0, 0, 0, 0.38);
        margin-bottom: 12px;
      }
      
      p {
        color: rgba(0, 0, 0, 0.6);
        margin: 0;
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .requirement-view-container {
      padding: 16px 8px;
      
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        
        .header-actions {
          width: 100%;
          
          button {
            flex: 1;
          }
        }
      }
      
      .status-bar {
        flex-wrap: wrap;
        gap: 8px;
        
        .requirement-id {
          flex: 1;
        }
      }
      
      .requirement-details {
        .detail-section {
          .section-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
            
            button {
              width: 100%;
            }
          }
        }
      }
    }
  }
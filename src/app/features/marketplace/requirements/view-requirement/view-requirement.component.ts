// view-requirement.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { RequirementService } from '../../../../core/auth/services/requirement.service';

@Component({
  selector: 'app-view-requirement',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatDividerModule,
    MatMenuModule,
    MatTooltipModule,
    MatSnackBarModule,
  ],
  templateUrl: './view-requirement.component.html',
  styleUrls: ['./view-requirement.component.scss'],
})
export class ViewRequirementComponent implements OnInit {
  requirement: any = null;
  loading = true;
  error = false;
  today = new Date();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private requirementService: RequirementService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadRequirement();
  }

  loadRequirement(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.error = true;
      this.loading = false;
      return;
    }

    this.requirementService.getRequirementById(+id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.requirement = response.data;
        } else {
          this.error = true;
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading requirement:', error);
        this.error = true;
        this.loading = false;
        this.snackBar.open(
          error.error?.message || 'Error loading requirement',
          'Close',
          { duration: 3000 }
        );
      },
    });
  }

  updateStatus(status: string): void {
    if (!this.requirement?.id) return;

    this.requirementService
      .updateRequirementStatus(this.requirement.id, status)
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open(
              `Requirement status updated to ${status}`,
              'Close',
              {
                duration: 3000,
              }
            );
            // Update the local requirement object's status
            this.requirement.status = status;
          }
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || 'Error updating status',
            'Close',
            { duration: 3000 }
          );
        },
      });
  }

  getRequirementStatus(): string {
    if (!this.requirement?.required_by) return '';

    const requiredDate = new Date(this.requirement.required_by);
    return requiredDate < this.today ? 'Overdue' : 'Upcoming';
  }

  deleteRequirement(): void {
    if (!this.requirement?.id) return;

    if (confirm('Are you sure you want to delete this requirement?')) {
      this.requirementService.deleteRequirement(this.requirement.id).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open('Requirement deleted successfully', 'Close', {
              duration: 3000,
            });
            this.router.navigate(['../../'], { relativeTo: this.route });
          }
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || 'Error deleting requirement',
            'Close',
            { duration: 3000 }
          );
        },
      });
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'active':
        return 'status-active';
      case 'fulfilled':
        return 'status-fulfilled';
      case 'expired':
        return 'status-expired';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return '';
    }
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }
}

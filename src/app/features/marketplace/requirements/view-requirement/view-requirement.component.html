<!-- view-requirement.component.html -->
<div class="requirement-view-container">
    <mat-card>
      <!-- Header -->
      <div class="card-header">
        <div class="header-left">
          <button mat-icon-button (click)="goBack()" matTooltip="Back">
            <mat-icon>arrow_back</mat-icon>
          </button>
          <h2>Requirement Details</h2>
        </div>
        <div class="header-actions">
          <button mat-raised-button color="primary" [routerLink]="['../edit', requirement?.id]" matTooltip="Edit">
            <mat-icon>edit</mat-icon> Edit
          </button>
          <button mat-raised-button color="warn" (click)="deleteRequirement()" matTooltip="Delete">
            <mat-icon>delete</mat-icon> Delete
          </button>
        </div>
      </div>
      
      <mat-divider></mat-divider>
      
      <!-- Loading/Error State -->
      <div class="card-content" *ngIf="loading">
        <div class="loading-message">
          <mat-icon>hourglass_empty</mat-icon>
          <p>Loading requirement details...</p>
        </div>
      </div>
      
      <div class="card-content" *ngIf="!loading && error">
        <div class="error-message">
          <mat-icon>error_outline</mat-icon>
          <p>Failed to load requirement details. Please try again.</p>
          <button mat-raised-button color="primary" (click)="loadRequirement()">
            <mat-icon>refresh</mat-icon> Retry
          </button>
        </div>
      </div>
      
      <!-- Requirement Details -->
      <div class="card-content" *ngIf="!loading && !error && requirement">
        <div class="status-bar">
          <div class="requirement-id">Requirement #{{requirement.id}}</div>
          <div class="spacer"></div>
          <div class="status-chip" [ngClass]="getStatusClass(requirement.status)">
            {{requirement.status | titlecase}}
          </div>
          
          <!-- Status Actions -->
          <button mat-icon-button [matMenuTriggerFor]="statusMenu" matTooltip="Update Status">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #statusMenu="matMenu">
            <button mat-menu-item (click)="updateStatus('active')" 
                    *ngIf="requirement.status !== 'active'">
              <mat-icon>play_circle</mat-icon>
              <span>Mark as Active</span>
            </button>
            <button mat-menu-item (click)="updateStatus('fulfilled')" 
                    *ngIf="requirement.status !== 'fulfilled'">
              <mat-icon>check_circle</mat-icon>
              <span>Mark as Fulfilled</span>
            </button>
            <button mat-menu-item (click)="updateStatus('expired')" 
                    *ngIf="requirement.status !== 'expired'">
              <mat-icon>event_busy</mat-icon>
              <span>Mark as Expired</span>
            </button>
            <button mat-menu-item (click)="updateStatus('cancelled')" 
                    *ngIf="requirement.status !== 'cancelled'">
              <mat-icon>cancel</mat-icon>
              <span>Mark as Cancelled</span>
            </button>
          </mat-menu>
        </div>
        
        <div class="requirement-details">
          <div class="detail-section">
            <h3>Product Details</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="label">Product</span>
                <span class="value">{{requirement.crop_name}}</span>
              </div>
              <div class="detail-item" *ngIf="requirement.variety">
                <span class="label">Variety</span>
                <span class="value">{{requirement.variety}}</span>
              </div>
              <div class="detail-item">
                <span class="label">Quantity</span>
                <span class="value">{{requirement.quantity}} {{requirement.unit_type}}</span>
              </div>
              <div class="detail-item">
                <span class="label">Price Offered</span>
                <span class="value">₹{{requirement.price_offered | number:'1.2-2'}} / {{requirement.unit_type}}</span>
              </div>
              <div class="detail-item" *ngIf="requirement.market_price">
                <span class="label">Market Price (when posted)</span>
                <span class="value">
                  ₹{{requirement.market_price | number:'1.2-2'}} / {{requirement.unit_type}}
                  <span class="price-difference" 
                        *ngIf="requirement.price_difference"
                        [ngClass]="{'positive': requirement.price_difference > 0, 'negative': requirement.price_difference < 0}">
                    ({{requirement.price_difference > 0 ? '+' : ''}}{{requirement.price_difference | number:'1.2-2'}})
                  </span>
                </span>
              </div>
            </div>
          </div>
          
          <mat-divider></mat-divider>
          
          <div class="detail-section">
            <h3>Requirement Details</h3>
            <div class="detail-grid">
              <div class="detail-item full-width">
                <span class="label">Description</span>
                <span class="value">{{requirement.description || 'No description provided'}}</span>
              </div>
              <div class="detail-item">
                <span class="label">Required By</span>
                <span class="value">{{requirement.required_by | date:'mediumDate'}}
                  <span class="badge" *ngIf="requirement.status === 'active'">
                    {{ requirement?.required_by ? getRequirementStatus() : '' }}
                  </span>
                </span>
              </div>
              <div class="detail-item">
                <span class="label">Created By</span>
                <span class="value">{{requirement.created_by_name}}</span>
              </div>
              <div class="detail-item">
                <span class="label">Created Date</span>
                <span class="value">{{requirement.created_at | date:'medium'}}</span>
              </div>
              <div class="detail-item" *ngIf="requirement.updated_by_name">
                <span class="label">Last Updated By</span>
                <span class="value">{{requirement.updated_by_name}}</span>
              </div>
              <div class="detail-item" *ngIf="requirement.updated_at !== requirement.created_at">
                <span class="label">Last Updated</span>
                <span class="value">{{requirement.updated_at | date:'medium'}}</span>
              </div>
            </div>
          </div>
          
          <mat-divider></mat-divider>
          
          <!-- Available Produce Section -->
          <div class="detail-section">
            <div class="section-header">
              <h3>Available Matching Produce</h3>
              <button mat-raised-button color="primary" *ngIf="requirement.status === 'active'">
                <mat-icon>search</mat-icon> Find Matching Produce
              </button>
            </div>
            
            <!-- Placeholder for available produce list -->
            <div class="no-data-message">
              <mat-icon>inventory_2</mat-icon>
              <p>No matching produce found</p>
            </div>
          </div>
        </div>
      </div>
    </mat-card>
  </div>
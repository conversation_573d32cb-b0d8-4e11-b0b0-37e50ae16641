import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-requirements',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './requirements.component.html',
  styleUrls: ['./requirements.component.scss'],
})
export class RequirementsComponent {
  // This is a container component with just a router-outlet
  // No additional logic needed here
}

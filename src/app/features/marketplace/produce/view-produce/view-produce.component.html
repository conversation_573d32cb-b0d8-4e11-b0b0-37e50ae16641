<div class="produce-detail-container">
    <mat-card>
      <!-- Header -->
      <div class="card-header">
        <div class="header-title">
          <button mat-icon-button (click)="goBack()" matTooltip="Go Back">
            <mat-icon>arrow_back</mat-icon>
          </button>
          <h2>Produce Listing #{{produce?.id}}</h2>
        </div>
        <div class="header-actions" *ngIf="produce">
          <button mat-raised-button color="primary" [matMenuTriggerFor]="statusMenu" 
                  *ngIf="produce.status !== 'sold' && produce.status !== 'expired'">
            <mat-icon>update</mat-icon> Update Status
          </button>
          <mat-menu #statusMenu="matMenu">
            <button mat-menu-item (click)="updateStatus('available')" 
                   *ngIf="produce.status !== 'available'">
              <mat-icon>check_circle</mat-icon>
              <span>Mark as Available</span>
            </button>
            <button mat-menu-item (click)="updateStatus('pending')" 
                   *ngIf="produce.status !== 'pending'">
              <mat-icon>hourglass_empty</mat-icon>
              <span>Mark as Pending</span>
            </button>
            <button mat-menu-item (click)="updateStatus('sold')" 
                   *ngIf="produce.status !== 'sold'">
              <mat-icon>shopping_cart</mat-icon>
              <span>Mark as Sold</span>
            </button>
            <button mat-menu-item (click)="updateStatus('expired')" 
                   *ngIf="produce.status !== 'expired'">
              <mat-icon>event_busy</mat-icon>
              <span>Mark as Expired</span>
            </button>
          </mat-menu>
        </div>
      </div>
  
      <!-- Loading spinner -->
      <div class="loading-container" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
  
      <!-- Error message -->
      <div class="error-container" *ngIf="error && !isLoading">
        <mat-icon color="warn">error</mat-icon>
        <p>{{error}}</p>
        <button mat-raised-button color="primary" (click)="loadProduce()">
          <mat-icon>refresh</mat-icon> Try Again
        </button>
      </div>
  
      <!-- Produce content -->
      <div class="card-content" *ngIf="produce && !isLoading">
        <mat-tab-group animationDuration="0ms">
          <!-- Overview Tab -->
          <mat-tab label="Overview">
            <div class="overview-container">
              <!-- Produce Status -->
              <div class="produce-header">
                <div class="status-section">
                  <div class="status-chip" [ngClass]="getStatusClass(produce.status)">
                    {{produce.status | titlecase}}
                  </div>
                  <div class="produce-date">
                    Listed on: {{formatDate(produce.created_at)}}
                  </div>
                </div>
              </div>
  
              <mat-divider></mat-divider>
  
              <!-- Farmer Section -->
              <div class="detail-section">
                <div class="section-header">
                  <h3>Farmer Information</h3>
                  <button mat-stroked-button color="primary" (click)="viewFarmerProfile(produce.farmer_id)">
                    <mat-icon>person</mat-icon> View Farmer
                  </button>
                </div>
                <div class="detail-grid">
                  <div class="detail-item">
                    <span class="label">Name</span>
                    <span class="value">{{produce.farmer_name}}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Farmer ID</span>
                    <span class="value">{{produce.farmer_id}}</span>
                  </div>
                  <div class="detail-item" *ngIf="produce.farmer_mobile">
                    <span class="label">Mobile</span>
                    <span class="value">{{produce.farmer_mobile}}</span>
                  </div>
                </div>
              </div>
  
              <mat-divider></mat-divider>
  
              <!-- Produce Section -->
              <div class="detail-section">
                <div class="section-header">
                  <h3>Produce Details</h3>
                </div>
                <div class="produce-container">
                  <div class="produce-image" *ngIf="produce.primary_image_url">
                    <img [src]="getFullImageUrl(produce.primary_image_url)" 
                         (click)="viewImage(produce.primary_image_url)"
                         (error)="handleImageError($event)"
                         alt="{{produce.crop_name}}">
                  </div>
                  <div class="produce-details">
                    <div class="detail-grid">
                      <div class="detail-item full-width">
                        <span class="label">Crop</span>
                        <span class="value">{{produce.crop_name}}</span>
                      </div>
                      <div class="detail-item" *ngIf="produce.variety">
                        <span class="label">Variety</span>
                        <span class="value">{{produce.variety}}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">Quantity</span>
                        <span class="value">{{produce.quantity}} {{produce.unit_type}}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">Available From</span>
                        <span class="value">{{formatDate(produce.available_from)}}</span>
                      </div>
                      <div class="detail-item" *ngIf="produce.available_until">
                        <span class="label">Available Until</span>
                        <span class="value">{{formatDate(produce.available_until)}}</span>
                      </div>
                      <div class="detail-item" *ngIf="produce.harvest_date">
                        <span class="label">Harvest Date</span>
                        <span class="value">{{formatDate(produce.harvest_date)}}</span>
                      </div>
                      <div class="detail-item" *ngIf="produce.interest_count !== undefined">
                        <span class="label">Interest Count</span>
                        <span class="value">{{produce.interest_count}} interested parties</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
  
              <mat-divider></mat-divider>
  
              <!-- Description Section -->
              <div class="detail-section" *ngIf="produce.description">
                <div class="section-header">
                  <h3>Description</h3>
                </div>
                <div class="description-container">
                  <p>{{produce.description}}</p>
                </div>
              </div>
  
              <mat-divider *ngIf="produce.description"></mat-divider>
  
              <!-- Additional Images Section -->
              <div class="detail-section" *ngIf="produce.secondary_image_url || produce.tertiary_image_url">
                <div class="section-header">
                  <h3>Additional Images</h3>
                </div>
                <div class="additional-images">
                  <div class="image-item" *ngIf="produce.secondary_image_url">
                    <img [src]="getFullImageUrl(produce.secondary_image_url)" 
                         (click)="viewImage(produce.secondary_image_url)"
                         (error)="handleImageError($event)"
                         alt="Secondary image">
                    <p>Secondary Image</p>
                  </div>
                  <div class="image-item" *ngIf="produce.tertiary_image_url">
                    <img [src]="getFullImageUrl(produce.tertiary_image_url)" 
                         (click)="viewImage(produce.tertiary_image_url)"
                         (error)="handleImageError($event)"
                         alt="Tertiary image">
                    <p>Tertiary Image</p>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
  
          <!-- Interests Tab (if there's interest data) -->
          <mat-tab label="Interests" *ngIf="produce?.interests?.length">
            <div class="interests-container">
              <h3>Interested Parties</h3>
              <div class="interests-list">
                <div class="interest-item" *ngFor="let interest of produce.interests">
                  <!-- Interest details would go here -->
                  <p>Interest details would be displayed here if available</p>
                </div>
                
                <div class="no-interests" *ngIf="!produce.interests?.length">
                  <p>No interests recorded for this produce listing yet.</p>
                </div>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </mat-card>
  </div>
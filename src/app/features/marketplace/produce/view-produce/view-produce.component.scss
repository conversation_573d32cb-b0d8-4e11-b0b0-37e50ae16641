.produce-detail-container {
    padding: 24px;
  
    mat-card {
      overflow: hidden;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
  
      .header-title {
        display: flex;
        align-items: center;
        gap: 8px;
  
        h2 {
          margin: 0;
          font-size: 24px;
          font-weight: 500;
        }
      }
  
      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 48px 0;
    }
  
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 48px 0;
      text-align: center;
  
      mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
      }
  
      p {
        margin-bottom: 24px;
        font-size: 16px;
        color: #e53935;
      }
    }
  
    .card-content {
      padding: 24px;
  
      .overview-container {
        padding: 16px 0;
  
        .produce-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;
  
          .status-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
  
            .status-chip {
              padding: 6px 12px;
              border-radius: 16px;
              display: inline-block;
              text-align: center;
              min-width: 100px;
              font-size: 14px;
              font-weight: 500;
              
              &.status-available {
                background-color: #e8f5e9;
                color: #388e3c;
              }
              
              &.status-pending {
                background-color: #fff8e1;
                color: #ffa000;
              }
              
              &.status-sold {
                background-color: #e3f2fd;
                color: #1976d2;
              }
              
              &.status-expired {
                background-color: #f5f5f5;
                color: #757575;
              }
            }
  
            .produce-date {
              font-size: 14px;
              color: #757575;
            }
          }
        }
      }
  
      .detail-section {
        margin: 24px 0;
  
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
  
          h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
          }
        }
  
        .detail-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 16px;
  
          .detail-item {
            &.full-width {
              grid-column: 1 / -1;
            }
  
            .label {
              display: block;
              font-size: 12px;
              color: #757575;
              margin-bottom: 4px;
            }
  
            .value {
              font-size: 16px;
              font-weight: 500;
            }
          }
        }
  
        .produce-container {
          display: flex;
          gap: 24px;
  
          .produce-image {
            flex: 0 0 200px;
  
            img {
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 8px;
              cursor: pointer;
              transition: transform 0.2s;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
              &:hover {
                transform: scale(1.05);
              }
            }
          }
  
          .produce-details {
            flex: 1;
          }
        }
  
        .description-container {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 16px;
          border-left: 4px solid #388e3c;
  
          p {
            margin: 0;
            white-space: pre-line;
          }
        }
  
        .additional-images {
          display: flex;
          flex-wrap: wrap;
          gap: 24px;
          margin-top: 16px;
  
          .image-item {
            flex: 0 0 calc(50% - 12px);
            max-width: 300px;
            text-align: center;
  
            img {
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 8px;
              cursor: pointer;
              transition: transform 0.2s;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
              &:hover {
                transform: scale(1.05);
              }
            }
  
            p {
              margin-top: 8px;
              font-size: 14px;
              color: #757575;
            }
          }
        }
      }
  
      .interests-container {
        padding: 16px 0;
  
        h3 {
          margin-top: 0;
          margin-bottom: 16px;
          font-size: 18px;
          font-weight: 500;
        }
  
        .interests-list {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 16px;
  
          .interest-item {
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e0e0e0;
  
            &:last-child {
              margin-bottom: 0;
              padding-bottom: 0;
              border-bottom: none;
            }
          }
  
          .no-interests {
            text-align: center;
            padding: 24px;
            color: #757575;
            font-style: italic;
          }
        }
      }
    }
  }
  
  // Styling for mat-tabs to match your application's design
  ::ng-deep {
    .mat-mdc-tab-header {
      margin-bottom: 16px;
    }
  
    .mat-mdc-tab-body-content {
      overflow: visible;
    }
  
    // Make dividers more subtle
    .mat-divider {
      margin: 16px 0;
    }
  }
  
  // Add print-friendly styles
  @media print {
    .produce-detail-container {
      padding: 0;
  
      mat-card {
        box-shadow: none;
      }
  
      .card-header {
        .header-actions {
          display: none;
        }
      }
  
      // Hide the tabs navigation
      ::ng-deep {
        .mat-mdc-tab-header {
          display: none;
        }
  
        // Show all tab content when printing
        .mat-mdc-tab-body {
          display: block !important;
          opacity: 1 !important;
        }
  
        // Hide buttons when printing
        button {
          display: none;
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .produce-detail-container {
      padding: 16px 8px;
  
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
  
        .header-actions {
          width: 100%;
        }
      }
  
      .card-content {
        padding: 16px;
  
        .overview-container {
          .produce-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
          }
        }
  
        .detail-section {
          .section-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
  
          .produce-container {
            flex-direction: column;
  
            .produce-image {
              flex: 0 0 auto;
              width: 100%;
  
              img {
                height: 180px;
              }
            }
          }
  
          .detail-grid {
            grid-template-columns: 1fr;
          }
  
          .additional-images {
            .image-item {
              flex: 0 0 100%;
              max-width: 100%;
            }
          }
        }
      }
    }
  }
  
  // For extra small screens
  @media (max-width: 480px) {
    .produce-detail-container {
      padding: 8px 4px;
  
      .card-header {
        padding: 12px 16px;
  
        .header-title {
          h2 {
            font-size: 20px;
          }
        }
      }
  
      .card-content {
        padding: 12px;
  
        .detail-section {
          margin: 16px 0;
  
          .section-header {
            h3 {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
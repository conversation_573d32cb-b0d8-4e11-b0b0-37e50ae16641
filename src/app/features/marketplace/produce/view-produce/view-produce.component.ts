import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { FarmerProduceService } from '../../../../core/auth/services/farmer-produce.service';
import { ImageViewerComponent } from '../../../../components/image-viewer.component';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-view-produce',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatTabsModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatMenuModule,
    MatDialogModule,
  ],
  templateUrl: './view-produce.component.html',
  styleUrls: ['./view-produce.component.scss'],
})
export class ViewProduceComponent implements OnInit {
  produce: any = null;
  isLoading = true;
  error: string | null = null;
  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private produceService: FarmerProduceService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadProduce();
  }

  loadProduce(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.error = 'Produce ID is required';
      this.isLoading = false;
      return;
    }

    this.produceService.getProduceById(parseInt(id)).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.produce = response.data;
        } else {
          this.error = 'Failed to load produce details';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.error = error.message || 'An error occurred while loading produce';
        this.isLoading = false;
      },
    });
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return 'assets/images/placeholder-image.png';
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    event.target.src = 'assets/images/placeholder-image.png';
  }

  viewImage(imageUrl: string): void {
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl: this.getFullImageUrl(imageUrl) },
    });
  }

  goBack(): void {
    this.router.navigate(['/marketplace/produce']);
  }

  viewFarmerProfile(farmerId: number): void {
    this.router.navigate(['/farmers/view', farmerId]);
  }

  updateStatus(newStatus: string): void {
    if (!this.produce) return;

    this.produceService
      .updateProduceStatus(this.produce.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open(
              `Produce status updated to ${newStatus}`,
              'Close',
              {
                duration: 3000,
              }
            );
            this.loadProduce(); // Reload to get updated data
          } else {
            this.snackBar.open('Failed to update produce status', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error) => {
          this.snackBar.open(
            error.message || 'Error updating produce status',
            'Close',
            {
              duration: 3000,
            }
          );
        },
      });
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'available':
        return 'status-available';
      case 'pending':
        return 'status-pending';
      case 'sold':
        return 'status-sold';
      case 'expired':
        return 'status-expired';
      default:
        return '';
    }
  }

  formatDate(date: string | null): string {
    if (!date) return 'Not specified';
    return new Date(date).toLocaleDateString();
  }
}

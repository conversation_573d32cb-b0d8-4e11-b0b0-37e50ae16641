// src/app/features/marketplace/produce/list-produce/link-produce-dialog.component.ts
import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';

export interface LinkProduceDialogData {
  produce: any;
  requirements?: any[];
  allRequirements?: any[];
  createNewRequirement?: boolean;
}

@Component({
  selector: 'app-link-produce-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
  ],
  templateUrl: './link-produce-dialog.component.html',
  styleUrls: ['./link-produce-dialog.component.scss'],
})
export class LinkProduceDialogComponent {
  linkForm: FormGroup;
  newRequirementForm: FormGroup;
  showAllRequirements = false;
  otherRequirements: any[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: LinkProduceDialogData,
    public dialogRef: MatDialogRef<LinkProduceDialogComponent>,
    private fb: FormBuilder
  ) {
    this.linkForm = this.fb.group({
      requirementId: ['', Validators.required],
      quantity: [
        data.produce.quantity,
        [
          Validators.required,
          Validators.min(0.01),
          Validators.max(data.produce.quantity),
        ],
      ],
      notes: [''],
    });

    this.newRequirementForm = this.fb.group({
      quantity: [
        data.produce.quantity,
        [
          Validators.required,
          Validators.min(0.01),
          Validators.max(data.produce.quantity),
        ],
      ],
      unit_type: [data.produce.unit_type, Validators.required],
      price_offered: ['', [Validators.required, Validators.min(0.01)]],
      required_by: [this.getDateInNextMonth(), Validators.required],
      description: ['', Validators.required],
    });

    // Filter out requirements that are already in the matching list
    if (data.allRequirements && data.requirements) {
      const matchingIds = data.requirements.map((req: any) => req.id);
      this.otherRequirements = data.allRequirements.filter(
        (req: any) => !matchingIds.includes(req.id)
      );
      this.showAllRequirements = this.otherRequirements.length > 0;
    }
  }

  getDateInNextMonth(): Date {
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    return date;
  }

  formatDate(date: string): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  }

  close(): void {
    this.dialogRef.close();
  }

  submit(): void {
    if (this.data.createNewRequirement) {
      if (this.newRequirementForm.valid) {
        // Create a new requirement object
        const requirementData = {
          ...this.newRequirementForm.value,
          // Include product_id from the produce
          product_id: this.data.produce.product_id,
        };

        this.dialogRef.close({ requirementData });
      }
    } else {
      if (this.linkForm.valid) {
        this.dialogRef.close({
          produceId: this.data.produce.id,
          requirementId: this.linkForm.value.requirementId,
          quantity: this.linkForm.value.quantity,
          notes: this.linkForm.value.notes,
        });
      }
    }
  }
}

.produce-listings-container {
    padding: 24px;
    
    mat-card {
      overflow: hidden;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        button {
          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }
    
    .filter-container {
      padding: 16px 24px;
      border-bottom: 1px solid #e0e0e0;
      
      .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;
        
        mat-form-field {
          flex: 1;
          min-width: 200px;
        }
        
        .filter-buttons {
          display: flex;
          gap: 8px;
        }
      }
    }
    
    .table-container {
      position: relative;
      min-height: 400px;
      overflow: auto;
      
      .loading-shade {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .produce-table {
        width: 100%;
        
        .mat-column-actions {
          width: 150px;
          text-align: right;
        }
        
        .mat-column-status {
          width: 120px;
        }
        
        .mat-column-id {
          width: 60px;
        }
        
        .mat-column-quantity {
          width: 120px;
        }
  
        .mat-column-price {
          width: 150px;
        }
  
        .mat-column-select {
          width: 50px;
          padding-right: 0;
        }
        
        .status-chip {
          padding: 4px 8px;
          border-radius: 16px;
          display: inline-block;
          text-align: center;
          min-width: 80px;
          font-size: 12px;
          font-weight: 500;
          
          &.status-available {
            background-color: #e8f5e9;
            color: #388e3c;
          }
          
          &.status-pending {
            background-color: #fff8e1;
            color: #ffa000;
          }
          
          &.status-sold {
            background-color: #e3f2fd;
            color: #1976d2;
          }
          
          &.status-expired {
            background-color: #f5f5f5;
            color: #757575;
          }
        }
        
        .mat-mdc-row:hover {
          background-color: rgba(0, 0, 0, 0.04);
        }
        
        .farmer-info {
          display: flex;
          align-items: center;
          
          .farmer-name {
            font-weight: 500;
            margin-right: 8px;
          }
        }
        
        .produce-details {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          
          .produce-image-container {
            flex-shrink: 0;
            width: 50px;
            height: 50px;
            
            .produce-thumbnail {
              width: 50px;
              height: 50px;
              object-fit: cover;
              border-radius: 4px;
              cursor: pointer;
              transition: transform 0.2s;
              
              &:hover {
                transform: scale(1.1);
              }
            }
          }
          
          .produce-info {
            display: flex;
            flex-direction: column;
            
            .produce-name {
              font-weight: 500;
            }
            
            .produce-variety {
              font-size: 12px;
              color: #757575;
              font-style: italic;
            }
            
            .produce-description {
              font-size: 12px;
              color: #757575;
              margin-top: 4px;
            }
  
            .matching-requirements, .interest-count {
              margin-top: 4px;
              
              .badge {
                font-size: 11px;
                background-color: #f0f4c3;
                color: #33691e;
                padding: 2px 6px;
                border-radius: 10px;
                display: inline-block;
                
                &.badge-info {
                  background-color: #e1f5fe;
                  color: #0277bd;
                }
              }
            }
          }
        }
        
        .date-info {
          font-size: 14px;
          
          div {
            margin-bottom: 4px;
          }
        }
  
        .price-info {
          .current-price {
            font-weight: 500;
            color: #2e7d32;
            font-size: 15px;
          }
          
          .price-date {
            font-size: 11px;
            color: #757575;
            margin-top: 2px;
          }
        }
      }
      
      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 32px;
        color: rgba(0, 0, 0, 0.54);
        
        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          margin-bottom: 16px;
        }
      }
    }
  }
  
  // Make table layout more compact
  ::ng-deep {
    .mat-mdc-table {
      .mat-mdc-row, .mat-mdc-header-row {
        height: 52px; // Slightly increased height for the added content
      }
      
      .mat-mdc-cell, .mat-mdc-header-cell {
        padding: 0 12px; // Reduced horizontal padding
      }
    }
    
    // Ensure proper alignment for paginator
    .mat-mdc-paginator {
      border-top: 1px solid rgba(0, 0, 0, 0.12);
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .produce-listings-container {
      padding: 16px 8px;
      
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        
        .header-actions {
          width: 100%;
        }
      }
      
      .filter-container {
        padding: 16px;
        
        .filter-row {
          flex-direction: column;
          align-items: stretch;
          
          .filter-buttons {
            margin-top: 8px;
          }
        }
      }
    }
  }
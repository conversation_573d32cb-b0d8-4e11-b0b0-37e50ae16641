import { Compo<PERSON>, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router, RouterModule } from '@angular/router';

import { CropService } from '../../../../core/auth/services/masters/crop.service';
import { ImageViewerComponent } from '../../../../components/image-viewer.component';
import { environment } from '../../../../../environments/environment';
import { FarmerProduceService } from '../../../../core/auth/services/farmer-produce.service';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { LinkProduceDialogComponent } from './link-produce-dialog.component';
import { SelectionModel } from '@angular/cdk/collections';
import { FarmerService } from '../../../../core/auth/services/farmer.service';
import { RequirementService } from '../../../../core/auth/services/requirement.service';
import { MatCheckboxModule } from '@angular/material/checkbox';

interface FarmerProduce {
  id: number;
  farmer_id: number;
  product_id: number;
  crop_name: string;
  variety?: string;
  quantity: number;
  unit_type: string;
  description?: string;
  primary_image_url?: string;
  secondary_image_url?: string;
  tertiary_image_url?: string;
  harvest_date?: string;
  available_from: string;
  available_until?: string;
  status: string;
  created_at: string;
  farmer_name: string;
  market_price?: {
    price_per_unit: number;
    unit_type: string;
    price_date: string;
  };
  matching_requirements_count?: number;
  interest_count?: number;
}

@Component({
  selector: 'app-list-produce',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatMenuModule,
    MatDialogModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
  ],
  templateUrl: './list-produce.component.html',
  styleUrls: ['./list-produce.component.scss'],
})
export class ListProduceComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'id',
    'farmer',
    'produce_details',
    'price',
    'quantity',
    'available_date',
    'status',
    'actions',
  ];

  dataSource = new MatTableDataSource<FarmerProduce>([]);
  selection = new SelectionModel<FarmerProduce>(true, []);
  filterForm: FormGroup;
  isLoading = false;
  crops: any[] = [];
  farmers: any[] = [];
  activeRequirements: any[] = [];

  statusOptions = [
    { value: 'available', label: 'Available' },
    { value: 'pending', label: 'Pending' },
    { value: 'sold', label: 'Sold' },
    { value: 'expired', label: 'Expired' },
  ];

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private produceService: FarmerProduceService,
    private cropService: CropService,
    private farmerService: FarmerService,
    private requirementService: RequirementService,
    private fb: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {
    this.filterForm = this.fb.group({
      farmer_id: [''],
      product_id: [''],
      status: [''],
      available_on: [''],
    });
  }

  ngOnInit(): void {
    this.loadCrops();
    this.loadFarmers();
    this.loadActiveRequirements();
    this.loadProduce();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadCrops(): void {
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.crops = response.data.filter((crop: any) => crop.is_active);
        }
      },
      error: (error) => {
        console.error('Error loading crops:', error);
      },
    });
  }

  loadFarmers(): void {
    this.farmerService.getAllFarmers().subscribe({
      next: (farmers) => {
        this.farmers = farmers;
      },
      error: (error) => {
        console.error('Error loading farmers:', error);
      },
    });
  }

  loadActiveRequirements(): void {
    this.requirementService.getActiveRequirements().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.activeRequirements = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading active requirements:', error);
      },
    });
  }

  loadProduce(): void {
    this.isLoading = true;
    const filters = this.filterForm.value;

    // Use the enhanced endpoint to get price and requirement info
    this.produceService.getProduceWithDetails(filters).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.dataSource.data = response.data;
        } else {
          this.snackBar.open('Failed to load produce listings', 'Close', {
            duration: 3000,
          });
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading produce listings:', error);
        this.snackBar.open(
          error.message || 'Failed to load produce listings',
          'Close',
          { duration: 3000 }
        );
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    this.loadProduce();
  }

  resetFilter(): void {
    this.filterForm.reset();
    this.loadProduce();
  }

  viewProduceDetails(produceId: number): void {
    if (produceId) {
      this.router.navigate(['/marketplace/produce/view', produceId]);
    }
  }

  updateProduceStatus(produceId: number, newStatus: string): void {
    this.produceService.updateProduceStatus(produceId, newStatus).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.snackBar.open(
            `Produce status updated to ${newStatus}`,
            'Close',
            { duration: 3000 }
          );
          this.loadProduce();
        } else {
          this.snackBar.open('Failed to update produce status', 'Close', {
            duration: 3000,
          });
        }
      },
      error: (error) => {
        console.error('Error updating produce status:', error);
        this.snackBar.open(
          error.message || 'Failed to update produce status',
          'Close',
          { duration: 3000 }
        );
      },
    });
  }

  batchUpdateStatus(status: string): void {
    if (this.selection.isEmpty()) {
      this.snackBar.open('Please select at least one produce item', 'Close', {
        duration: 3000,
      });
      return;
    }

    const produceIds = this.selection.selected.map((item) => item.id);

    this.produceService.batchUpdateStatus(produceIds, status).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.snackBar.open(
            `${response.data.affected} items updated to ${status}`,
            'Close',
            { duration: 3000 }
          );
          this.loadProduce();
        } else {
          this.snackBar.open('Failed to update items', 'Close', {
            duration: 3000,
          });
        }
      },
      error: (error) => {
        console.error('Error batch updating status:', error);
        this.snackBar.open(error.message || 'Failed to update items', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  exportProduce(): void {
    // Implement export functionality if needed
    this.snackBar.open('Export feature coming soon', 'Close', {
      duration: 3000,
    });
  }

  viewImage(imageUrl: string): void {
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl: this.getFullImageUrl(imageUrl) },
    });
  }

  getFullImageUrl(imageUrl: string | null | undefined): string {
    if (!imageUrl) return 'assets/images/placeholder-image.png';
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    event.target.src = 'assets/images/placeholder-image.png';
  }

  viewFarmerProfile(farmerId: number): void {
    if (farmerId) {
      this.router.navigate(['/farmers/view', farmerId]);
    }
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'available':
        return 'status-available';
      case 'pending':
        return 'status-pending';
      case 'sold':
        return 'status-sold';
      case 'expired':
        return 'status-expired';
      default:
        return '';
    }
  }

  formatDate(date: string): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  }

  formatPrice(price: number | undefined): string {
    return price ? `₹${price.toFixed(2)}` : 'N/A';
  }

  // For table selection
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.filter(
      (row) => row.status === 'available'
    ).length;
    return numSelected === numRows;
  }

  masterToggle() {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.dataSource.data
        .filter((row) => row.status === 'available')
        .forEach((row) => this.selection.select(row));
    }
  }

  checkboxLabel(row?: FarmerProduce): string {
    if (!row) {
      return `${this.isAllSelected() ? 'deselect' : 'select'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${
      row.id
    }`;
  }

  canSelect(row: FarmerProduce): boolean {
    return row.status === 'available';
  }

  // Connect produce to requirement
  openLinkProduceDialog(produce: FarmerProduce): void {
    // First, get matching requirements for this produce
    this.produceService.getMatchingRequirements(produce.id).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          const matchingRequirements = response.data.matching_requirements;

          // Open dialog with matching requirements
          const dialogRef = this.dialog.open(LinkProduceDialogComponent, {
            width: '600px',
            data: {
              produce: produce,
              requirements: matchingRequirements,
              allRequirements: this.activeRequirements,
            },
          });

          dialogRef.afterClosed().subscribe((result) => {
            if (result) {
              this.createTransaction(
                result.produceId,
                result.requirementId,
                result.quantity,
                result.notes
              );
            }
          });
        } else {
          this.snackBar.open('Failed to fetch matching requirements', 'Close', {
            duration: 3000,
          });
        }
      },
      error: (error) => {
        console.error('Error fetching matching requirements:', error);
        this.snackBar.open(
          error.message || 'Failed to fetch matching requirements',
          'Close',
          { duration: 3000 }
        );
      },
    });
  }

  createTransaction(
    produceId: number,
    requirementId: number,
    quantity: number,
    notes: string
  ): void {
    this.produceService
      .createTransactionFromProduce(produceId, requirementId, quantity, notes)
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open('Transaction created successfully', 'Close', {
              duration: 3000,
            });
            this.loadProduce(); // Refresh the list
          } else {
            this.snackBar.open('Failed to create transaction', 'Close', {
              duration: 3000,
            });
          }
        },
        error: (error) => {
          console.error('Error creating transaction:', error);
          this.snackBar.open(
            error.message || 'Failed to create transaction',
            'Close',
            { duration: 3000 }
          );
        },
      });
  }

  createRequirementFromProduce(produce: FarmerProduce): void {
    // Open dialog for creating a new requirement
    const dialogRef = this.dialog.open(LinkProduceDialogComponent, {
      width: '600px',
      data: {
        produce: produce,
        createNewRequirement: true,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.produceService
          .createRequirementFromProduce(produce.id, result.requirementData)
          .subscribe({
            next: (response) => {
              if (response.status === 'success') {
                this.snackBar.open(
                  'Requirement created and produce linked successfully',
                  'Close',
                  {
                    duration: 3000,
                  }
                );
                this.loadProduce(); // Refresh the list
                this.loadActiveRequirements(); // Refresh requirements list
              } else {
                this.snackBar.open('Failed to create requirement', 'Close', {
                  duration: 3000,
                });
              }
            },
            error: (error) => {
              console.error('Error creating requirement:', error);
              this.snackBar.open(
                error.message || 'Failed to create requirement',
                'Close',
                { duration: 3000 }
              );
            },
          });
      }
    });
  }
}

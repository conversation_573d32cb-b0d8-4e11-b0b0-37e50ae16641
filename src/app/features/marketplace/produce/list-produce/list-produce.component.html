<div class="produce-listings-container">
    <mat-card>
      <div class="card-header">
        <h2>Farmer Produce Listings</h2>
        <div class="header-actions">
          <button mat-raised-button color="primary" *ngIf="selection.selected.length > 0" [matMenuTriggerFor]="batchMenu">
            <mat-icon>assignment</mat-icon> Batch Actions ({{selection.selected.length}})
          </button>
          <mat-menu #batchMenu="matMenu">
            <button mat-menu-item (click)="batchUpdateStatus('pending')">
              <mat-icon>hourglass_empty</mat-icon>
              <span>Mark as Pending</span>
            </button>
            <button mat-menu-item (click)="batchUpdateStatus('sold')">
              <mat-icon>shopping_cart</mat-icon>
              <span>Mark as Sold</span>
            </button>
            <button mat-menu-item (click)="batchUpdateStatus('expired')">
              <mat-icon>event_busy</mat-icon>
              <span>Mark as Expired</span>
            </button>
          </mat-menu>
          <button mat-raised-button color="accent" (click)="exportProduce()">
            <mat-icon>download</mat-icon> Export to Excel
          </button>
        </div>
      </div>
  
      <!-- Filter Form -->
      <div class="filter-container">
        <form [formGroup]="filterForm" (ngSubmit)="applyFilter()">
          <div class="filter-row">
            <mat-form-field appearance="outline">
              <mat-label>Farmer</mat-label>
              <mat-select formControlName="farmer_id">
                <mat-option [value]="">All Farmers</mat-option>
                <mat-option *ngFor="let farmer of farmers" [value]="farmer.id">
                  {{farmer.full_name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Crop</mat-label>
              <mat-select formControlName="product_id">
                <mat-option [value]="">All Crops</mat-option>
                <mat-option *ngFor="let crop of crops" [value]="crop.id">
                  {{crop.crop_name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select formControlName="status">
                <mat-option [value]="">All Statuses</mat-option>
                <mat-option *ngFor="let option of statusOptions" [value]="option.value">
                  {{option.label}}
                </mat-option>
              </mat-select>
            </mat-form-field>
  
            <mat-form-field appearance="outline">
              <mat-label>Available On</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="available_on">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
  
            <div class="filter-buttons">
              <button mat-raised-button color="primary" type="submit">
                <mat-icon>search</mat-icon> Filter
              </button>
              <button mat-stroked-button color="warn" type="button" (click)="resetFilter()">
                <mat-icon>clear</mat-icon> Reset
              </button>
            </div>
          </div>
        </form>
      </div>
  
      <!-- Produce Listings Table -->
      <div class="table-container">
        <div *ngIf="isLoading" class="loading-shade">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
  
        <table mat-table [dataSource]="dataSource" matSort class="produce-table">
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox (change)="$event ? masterToggle() : null"
                            [checked]="selection.hasValue() && isAllSelected()"
                            [indeterminate]="selection.hasValue() && !isAllSelected()"
                            [aria-label]="checkboxLabel()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox (click)="$event.stopPropagation()"
                            (change)="$event ? selection.toggle(row) : null"
                            [checked]="selection.isSelected(row)"
                            [aria-label]="checkboxLabel(row)"
                            [disabled]="!canSelect(row)">
              </mat-checkbox>
            </td>
          </ng-container>
  
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
            <td mat-cell *matCellDef="let produce"> {{produce.id}} </td>
          </ng-container>
  
          <!-- Farmer Info Column -->
          <ng-container matColumnDef="farmer">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Farmer </th>
            <td mat-cell *matCellDef="let produce">
              <div class="farmer-info">
                <div class="farmer-name">{{produce.farmer_name}}</div>
                <button mat-icon-button color="primary" matTooltip="View Farmer Profile" 
                        (click)="viewFarmerProfile(produce.farmer_id)">
                  <mat-icon>person</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>
  
          <!-- Produce Details Column -->
          <ng-container matColumnDef="produce_details">
            <th mat-header-cell *matHeaderCellDef> Produce Details </th>
            <td mat-cell *matCellDef="let produce">
              <div class="produce-details">
                <div class="produce-image-container" *ngIf="produce.primary_image_url">
                  <img [src]="getFullImageUrl(produce.primary_image_url)" 
                      (click)="viewImage(produce.primary_image_url)" 
                      class="produce-thumbnail" 
                      alt="{{produce.crop_name}}"
                      (error)="handleImageError($event)">
                </div>
                <div class="produce-info">
                  <div class="produce-name">{{produce.crop_name}}</div>
                  <div class="produce-variety" *ngIf="produce.variety">{{produce.variety}}</div>
                  <div class="produce-description" *ngIf="produce.description">
                    <small>{{produce.description | slice:0:50}}{{produce.description.length > 50 ? '...' : ''}}</small>
                  </div>
                  <div class="matching-requirements" *ngIf="produce.matching_requirements_count && produce.matching_requirements_count > 0">
                    <span class="badge" matTooltip="Number of active requirements that match this produce">
                      {{produce.matching_requirements_count}} matching requirements
                    </span>
                  </div>
                  <div class="interest-count" *ngIf="produce.interest_count && produce.interest_count > 0">
                    <span class="badge badge-info" matTooltip="Number of interests expressed in this produce">
                      {{produce.interest_count}} interests
                    </span>
                  </div>
                </div>
              </div>
            </td>
          </ng-container>
  
          <!-- Price Column -->
          <ng-container matColumnDef="price">
            <th mat-header-cell *matHeaderCellDef> Market Price </th>
            <td mat-cell *matCellDef="let produce">
                <div *ngIf="produce.market_price" class="price-info">
                    <div class="current-price">₹{{produce.market_price.price_per_unit | number:'1.2-2'}}/{{produce.market_price.unit_type}}</div>
                    <div class="price-date">as of {{produce.market_price.price_date | date}}</div>
                  </div>
                  <div *ngIf="!produce.market_price" class="price-info">
                    <div class="current-price">N/A</div>
                  </div>
            </td>
          </ng-container>
  
          <!-- Quantity Column -->
          <ng-container matColumnDef="quantity">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Quantity </th>
            <td mat-cell *matCellDef="let produce">
              {{produce.quantity}} {{produce.unit_type}}
            </td>
          </ng-container>
  
          <!-- Available Date Column -->
          <ng-container matColumnDef="available_date">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Availability </th>
            <td mat-cell *matCellDef="let produce">
              <div class="date-info">
                <div>From: {{formatDate(produce.available_from)}}</div>
                <div *ngIf="produce.available_until">To: {{formatDate(produce.available_until)}}</div>
              </div>
            </td>
          </ng-container>
          
          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td mat-cell *matCellDef="let produce">
              <div class="status-chip" [ngClass]="getStatusClass(produce.status)">
                {{produce.status | titlecase}}
              </div>
            </td>
          </ng-container>
  
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let produce">
              <button mat-icon-button color="primary" 
                      (click)="viewProduceDetails(produce.id)" 
                      matTooltip="View Details">
                <mat-icon>visibility</mat-icon>
              </button>
              
              <button *ngIf="produce.status === 'available'" mat-icon-button color="accent"
                      (click)="openLinkProduceDialog(produce)"
                      matTooltip="Link to Requirement">
                <mat-icon>link</mat-icon>
              </button>
  
              <button *ngIf="produce.status === 'available'" mat-icon-button color="primary"
                      (click)="createRequirementFromProduce(produce)"
                      matTooltip="Create New Requirement">
                <mat-icon>add_circle</mat-icon>
              </button>
              
              <button mat-icon-button [matMenuTriggerFor]="statusMenu" 
                      matTooltip="Change Status"
                      *ngIf="produce.status !== 'sold' && produce.status !== 'expired'">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #statusMenu="matMenu">
                <button mat-menu-item (click)="updateProduceStatus(produce.id, 'available')" 
                       *ngIf="produce.status !== 'available'">
                  <mat-icon>check_circle</mat-icon>
                  <span>Mark as Available</span>
                </button>
                <button mat-menu-item (click)="updateProduceStatus(produce.id, 'pending')" 
                       *ngIf="produce.status !== 'pending'">
                  <mat-icon>hourglass_empty</mat-icon>
                  <span>Mark as Pending</span>
                </button>
                <button mat-menu-item (click)="updateProduceStatus(produce.id, 'sold')" 
                       *ngIf="produce.status !== 'sold'">
                  <mat-icon>shopping_cart</mat-icon>
                  <span>Mark as Sold</span>
                </button>
                <button mat-menu-item (click)="updateProduceStatus(produce.id, 'expired')" 
                       *ngIf="produce.status !== 'expired'">
                  <mat-icon>event_busy</mat-icon>
                  <span>Mark as Expired</span>
                </button>
              </mat-menu>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  
          <!-- No Data Row -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="8">
              <div class="no-data">
                <mat-icon>search_off</mat-icon>
                <span>No produce listings found</span>
              </div>
            </td>
          </tr>
        </table>
  
        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </div>
    </mat-card>
  </div>
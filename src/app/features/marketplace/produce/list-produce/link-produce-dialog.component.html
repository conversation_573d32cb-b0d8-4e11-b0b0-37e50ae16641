// src/app/features/marketplace/produce/list-produce/link-produce-dialog.component.html

<h2 mat-dialog-title>
  {{ data.createNewRequirement ? 'Create New Requirement' : 'Link Produce to Requirement' }}
</h2>
<mat-dialog-content>
  <div class="produce-summary">
    <h3>Selected Produce</h3>
    <div class="produce-details">
      <p><strong>Crop:</strong> {{ data.produce.crop_name }} 
        <span *ngIf="data.produce.variety">({{ data.produce.variety }})</span>
      </p>
      <p><strong>Quantity:</strong> {{ data.produce.quantity }} {{ data.produce.unit_type }}</p>
      <p><strong>Farmer:</strong> {{ data.produce.farmer_name }}</p>
      <p><strong>Available From:</strong> {{ formatDate(data.produce.available_from) }}</p>
    </div>
  </div>

  <form [formGroup]="linkForm" *ngIf="!data.createNewRequirement">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Select Requirement</mat-label>
      <mat-select formControlName="requirementId">
        <mat-option *ngFor="let req of data.requirements" [value]="req.id">
          ID: {{ req.id }} - {{ req.crop_name }} ({{ req.quantity }} {{ req.unit_type }} at ₹{{ req.price_offered }})
        </mat-option>
        <mat-option *ngIf="data.allRequirements && showAllRequirements" disabled>
          --- Other Active Requirements ---
        </mat-option>
        <mat-option *ngFor="let req of otherRequirements" [value]="req.id" 
                   [disabled]="req.product_id !== data.produce.product_id">
          ID: {{ req.id }} - {{ req.crop_name }} ({{ req.quantity }} {{ req.unit_type }} at ₹{{ req.price_offered }})
          <span *ngIf="req.product_id !== data.produce.product_id"> (Different crop)</span>
        </mat-option>
      </mat-select>
      <mat-error *ngIf="linkForm.get('requirementId')?.hasError('required')">
        Please select a requirement
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Quantity</mat-label>
      <input matInput type="number" formControlName="quantity" placeholder="Enter quantity">
      <span matSuffix>{{ data.produce.unit_type }}</span>
      <mat-error *ngIf="linkForm.get('quantity')?.hasError('required')">
        Quantity is required
      </mat-error>
      <mat-error *ngIf="linkForm.get('quantity')?.hasError('max')">
        Quantity cannot exceed available amount ({{ data.produce.quantity }})
      </mat-error>
      <mat-error *ngIf="linkForm.get('quantity')?.hasError('min')">
        Quantity must be greater than 0
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Notes</mat-label>
      <textarea matInput formControlName="notes" placeholder="Add any notes about this transaction"></textarea>
    </mat-form-field>
  </form>

  <!-- New Requirement Form -->
  <form [formGroup]="newRequirementForm" *ngIf="data.createNewRequirement">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Quantity</mat-label>
      <input matInput type="number" formControlName="quantity" placeholder="Enter quantity">
      <span matSuffix>{{ data.produce.unit_type }}</span>
      <mat-error *ngIf="newRequirementForm.get('quantity')?.hasError('required')">
        Quantity is required
      </mat-error>
      <mat-error *ngIf="newRequirementForm.get('quantity')?.hasError('max')">
        Quantity cannot exceed available amount ({{ data.produce.quantity }})
      </mat-error>
      <mat-error *ngIf="newRequirementForm.get('quantity')?.hasError('min')">
        Quantity must be greater than 0
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Price Offered (per {{ data.produce.unit_type }})</mat-label>
      <input matInput type="number" formControlName="price_offered" placeholder="Enter price">
      <span matSuffix>₹</span>
      <mat-error *ngIf="newRequirementForm.get('price_offered')?.hasError('required')">
        Price is required
      </mat-error>
      <mat-error *ngIf="newRequirementForm.get('price_offered')?.hasError('min')">
        Price must be greater than 0
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Required By</mat-label>
      <input matInput [matDatepicker]="picker" formControlName="required_by">
      <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
      <mat-datepicker #picker></mat-datepicker>
      <mat-error *ngIf="newRequirementForm.get('required_by')?.hasError('required')">
        Required date is required
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Description</mat-label>
      <textarea matInput formControlName="description" placeholder="Add description about this requirement"></textarea>
      <mat-error *ngIf="newRequirementForm.get('description')?.hasError('required')">
        Description is required
      </mat-error>
    </mat-form-field>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button (click)="close()">Cancel</button>
  <button mat-raised-button color="primary" 
          (click)="submit()" 
          [disabled]="(data.createNewRequirement && newRequirementForm.invalid) || 
                     (!data.createNewRequirement && linkForm.invalid)">
    {{ data.createNewRequirement ? 'Create Requirement' : 'Link to Requirement' }}
  </button>
</mat-dialog-actions>
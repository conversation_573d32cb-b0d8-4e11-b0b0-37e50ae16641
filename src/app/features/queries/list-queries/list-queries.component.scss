// src/app/features/queries/list-queries/list-queries.component.scss
.page-container {
    padding: 24px;
  }
  
  .page-header {
    background: transparent;
    
    h1 {
      font-size: 24px;
      margin: 0;
    }
  }
  
  .spacer {
    flex: 1 1 auto;
  }
  
  .queries-list-container {
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  
    .header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 24px;
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  
      .filter-field {
        width: 100%;
        max-width: 500px;
        margin: 0;
  
        .mat-mdc-form-field-subscript-wrapper {
          display: none;
        }
      }
    }
  
    .table-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      overflow: auto;
      position: relative;
  
      table {
        width: 100%;
      }
  
      .mat-column-id {
        width: 80px;
        padding-left: 16px;
      }
  
      .mat-column-status {
        width: 120px;
        
        .mat-chip {
          font-size: 12px;
          min-height: 28px;
        }
      }
  
      .mat-column-response_count {
        width: 100px;
        text-align: center;
      }
  
      .mat-column-actions {
        width: 80px;
        text-align: center;
      }
  
      .mat-mdc-row:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }
<!-- src/app/features/queries/list-queries/list-queries.component.html -->
<div class="page-container">
    <mat-card class="mat-elevation-z2">
      <mat-toolbar class="page-header">
        <h1>Farmer Queries</h1>
        <span class="spacer"></span>
      </mat-toolbar>
      <mat-divider></mat-divider>
  
      <div class="queries-list-container">
        <div class="header-actions">
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Filter</mat-label>
            <input matInput placeholder="Search queries..." (keyup)="applyFilter($event)" #input>
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
        </div>
  
        <div class="table-container">
          <table mat-table [dataSource]="dataSource" matSort class="full-width-table">
            <!-- ID Column -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
              <td mat-cell *matCellDef="let row">{{row.id}}</td>
            </ng-container>
  
            <!-- Title Column -->
            <ng-container matColumnDef="title">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Title</th>
              <td mat-cell *matCellDef="let row">{{row.title}}</td>
            </ng-container>
  
            <!-- Farmer Column -->
            <ng-container matColumnDef="farmer_name">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Farmer</th>
              <td mat-cell *matCellDef="let row">{{row.farmer_name}}</td>
            </ng-container>
  
            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
              <td mat-cell *matCellDef="let row">
                <mat-chip [color]="getStatusColor(row.status)" selected>
                  {{row.status | titlecase}}
                </mat-chip>
              </td>
            </ng-container>
  
            <!-- Created Date Column -->
            <ng-container matColumnDef="created_at">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
              <td mat-cell *matCellDef="let row">{{row.created_at | date:'medium'}}</td>
            </ng-container>
  
            <!-- Responses Column -->
            <ng-container matColumnDef="response_count">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Responses</th>
              <td mat-cell *matCellDef="let row">{{row.response_count || 0}}</td>
            </ng-container>
  
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let row">
                <a mat-icon-button color="primary" [routerLink]="['/queries/view', row.id]">
                  <mat-icon>visibility</mat-icon>
                </a>
              </td>
            </ng-container>
  
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  
            <!-- No Data Row -->
            <tr class="mat-row" *matNoDataRow>
              <td class="mat-cell" colspan="7">
                No queries matching the filter "{{input.value}}"
              </td>
            </tr>
          </table>
  
          <mat-paginator [pageSizeOptions]="[10, 25, 50]" aria-label="Select page of queries"></mat-paginator>
        </div>
      </div>
    </mat-card>
  </div>
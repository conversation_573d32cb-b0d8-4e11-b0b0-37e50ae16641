// src/app/features/queries/view-query/view-query.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSelectModule } from '@angular/material/select';

import { QueryService, Query } from '../../../core/auth/services/query.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-view-query',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatDividerModule,
    MatToolbarModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatSnackBarModule,
    MatSelectModule,
  ],
  templateUrl: './view-query.component.html',
  styleUrls: ['./view-query.component.scss'],
})
export class ViewQueryComponent implements OnInit {
  query: Query | null = null;
  responseForm: FormGroup;
  statusForm: FormGroup;
  isLoading = false;
  isSubmitting = false;
  queryId: number | null = null;
  selectedFile: File | null = null;
  backendUrl = environment.backendUrl || '';

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private queryService: QueryService,
    private snackBar: MatSnackBar
  ) {
    this.responseForm = this.fb.group({
      response: ['', Validators.required],
    });

    this.statusForm = this.fb.group({
      status: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.queryId = +params['id'];
        this.loadQuery(this.queryId);
      } else {
        this.showError('Query ID not found');
        this.goBack();
      }
    });
  }

  loadQuery(id: number): void {
    this.isLoading = true;
    this.queryService.getQueryById(id).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.query = response.data;
          this.statusForm.patchValue({ status: this.query.status });
        } else {
          this.showError('Query not found');
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading query:', error);
        this.showError('Failed to load query');
        this.isLoading = false;
        this.goBack();
      },
    });
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
    }
  }

  submitResponse(): void {
    if (this.responseForm.valid && this.queryId) {
      this.isSubmitting = true;
      const formData = new FormData();
      formData.append('response', this.responseForm.get('response')?.value);

      if (this.selectedFile) {
        formData.append('image', this.selectedFile);
      }

      this.queryService.addResponse(this.queryId, formData).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('Response submitted successfully');
            this.responseForm.reset();
            this.selectedFile = null;
            this.loadQuery(this.queryId!);
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          console.error('Error submitting response:', error);
          this.showError('Failed to submit response');
          this.isSubmitting = false;
        },
      });
    }
  }

  updateStatus(): void {
    if (this.statusForm.valid && this.queryId) {
      const status = this.statusForm.get('status')?.value;
      this.isLoading = true;
      this.queryService.updateStatus(this.queryId, status).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess(`Status updated to ${status}`);
            if (this.query) {
              this.query.status = status;
            }
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error updating status:', error);
          this.showError('Failed to update status');
          this.isLoading = false;
        },
      });
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'open':
        return 'primary';
      case 'in_progress':
        return 'accent';
      case 'resolved':
        return 'success';
      case 'closed':
        return 'default';
      default:
        return 'default';
    }
  }

  goBack(): void {
    this.router.navigate(['/queries']);
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  // Add this to your ViewQueryComponent class
  get statusControl(): FormControl {
    return this.statusForm.get('status') as FormControl;
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

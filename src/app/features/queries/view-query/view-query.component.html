<!-- src/app/features/queries/view-query/view-query.component.html -->
<div class="query-container">
    <mat-card class="query-card">
      <mat-toolbar class="card-header">
        <button mat-icon-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <h1>Query Details</h1>
      </mat-toolbar>
      <mat-divider></mat-divider>
  
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
  
      <div *ngIf="!isLoading && query" class="query-content">
        <!-- Query Header -->
        <div class="query-header">
          <div class="query-title-section">
            <h2 class="query-title">{{query.title}}</h2>
            <div class="query-meta">
              <span class="query-author">From: {{query.farmer_name}}</span>
              <span class="query-date">{{query.created_at | date:'medium'}}</span>
            </div>
          </div>
          <div class="query-status">
            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select [formControl]="statusControl" (selectionChange)="updateStatus()">
                <mat-option value="open">Open</mat-option>
                <mat-option value="in_progress">In Progress</mat-option>
                <mat-option value="resolved">Resolved</mat-option>
                <mat-option value="closed">Closed</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
  
        <!-- Query Body -->
        <div class="query-body">
          <p class="query-description">{{query.description}}</p>
          <div *ngIf="query.image_url" class="query-image">
            <img [src]="backendUrl + query.image_url" alt="Query Image">
          </div>
        </div>
  
        <mat-divider class="section-divider"></mat-divider>
  
        <!-- Response Section -->
        <div class="responses-section">
          <h3 class="section-title">Responses ({{query.responses?.length || 0}})</h3>
          
          <div *ngIf="query.responses && query.responses.length > 0" class="responses-list">
            <div *ngFor="let response of query.responses" class="response-item">
              <div class="response-header">
                <span class="response-author">{{response.responder_name}}</span>
                <span class="response-date">{{response.created_at | date:'medium'}}</span>
              </div>
              <p class="response-content">{{response.response}}</p>
              <div *ngIf="response.image_url" class="response-image">
                <img [src]="backendUrl + response.image_url" alt="Response Image">
              </div>
            </div>
          </div>
          
          <div *ngIf="!query.responses || query.responses.length === 0" class="no-responses">
            <p>No responses yet.</p>
          </div>
  
          <!-- Add Response Form -->
          <div class="response-form-container">
            <h3 class="form-title">Add Response</h3>
            <form [formGroup]="responseForm" (ngSubmit)="submitResponse()">
              <mat-form-field appearance="outline" class="response-input">
                <mat-label>Your Response</mat-label>
                <textarea matInput formControlName="response" rows="4" placeholder="Type your response here..."></textarea>
                <mat-error *ngIf="responseForm.get('response')?.hasError('required')">
                  Response is required
                </mat-error>
              </mat-form-field>
  
              <div class="file-upload">
                <label for="response-image">Attach Image (Optional)</label>
                <input type="file" id="response-image" (change)="onFileSelected($event)" accept="image/*">
                <p *ngIf="selectedFile" class="selected-file">
                  Selected: {{selectedFile.name}}
                </p>
              </div>
  
              <div class="form-actions">
                <button 
                  mat-raised-button 
                  color="primary" 
                  type="submit" 
                  [disabled]="responseForm.invalid || isSubmitting">
                  <mat-icon>send</mat-icon>
                  Submit Response
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
  
      <div *ngIf="!isLoading && !query" class="not-found">
        <mat-icon>error</mat-icon>
        <h2>Query Not Found</h2>
        <p>The query you're looking for doesn't exist or you don't have permission to view it.</p>
        <button mat-raised-button color="primary" (click)="goBack()">
          Back to Queries
        </button>
      </div>
    </mat-card>
  </div>
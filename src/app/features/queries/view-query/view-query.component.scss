// src/app/features/queries/view-query/view-query.component.scss
.query-container {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .query-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
  
    .card-header {
      background: transparent;
      
      h1 {
        margin-left: 16px;
        font-size: 20px;
        font-weight: 500;
      }
    }
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
  
  .query-content {
    padding: 24px;
  
    .query-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
  
      .query-title-section {
        flex: 1;
  
        .query-title {
          font-size: 24px;
          font-weight: 500;
          margin: 0 0 8px;
          color: #333;
        }
  
        .query-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          font-size: 14px;
          color: #666;
  
          .query-author {
            font-weight: 500;
          }
        }
      }
  
      .query-status {
        min-width: 150px;
        
        mat-form-field {
          width: 100%;
        }
      }
    }
  
    .query-body {
      margin-bottom: 24px;
  
      .query-description {
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 16px;
        white-space: pre-line;
      }
  
      .query-image {
        margin-top: 16px;
        border-radius: 8px;
        overflow: hidden;
        max-width: 100%;
  
        img {
          max-width: 100%;
          max-height: 400px;
          object-fit: contain;
        }
      }
    }
  
    .section-divider {
      margin: 24px 0;
    }
  
    .responses-section {
      .section-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0 0 16px;
        color: #333;
      }
  
      .responses-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 32px;
  
        .response-item {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 16px;
          border-left: 4px solid #3f51b5;
  
          .response-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
  
            .response-author {
              font-weight: 500;
              color: #333;
            }
  
            .response-date {
              color: #666;
            }
          }
  
          .response-content {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 12px;
            white-space: pre-line;
          }
  
          .response-image {
            margin-top: 12px;
            border-radius: 4px;
            overflow: hidden;
            max-width: 100%;
  
            img {
              max-width: 100%;
              max-height: 300px;
              object-fit: contain;
            }
          }
        }
      }
  
      .no-responses {
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 24px;
        text-align: center;
        color: #666;
        margin-bottom: 32px;
      }
  
      .response-form-container {
        background-color: #fff;
        border-radius: 8px;
        padding: 24px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
        .form-title {
          font-size: 18px;
          font-weight: 500;
          margin: 0 0 16px;
          color: #333;
        }
  
        .response-input {
          width: 100%;
          margin-bottom: 16px;
  
          textarea {
            resize: vertical;
            min-height: 100px;
          }
        }
  
        .file-upload {
          margin-bottom: 24px;
  
          label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
          }
  
          input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
          }
  
          .selected-file {
            margin-top: 8px;
            font-size: 14px;
            color: #3f51b5;
          }
        }
  
        .form-actions {
          display: flex;
          justify-content: flex-end;
  
          button {
            min-width: 150px;
  
            mat-icon {
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
  
  .not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    text-align: center;
  
    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      color: #f44336;
      margin-bottom: 16px;
    }
  
    h2 {
      font-size: 24px;
      margin-bottom: 8px;
      color: #333;
    }
  
    p {
      font-size: 16px;
      color: #666;
      margin-bottom: 24px;
      max-width: 500px;
    }
  
    button {
      min-width: 150px;
    }
  }
  
  @media (max-width: 768px) {
    .query-container {
      padding: 16px;
    }
  
    .query-content {
      padding: 16px;
    }
  
    .query-header {
      flex-direction: column;
      
      .query-status {
        width: 100%;
        margin-top: 16px;
      }
    }
  
    .responses-section {
      .response-item {
        padding: 12px;
        
        .response-header {
          flex-direction: column;
          gap: 4px;
        }
      }
    }
  }
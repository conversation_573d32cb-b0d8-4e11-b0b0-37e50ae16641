// dashboard.component.scss
.dashboard-container {
    padding: 24px;
    height: calc(100vh - 64px);
    overflow-y: auto;
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }
  
  .dashboard-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  
  // Stats Cards
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 24px;
  }
  
  .stats-card {
    border-radius: 8px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    }
  
    mat-card-content {
      display: flex;
      align-items: center;
      padding: 16px;
    }
  
    .stats-icon {
      background-color: rgba(63, 81, 181, 0.1);
      border-radius: 50%;
      width: 64px;
      height: 64px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 16px;
  
      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
        color: #3f51b5;
      }
    }
  
    .stats-info {
      flex: 1;
    }
  
    .stats-title {
      margin: 0;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
    }
  
    .stats-value {
      margin: 8px 0 0;
      font-size: 28px;
      font-weight: 500;
    }
  }
  
  // Tables Section
  .data-section {
    mat-card {
      border-radius: 8px;
    }
  
    mat-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
  
      .header-actions {
        margin-left: auto;
      }
    }
  
    mat-card-content {
      padding: 0 16px 16px;
    }
  }
  
  .data-table {
    width: 100%;
  
    .mat-mdc-header-row {
      background-color: #f5f5f5;
    }
  
    .mat-mdc-header-cell {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.87);
    }
  
    .mat-mdc-row:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
  
  .no-data {
    padding: 40px;
    text-align: center;
    color: rgba(0, 0, 0, 0.54);
    font-style: italic;
  }
  
  // State Distribution
  .state-distribution {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .state-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .state-name {
    font-weight: 500;
  }
  
  .state-bar-container {
    display: flex;
    align-items: center;
    height: 24px;
  }
  
  .state-bar {
    background-color: #3f51b5;
    height: 100%;
    border-radius: 4px;
    min-width: 20px;
  }
  
  .state-count {
    margin-left: 8px;
    font-weight: 500;
  }
  
  // Responsive Adjustments
  @media (max-width: 768px) {
    .dashboard-container {
      padding: 16px;
    }
  
    .stats-cards {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }
  
    .stats-card {
      .stats-icon {
        width: 48px;
        height: 48px;
  
        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
        }
      }
  
      .stats-value {
        font-size: 24px;
      }
    }
  }
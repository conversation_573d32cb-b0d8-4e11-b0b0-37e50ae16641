// dashboard.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { FarmerService } from '../../core/auth/services/farmer.service';
import { ArticleService } from '../../core/auth/services/article.service';
import { catchError, forkJoin, map, of } from 'rxjs';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatTableModule,
    MatPaginatorModule,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {
  isLoading = true;
  dashboardData: any = {
    totalFarmers: 0,
    recentFarmers: [],
    recentArticles: [],
    openQueries: 0,
    resolvedQueries: 0,
    farmersPerState: [],
    stateCounts: [],
  };

  displayedColumnsFarmers: string[] = [
    'id',
    'full_name',
    'mobile_number',
    'state_name',
    'created_at',
  ];
  displayedColumnsArticles: string[] = [
    'id',
    'title',
    'created_by_name',
    'created_at',
  ];

  constructor(
    private farmerService: FarmerService,
    private articleService: ArticleService
  ) {}

  ngOnInit() {
    this.loadDashboardData();
  }

  loadDashboardData() {
    this.isLoading = true;

    forkJoin({
      farmers: this.farmerService
        .getAllFarmers()
        .pipe(catchError(() => of([]))),
      articles: this.articleService.getAllArticles().pipe(
        map((response) => response.data),
        catchError(() => of([]))
      ),
    }).subscribe({
      next: (results) => {
        // Process farmers data
        this.dashboardData.totalFarmers = results.farmers.length;
        this.dashboardData.recentFarmers = results.farmers
          .sort((a, b) => {
            // Handle potential undefined created_at values
            const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
            const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
            return dateB - dateA;
          })
          .slice(0, 5);

        // Process articles data
        this.dashboardData.recentArticles = results.articles
          .sort((a, b) => {
            // Handle potential undefined created_at values
            const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
            const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
            return dateB - dateA;
          })
          .slice(0, 5);

        // Calculate state-wise distribution
        this.calculateStateDistribution(results.farmers);

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading dashboard data:', error);
        this.isLoading = false;
      },
    });
  }

  calculateStateDistribution(farmers: any[]) {
    // Get count of farmers by state
    const stateMap = new Map<string, number>();

    farmers.forEach((farmer) => {
      const stateName = farmer.state_name || 'Unknown';
      stateMap.set(stateName, (stateMap.get(stateName) || 0) + 1);
    });

    // Convert to array for the chart
    this.dashboardData.stateCounts = Array.from(stateMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }

  formatDate(date: string | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  }
}

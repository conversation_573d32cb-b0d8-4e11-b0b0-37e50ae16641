<!-- dashboard.component.html -->
<div class="dashboard-container">
    <!-- Loading Spinner -->
    <div class="loading-container" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>
  
    <div class="dashboard-content" *ngIf="!isLoading">
      <!-- Stats Summary Cards -->
      <div class="stats-cards">
        <mat-card class="stats-card">
          <mat-card-content>
            <div class="stats-icon">
              <mat-icon>people</mat-icon>
            </div>
            <div class="stats-info">
              <p class="stats-title">Total Farmers</p>
              <h2 class="stats-value">{{dashboardData.totalFarmers}}</h2>
            </div>
          </mat-card-content>
        </mat-card>
  
        <mat-card class="stats-card">
          <mat-card-content>
            <div class="stats-icon">
              <mat-icon>article</mat-icon>
            </div>
            <div class="stats-info">
              <p class="stats-title">Knowledge Articles</p>
              <h2 class="stats-value">{{dashboardData.recentArticles.length}}</h2>
            </div>
          </mat-card-content>
        </mat-card>
  
        <mat-card class="stats-card">
          <mat-card-content>
            <div class="stats-icon">
              <mat-icon>question_answer</mat-icon>
            </div>
            <div class="stats-info">
              <p class="stats-title">Open Queries</p>
              <h2 class="stats-value">{{dashboardData.openQueries || 0}}</h2>
            </div>
          </mat-card-content>
        </mat-card>
  
        <mat-card class="stats-card">
          <mat-card-content>
            <div class="stats-icon">
              <mat-icon>verified</mat-icon>
            </div>
            <div class="stats-info">
              <p class="stats-title">Resolved Queries</p>
              <h2 class="stats-value">{{dashboardData.resolvedQueries || 0}}</h2>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
  
      <!-- Recent Farmers Section -->
      <div class="data-section">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Recently Added Farmers</mat-card-title>
            <div class="header-actions">
              <a mat-button color="primary" routerLink="/farmers">View All</a>
            </div>
          </mat-card-header>
          <mat-card-content>
            <table mat-table [dataSource]="dashboardData.recentFarmers" class="data-table">
              <!-- ID Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef>ID</th>
                <td mat-cell *matCellDef="let farmer">{{farmer.id}}</td>
              </ng-container>
  
              <!-- Name Column -->
              <ng-container matColumnDef="full_name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let farmer">{{farmer.full_name}}</td>
              </ng-container>
  
              <!-- Mobile Column -->
              <ng-container matColumnDef="mobile_number">
                <th mat-header-cell *matHeaderCellDef>Mobile</th>
                <td mat-cell *matCellDef="let farmer">{{farmer.mobile_number}}</td>
              </ng-container>
  
              <!-- State Column -->
              <ng-container matColumnDef="state_name">
                <th mat-header-cell *matHeaderCellDef>State</th>
                <td mat-cell *matCellDef="let farmer">{{farmer.state_name || 'Unknown'}}</td>
              </ng-container>
  
              <!-- Date Column -->
              <ng-container matColumnDef="created_at">
                <th mat-header-cell *matHeaderCellDef>Added On</th>
                <td mat-cell *matCellDef="let farmer">{{formatDate(farmer.created_at)}}</td>
              </ng-container>
  
              <tr mat-header-row *matHeaderRowDef="displayedColumnsFarmers"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumnsFarmers;"></tr>
            </table>
            
            <div class="no-data" *ngIf="dashboardData.recentFarmers.length === 0">
              No farmers have been added yet.
            </div>
          </mat-card-content>
        </mat-card>
      </div>
  
      <!-- Knowledge Hub Articles -->
      <div class="data-section">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Recent Knowledge Articles</mat-card-title>
            <div class="header-actions">
              <a mat-button color="primary" routerLink="/knowledge-hub">View All</a>
            </div>
          </mat-card-header>
          <mat-card-content>
            <table mat-table [dataSource]="dashboardData.recentArticles" class="data-table">
              <!-- ID Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef>ID</th>
                <td mat-cell *matCellDef="let article">{{article.id}}</td>
              </ng-container>
  
              <!-- Title Column -->
              <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef>Title</th>
                <td mat-cell *matCellDef="let article">{{article.title}}</td>
              </ng-container>
  
              <!-- Author Column -->
              <ng-container matColumnDef="created_by_name">
                <th mat-header-cell *matHeaderCellDef>Author</th>
                <td mat-cell *matCellDef="let article">{{article.created_by_name || 'Unknown'}}</td>
              </ng-container>
  
              <!-- Date Column -->
              <ng-container matColumnDef="created_at">
                <th mat-header-cell *matHeaderCellDef>Published On</th>
                <td mat-cell *matCellDef="let article">{{formatDate(article.created_at)}}</td>
              </ng-container>
  
              <tr mat-header-row *matHeaderRowDef="displayedColumnsArticles"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumnsArticles;"></tr>
            </table>
            
            <div class="no-data" *ngIf="dashboardData.recentArticles.length === 0">
              No knowledge articles have been published yet.
            </div>
          </mat-card-content>
        </mat-card>
      </div>
  
      <!-- State Distribution -->
      <div class="data-section">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Farmers by State</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="state-distribution">
              <div *ngFor="let stateData of dashboardData.stateCounts" class="state-item">
                <div class="state-name">{{stateData.name}}</div>
                <div class="state-bar-container">
                  <div class="state-bar" [style.width.%]="(stateData.count / dashboardData.totalFarmers) * 100"></div>
                  <div class="state-count">{{stateData.count}}</div>
                </div>
              </div>
            </div>
            
            <div class="no-data" *ngIf="dashboardData.stateCounts.length === 0">
              No data available for state distribution.
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
.farmer-view-container {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
    
    .back-navigation {
      margin-bottom: 16px;
    }
    
    .loading-card, .error-card {
      margin-bottom: 24px;
      
      .loading-container, .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 24px;
        text-align: center;
        
        p {
          margin: 16px 0;
        }
      }
    }
    
    .farmer-details {
      .profile-card {
        margin-bottom: 24px;
        
        .profile-avatar {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f5f5;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          mat-icon {
            font-size: 36px;
            width: 36px;
            height: 36px;
            color: #757575;
          }
        }
      }
      
      .tab-content {
        padding: 24px 0;
        
        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 24px;
          margin-bottom: 24px;
          
          .info-item {
            .info-label {
              font-size: 12px;
              color: #757575;
              margin-bottom: 4px;
            }
            
            .info-value {
              font-size: 16px;
              color: #212121;
            }
          }
        }
        
        .section-divider {
          margin: 24px 0;
        }
        
        h3 {
          margin: 16px 0 8px;
          font-size: 18px;
          font-weight: 500;
        }
        
        .chips-container {
          margin-bottom: 24px;
        }
      }
    }
  }
  
  // Make sure tabs have consistent styling
  ::ng-deep {
    .mat-mdc-tab-body-content {
      overflow: hidden !important;
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .farmer-view-container {
      padding: 16px;
      
      .farmer-details {
        .tab-content {
          .info-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
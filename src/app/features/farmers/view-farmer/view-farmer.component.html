<div class="farmer-view-container">
    <div class="back-navigation">
      <button mat-button color="primary" (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> Back to Farmers
      </button>
    </div>
  
    <mat-card *ngIf="isLoading" class="loading-card">
      <mat-card-content>
        <div class="loading-container">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Loading farmer details...</p>
        </div>
      </mat-card-content>
    </mat-card>
  
    <mat-card *ngIf="!isLoading && errorMessage" class="error-card">
      <mat-card-content>
        <div class="error-container">
          <mat-icon color="warn">error_outline</mat-icon>
          <p>{{ errorMessage }}</p>
          <button mat-raised-button color="primary" (click)="loadFarmerDetails()">Retry</button>
        </div>
      </mat-card-content>
    </mat-card>
  
    <div *ngIf="!isLoading && !errorMessage && farmer" class="farmer-details">
      <mat-card class="profile-card">
        <mat-card-header>
          <div mat-card-avatar class="profile-avatar">
            <img *ngIf="farmer.profile_pic_url" [src]="farmer.profile_pic_url" alt="Farmer Photo">
            <mat-icon *ngIf="!farmer.profile_pic_url">person</mat-icon>
          </div>
          <mat-card-title>{{ farmer.full_name }}</mat-card-title>
          <mat-card-subtitle>
            <mat-chip-set>
              <mat-chip color="primary" selected>Farmer</mat-chip>
              <mat-chip *ngIf="farmer.farming_type">{{ farmer.farming_type }}</mat-chip>
            </mat-chip-set>
          </mat-card-subtitle>
        </mat-card-header>
      </mat-card>
  
      <mat-tab-group>
        <mat-tab label="Basic Information">
          <div class="tab-content">
            <mat-card>
              <mat-card-content>
                <div class="info-grid">
                  <div class="info-item">
                    <div class="info-label">Full Name</div>
                    <div class="info-value">{{ farmer.full_name }}</div>
                  </div>
                  
                  <div class="info-item">
                    <div class="info-label">Mobile Number</div>
                    <div class="info-value">{{ farmer.mobile_number }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.alternate_number">
                    <div class="info-label">Alternate Number</div>
                    <div class="info-value">{{ farmer.alternate_number }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.email_id">
                    <div class="info-label">Email</div>
                    <div class="info-value">{{ farmer.email_id }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.date_of_birth">
                    <div class="info-label">Date of Birth</div>
                    <div class="info-value">{{ farmer.date_of_birth | date }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.gender">
                    <div class="info-label">Gender</div>
                    <div class="info-value">{{ farmer.gender }}</div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
        
        <mat-tab label="Farming Details">
          <div class="tab-content">
            <mat-card>
              <mat-card-content>
                <div class="info-grid">
                  <div class="info-item" *ngIf="farmer.farming_type">
                    <div class="info-label">Farming Type</div>
                    <div class="info-value">{{ farmer.farming_type }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.irrigation_type">
                    <div class="info-label">Irrigation Type</div>
                    <div class="info-value">{{ farmer.irrigation_type }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.farm_size">
                    <div class="info-label">Farm Size</div>
                    <div class="info-value">{{ farmer.farm_size }} acres</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.farming_experience">
                    <div class="info-label">Farming Experience</div>
                    <div class="info-value">{{ farmer.farming_experience }} years</div>
                  </div>
                </div>
                
                <mat-divider class="section-divider"></mat-divider>
                
                <div *ngIf="farmer.crops && farmer.crops.length > 0">
                  <h3>Crops</h3>
                  <div class="chips-container">
                    <mat-chip-set>
                      <mat-chip *ngFor="let crop of farmer.crops">{{ crop.crop_name }}</mat-chip>
                    </mat-chip-set>
                  </div>
                </div>
                
                <div *ngIf="farmer.livestock && farmer.livestock.length > 0">
                  <h3>Livestock</h3>
                  <div class="chips-container">
                    <mat-chip-set>
                      <mat-chip *ngFor="let livestock of farmer.livestock">{{ livestock.livestock_name }}</mat-chip>
                    </mat-chip-set>
                  </div>
                </div>
                
                <div *ngIf="farmer.equipment && farmer.equipment.length > 0">
                  <h3>Equipment</h3>
                  <div class="chips-container">
                    <mat-chip-set>
                      <mat-chip *ngFor="let equipment of farmer.equipment">{{ equipment.equipment_name }}</mat-chip>
                    </mat-chip-set>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
        
        <mat-tab label="Address Information">
          <div class="tab-content">
            <mat-card>
              <mat-card-content>
                <div class="info-grid">
                  <div class="info-item" *ngIf="farmer.address_line1">
                    <div class="info-label">Address Line 1</div>
                    <div class="info-value">{{ farmer.address_line1 }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.address_line2">
                    <div class="info-label">Address Line 2</div>
                    <div class="info-value">{{ farmer.address_line2 }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.locality">
                    <div class="info-label">Locality</div>
                    <div class="info-value">{{ farmer.locality }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.city_name">
                    <div class="info-label">City</div>
                    <div class="info-value">{{ farmer.city_name }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.state_name">
                    <div class="info-label">State</div>
                    <div class="info-value">{{ farmer.state_name }}</div>
                  </div>
                  
                  <div class="info-item" *ngIf="farmer.pincode">
                    <div class="info-label">Pincode</div>
                    <div class="info-value">{{ farmer.pincode }}</div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </div>
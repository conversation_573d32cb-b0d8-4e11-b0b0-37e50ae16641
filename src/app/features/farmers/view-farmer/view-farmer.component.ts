import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FarmerService } from '../../../core/auth/services/farmer.service';

@Component({
  selector: 'app-farmer-view',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTabsModule,
    MatDividerModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './view-farmer.component.html',
  styleUrls: ['./view-farmer.component.scss'],
})
export class FarmerViewComponent implements OnInit {
  farmerId: number = 0;
  farmer: any = null;
  isLoading: boolean = true;
  errorMessage: string = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private farmerService: FarmerService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Get the farmer ID from the route parameters
    this.route.paramMap.subscribe((params) => {
      const idParam = params.get('id');
      if (idParam) {
        this.farmerId = +idParam;
        this.loadFarmerDetails();
      } else {
        this.errorMessage = 'Invalid farmer ID';
        this.isLoading = false;
      }
    });
  }

  loadFarmerDetails(): void {
    this.isLoading = true;
    this.farmerService.getById(this.farmerId).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.farmer = response.data;
        } else {
          this.errorMessage = 'Failed to load farmer details';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading farmer details:', error);
        this.errorMessage =
          error.error?.message || 'Failed to load farmer details';
        this.isLoading = false;
      },
    });
  }

  goBack(): void {
    this.router.navigate(['/farmers']);
  }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatStepperModule } from '@angular/material/stepper';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatRadioModule } from '@angular/material/radio';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { FarmerService } from '../../../core/auth/services/farmer.service';
import { forkJoin } from 'rxjs';

import {
  StateService,
  State,
} from '../../../core/auth/services/masters/state.service';
import {
  CityService,
  City,
} from '../../../core/auth/services/masters/city.service';
import {
  FarmingTypeService,
  FarmingType,
} from '../../../core/auth/services/masters/farming-type.service';
import {
  IrrigationTypeService,
  IrrigationType,
} from '../../../core/auth/services/masters/irrigation-type.service';
import {
  CropService,
  Crop,
} from '../../../core/auth/services/masters/crop.service';
import {
  EquipmentTypeService,
  EquipmentType,
} from '../../../core/auth/services/masters/equipment-type.service';
import {
  LivestockTypeService,
  LivestockType,
} from '../../../core/auth/services/masters/livestock-type.service';

@Component({
  selector: 'app-add-farmer',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatStepperModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatRadioModule,
    MatCardModule,
    MatIconModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
  ],
  templateUrl: './add-farmer.component.html',
  styleUrls: ['./add-farmer.component.scss'],
})
export class AddFarmerComponent implements OnInit {
  personalInfoForm!: FormGroup;
  addressForm!: FormGroup;
  farmingDetailsForm!: FormGroup;
  governmentDetailsForm!: FormGroup;
  documentsForm!: FormGroup;

  states: State[] = [];
  cities: City[] = [];
  farmingTypes: FarmingType[] = [];
  irrigationTypes: IrrigationType[] = [];
  crops: Crop[] = [];
  equipmentTypes: EquipmentType[] = [];
  livestockTypes: LivestockType[] = [];

  isLoading = false;
  cityLoading = false;
  selectedFiles: { [key: string]: File } = {};

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private stateService: StateService,
    private cityService: CityService,
    private farmerService: FarmerService,
    private farmingTypeService: FarmingTypeService,
    private irrigationTypeService: IrrigationTypeService,
    private cropService: CropService,
    private equipmentTypeService: EquipmentTypeService,
    private livestockTypeService: LivestockTypeService
  ) {
    this.initializeForms();
  }

  ngOnInit() {
    this.loadMasterData();
  }

  private initializeForms() {
    this.personalInfoForm = this.fb.group({
      full_name: ['', [Validators.required]],
      mobile_number: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      alternate_number: ['', [Validators.pattern('^[0-9]{10}$')]],
      email_id: ['', [Validators.email]],
      date_of_birth: ['', Validators.required],
      gender: ['', Validators.required],
      username: ['', [Validators.required, Validators.minLength(4)]],
      password: ['', [Validators.required, Validators.minLength(6)]],
    });

    this.addressForm = this.fb.group({
      address_line1: ['', Validators.required],
      address_line2: [''],
      locality: [''],
      state_id: ['', Validators.required],
      city_id: ['', Validators.required],
      pincode: ['', [Validators.required, Validators.pattern('^[0-9]{6}$')]],
    });

    this.farmingDetailsForm = this.fb.group({
      farm_size: ['', [Validators.required, Validators.min(0)]],
      farming_type_id: ['', Validators.required],
      irrigation_type_id: ['', Validators.required],
      farming_experience: ['', [Validators.required, Validators.min(0)]],
      crops: [[]],
      livestock: [[]],
      equipment: [[]],
    });

    this.governmentDetailsForm = this.fb.group({
      aadhaar_number: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{12}$')],
      ],
      ration_card_number: [''],
      farmer_id: [''],
    });

    this.documentsForm = this.fb.group({
      id_proof: [''],
      profile_pic: [''],
    });
  }

  private loadMasterData() {
    this.isLoading = true;

    forkJoin({
      states: this.stateService.getAll(),
      farmingTypes: this.farmingTypeService.getAll(),
      irrigationTypes: this.irrigationTypeService.getAll(),
      crops: this.cropService.getAll(),
      equipmentTypes: this.equipmentTypeService.getAll(),
      livestockTypes: this.livestockTypeService.getAll(),
    }).subscribe({
      next: (responses) => {
        if (responses.states.status === 'success') {
          this.states = responses.states.data.filter(
            (state) => state.is_active
          );
        }

        if (responses.farmingTypes.status === 'success') {
          this.farmingTypes = responses.farmingTypes.data.filter(
            (type) => type.is_active
          );
        }

        if (responses.irrigationTypes.status === 'success') {
          this.irrigationTypes = responses.irrigationTypes.data.filter(
            (type) => type.is_active
          );
        }

        if (responses.crops.status === 'success') {
          this.crops = responses.crops.data.filter((crop) => crop.is_active);
        }

        if (responses.equipmentTypes.status === 'success') {
          this.equipmentTypes = responses.equipmentTypes.data.filter(
            (type) => type.is_active
          );
        }

        if (responses.livestockTypes.status === 'success') {
          this.livestockTypes = responses.livestockTypes.data.filter(
            (type) => type.is_active
          );
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading master data:', error);
        this.showError('Failed to load master data. Please try again.');
        this.isLoading = false;
      },
    });
  }

  onStateChange(stateId: number) {
    if (!stateId) {
      this.cities = [];
      return;
    }

    this.cityLoading = true;
    this.cityService.getCitiesByState(stateId).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.cities = response.data.filter((city) => city.is_active);
        } else {
          this.showError('Failed to load cities');
        }
        this.cityLoading = false;
      },
      error: (error) => {
        console.error('Error loading cities:', error);
        this.showError('Failed to load cities');
        this.cityLoading = false;
      },
    });
  }

  onIdProofSelected(event: Event) {
    const element = event.target as HTMLInputElement;
    const file = element.files?.[0];
    if (file) {
      this.selectedFiles['id_proof'] = file;
      this.documentsForm.patchValue({ id_proof: file.name });
    }
  }

  onProfilePicSelected(event: Event) {
    const element = event.target as HTMLInputElement;
    const file = element.files?.[0];
    if (file) {
      this.selectedFiles['profile_pic'] = file;
      this.documentsForm.patchValue({ profile_pic: file.name });
    }
  }

  onSubmit() {
    if (this.isFormsValid()) {
      const formData = new FormData();

      // Add personal info
      Object.entries(this.personalInfoForm.value).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, value.toString().trim());
        }
      });

      // Add address details
      Object.entries(this.addressForm.value).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, value.toString().trim());
        }
      });

      // Add farming details
      const farmingDetails = this.farmingDetailsForm.value;
      Object.entries(farmingDetails).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, value.toString());
          }
        }
      });

      // Add government details
      Object.entries(this.governmentDetailsForm.value).forEach(
        ([key, value]) => {
          if (value !== null && value !== undefined) {
            formData.append(key, value.toString().trim());
          }
        }
      );

      // Add files
      if (this.selectedFiles['id_proof']) {
        formData.append('id_proof', this.selectedFiles['id_proof']);
      }
      if (this.selectedFiles['profile_pic']) {
        formData.append('profile_pic', this.selectedFiles['profile_pic']);
      }

      this.isLoading = true;
      this.farmerService.createFarmer(formData).subscribe({
        next: () => {
          this.showSuccess('Farmer added successfully');
          this.router.navigate(['../'], { relativeTo: this.route });
        },
        error: (error) => {
          this.isLoading = false;
          this.showError(error.message || 'Failed to add farmer');
        },
      });
    } else {
      this.validateAllFormFields();
    }
  }

  private validateAllFormFields() {
    [
      this.personalInfoForm,
      this.addressForm,
      this.farmingDetailsForm,
      this.governmentDetailsForm,
    ].forEach((form) => {
      Object.keys(form.controls).forEach((key) => {
        const control = form.get(key);
        if (control) {
          control.markAsTouched();
        }
      });
    });
  }

  isFormsValid(): boolean {
    return (
      this.personalInfoForm.valid &&
      this.addressForm.valid &&
      this.farmingDetailsForm.valid &&
      this.governmentDetailsForm.valid
    );
  }

  goBack() {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  private showSuccess(message: string) {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string) {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

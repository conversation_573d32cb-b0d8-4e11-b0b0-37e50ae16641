<div class="content-container">
    <mat-card>
      <mat-toolbar class="page-header">
        <button mat-icon-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <h1>Add Farmer</h1>
      </mat-toolbar>
      <mat-divider></mat-divider>
  
      <mat-stepper orientation="horizontal" #stepper>
        <!-- Personal Information Step -->
        <mat-step [stepControl]="personalInfoForm">
          <form [formGroup]="personalInfoForm">
            <ng-template matStepLabel>Personal Information</ng-template>
            
            <div class="form-container">
              <mat-form-field appearance="outline">
                <mat-label>Full Name</mat-label>
                <input matInput formControlName="full_name" required>
                <mat-error *ngIf="personalInfoForm.get('full_name')?.hasError('required')">
                  Full name is required
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Mobile Number</mat-label>
                <input matInput formControlName="mobile_number" required>
                <mat-error *ngIf="personalInfoForm.get('mobile_number')?.hasError('required')">
                  Mobile number is required
                </mat-error>
                <mat-error *ngIf="personalInfoForm.get('mobile_number')?.hasError('pattern')">
                  Please enter a valid 10-digit mobile number
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Alternate Number</mat-label>
                <input matInput formControlName="alternate_number">
                <mat-error *ngIf="personalInfoForm.get('alternate_number')?.hasError('pattern')">
                  Please enter a valid 10-digit mobile number
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Email</mat-label>
                <input matInput formControlName="email_id" type="email">
                <mat-error *ngIf="personalInfoForm.get('email_id')?.hasError('email')">
                  Please enter a valid email address
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Date of Birth</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="date_of_birth" required>
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-error *ngIf="personalInfoForm.get('date_of_birth')?.hasError('required')">
                  Date of birth is required
                </mat-error>
              </mat-form-field>
  
              <div class="radio-group-container">
                <label class="mat-label">Gender</label>
                <mat-radio-group formControlName="gender" required>
                  <mat-radio-button value="male">Male</mat-radio-button>
                  <mat-radio-button value="female">Female</mat-radio-button>
                  <mat-radio-button value="other">Other</mat-radio-button>
                </mat-radio-group>
              </div>
  
              <!-- Username Field -->
              <mat-form-field appearance="outline">
                <mat-label>Username</mat-label>
                <input matInput formControlName="username" required>
                <mat-error *ngIf="personalInfoForm.get('username')?.hasError('required')">
                  Username is required
                </mat-error>
                <mat-error *ngIf="personalInfoForm.get('username')?.hasError('minlength')">
                  Username must be at least 4 characters long
                </mat-error>
              </mat-form-field>
  
              <!-- Password Field -->
              <mat-form-field appearance="outline">
                <mat-label>Password</mat-label>
                <input matInput formControlName="password" type="password" required>
                <mat-error *ngIf="personalInfoForm.get('password')?.hasError('required')">
                  Password is required
                </mat-error>
                <mat-error *ngIf="personalInfoForm.get('password')?.hasError('minlength')">
                  Password must be at least 6 characters long
                </mat-error>
                <mat-error *ngIf="personalInfoForm.get('password')?.hasError('pattern')">
                  Password must include uppercase, number, and special character
                </mat-error>
              </mat-form-field>
            </div>
  
            <div class="step-actions">
              <button mat-button matStepperNext [disabled]="!personalInfoForm.valid">Next</button>
            </div>
          </form>
        </mat-step>
  
        <!-- Address Details Step -->
        <mat-step [stepControl]="addressForm">
          <form [formGroup]="addressForm">
            <ng-template matStepLabel>Address Details</ng-template>
            
            <div class="form-container">
              <mat-form-field appearance="outline">
                <mat-label>Address Line 1</mat-label>
                <input matInput formControlName="address_line1" required>
                <mat-error *ngIf="addressForm.get('address_line1')?.hasError('required')">
                  Address is required
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Address Line 2</mat-label>
                <input matInput formControlName="address_line2">
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Locality</mat-label>
                <input matInput formControlName="locality">
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>State</mat-label>
                <mat-select formControlName="state_id" required (selectionChange)="onStateChange($event.value)">
                  <mat-option *ngFor="let state of states" [value]="state.id">
                    {{state.state_name}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="addressForm.get('state_id')?.hasError('required')">
                  State is required
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>City</mat-label>
                <mat-select formControlName="city_id" required>
                  <mat-option *ngFor="let city of cities" [value]="city.id">
                    {{city.city_name}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="addressForm.get('city_id')?.hasError('required')">
                  City is required
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Pincode</mat-label>
                <input matInput formControlName="pincode" required>
                <mat-error *ngIf="addressForm.get('pincode')?.hasError('required')">
                  Pincode is required
                </mat-error>
                <mat-error *ngIf="addressForm.get('pincode')?.hasError('pattern')">
                  Please enter a valid 6-digit pincode
                </mat-error>
              </mat-form-field>
            </div>
  
            <div class="step-actions">
              <button mat-button matStepperPrevious>Back</button>
              <button mat-button matStepperNext [disabled]="!addressForm.valid">Next</button>
            </div>
          </form>
        </mat-step>
  
        <!-- Farming Details Step -->
        <mat-step [stepControl]="farmingDetailsForm">
          <form [formGroup]="farmingDetailsForm">
            <ng-template matStepLabel>Farming Details</ng-template>
            
            <div class="form-container">
              <mat-form-field appearance="outline">
                <mat-label>Farm Size (in acres)</mat-label>
                <input matInput type="number" formControlName="farm_size" required>
                <mat-error *ngIf="farmingDetailsForm.get('farm_size')?.hasError('required')">
                  Farm size is required
                </mat-error>
                <mat-error *ngIf="farmingDetailsForm.get('farm_size')?.hasError('min')">
                  Farm size cannot be negative
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Farming Type</mat-label>
                <mat-select formControlName="farming_type_id" required>
                  <mat-option *ngFor="let type of farmingTypes" [value]="type.id">
                    {{type.type_name}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="farmingDetailsForm.get('farming_type_id')?.hasError('required')">
                  Farming type is required
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Irrigation Type</mat-label>
                <mat-select formControlName="irrigation_type_id" required>
                  <mat-option *ngFor="let type of irrigationTypes" [value]="type.id">
                    {{type.type_name}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="farmingDetailsForm.get('irrigation_type_id')?.hasError('required')">
                  Irrigation type is required
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Farming Experience (in years)</mat-label>
                <input matInput type="number" formControlName="farming_experience" required>
                <mat-error *ngIf="farmingDetailsForm.get('farming_experience')?.hasError('required')">
                  Farming experience is required
                </mat-error>
                <mat-error *ngIf="farmingDetailsForm.get('farming_experience')?.hasError('min')">
                  Experience cannot be negative
                </mat-error>
              </mat-form-field>
  
            
  
              <mat-form-field appearance="outline">
                <mat-label>Crops</mat-label>
                <mat-select formControlName="crops" multiple>
                  <mat-option *ngFor="let crop of crops" [value]="crop.id">
                    {{crop.crop_name}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Livestock</mat-label>
                <mat-select formControlName="livestock" multiple>
                  <mat-option *ngFor="let type of livestockTypes" [value]="type.id">
                    {{type.type_name}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Equipment</mat-label>
                <mat-select formControlName="equipment" multiple>
                  <mat-option *ngFor="let type of equipmentTypes" [value]="type.id">
                    {{type.type_name}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
  
            <div class="step-actions">
              <button mat-button matStepperPrevious>Back</button>
              <button mat-button matStepperNext [disabled]="!farmingDetailsForm.valid">Next</button>
            </div>
          </form>
        </mat-step>
  
        <!-- Government Details Step -->
        <mat-step [stepControl]="governmentDetailsForm">
          <form [formGroup]="governmentDetailsForm">
            <ng-template matStepLabel>Government Details</ng-template>
            
            <div class="form-container">
              <mat-form-field appearance="outline">
                <mat-label>Aadhaar Number</mat-label>
                <input matInput formControlName="aadhaar_number" required>
                <mat-error *ngIf="governmentDetailsForm.get('aadhaar_number')?.hasError('required')">
                  Aadhaar number is required
                </mat-error>
                <mat-error *ngIf="governmentDetailsForm.get('aadhaar_number')?.hasError('pattern')">
                  Please enter a valid 12-digit Aadhaar number
                </mat-error>
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Ration Card Number</mat-label>
                <input matInput formControlName="ration_card_number">
              </mat-form-field>
  
              <mat-form-field appearance="outline">
                <mat-label>Farmer ID</mat-label>
                <input matInput formControlName="farmer_id">
              </mat-form-field>
            </div>
  
            <div class="step-actions">
              <button mat-button matStepperPrevious>Back</button>
              <button mat-button matStepperNext [disabled]="!governmentDetailsForm.valid">Next</button>
            </div>
          </form>
        </mat-step>
  
        <!-- Documents Step -->
        <mat-step [stepControl]="documentsForm">
          <form [formGroup]="documentsForm">
            <ng-template matStepLabel>Documents</ng-template>
            
            <div class="form-container">
              <div class="file-upload-container">
                <label>ID Proof</label>
                <input type="file" (change)="onIdProofSelected($event)" accept="image/*,.pdf">
                <mat-error *ngIf="documentsForm.get('id_proof')?.hasError('required')">
                  ID proof is required
                </mat-error>
              </div>
  
              <div class="file-upload-container">
                <label>Profile Picture</label>
                <input type="file" (change)="onProfilePicSelected($event)" accept="image/*">
                <mat-error *ngIf="documentsForm.get('profile_pic')?.hasError('required')">
                  Profile picture is required
                </mat-error>
              </div>
            </div>
  
            <div class="step-actions">
              <button mat-button matStepperPrevious>Back</button>
              <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="!isFormsValid()">
                Submit
              </button>
            </div>
          </form>
        </mat-step>
      </mat-stepper>
    </mat-card>
  </div>
<div class="page-container">
    <mat-card class="mat-elevation-z2">
      <mat-toolbar class="page-header">
        <h1>Farmer Management</h1>
        <span class="spacer"></span>
        
      </mat-toolbar>
      <mat-divider></mat-divider>

<div class="farmers-list-container">
    <div class="header-actions">
      <mat-form-field appearance="outline" class="filter-field">
        <mat-label>Filter</mat-label>
        <input matInput placeholder="Search farmers..." (keyup)="applyFilter($event)" #input>
      </mat-form-field>
  
      <div class="button-container">
        <button mat-raised-button color="warn" [disabled]="!selection.hasValue()" (click)="deleteSelected()">
          <mat-icon>delete</mat-icon>
          Delete Selected
        </button>
        <button mat-raised-button color="primary" routerLink="add">
          <mat-icon>add</mat-icon>
          Add Farmer
        </button>
      </div>
    </div>
  
    <div class="mat-elevation-z8 table-container">
      <table mat-table [dataSource]="dataSource" matSort class="full-width-table">
        
        <!-- Checkbox Column -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="toggleAllRows()"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()"
                          [aria-label]="checkboxLabel()">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                          (change)="selection.toggle(row)"
                          [checked]="selection.isSelected(row)"
                          [aria-label]="checkboxLabel(row)">
            </mat-checkbox>
          </td>
        </ng-container>
        
        <!-- ID Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
          <td mat-cell *matCellDef="let row"> {{row.id}} </td>
        </ng-container>
  
        <!-- Name Column -->
        <ng-container matColumnDef="full_name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
          <td mat-cell *matCellDef="let row"> {{row.full_name}} </td>
        </ng-container>
  
        <!-- Mobile Column -->
        <ng-container matColumnDef="mobile_number">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Mobile </th>
          <td mat-cell *matCellDef="let row"> {{row.mobile_number}} </td>
        </ng-container>
  
        <!-- City Column -->
        <ng-container matColumnDef="city_name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> City </th>
          <td mat-cell *matCellDef="let row"> {{row.city_name || '-' }} </td>
        </ng-container>
  
        <!-- State Column -->
        <ng-container matColumnDef="state_name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> State </th>
          <td mat-cell *matCellDef="let row"> {{row.state_name || '-' }} </td>
        </ng-container>
  
        <!-- Farming Type Column -->
        <ng-container matColumnDef="farming_type">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Farming Type </th>
          <td mat-cell *matCellDef="let row"> {{row.farming_type || '-' }} </td>
        </ng-container>
  
        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef> Actions </th>
          <td mat-cell *matCellDef="let row">
            <button mat-icon-button color="primary" [routerLink]="['/farmers/edit', row.id]" aria-label="Edit Farmer">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deleteFarmer(row.id)" aria-label="Delete Farmer">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>
  
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns; trackBy: trackById;"></tr>
        <tr class="mat-row" *matNoDataRow>
          <td class="mat-cell" colspan="8">
            No data matching the filter "{{input.value}}"
          </td>
        </tr>
      </table>
  
      <mat-paginator [pageSizeOptions]="[5, 10, 25, 100]" aria-label="Select page of farmers"></mat-paginator>
    </div>
    
  </div>
</mat-card>
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { SelectionModel } from '@angular/cdk/collections';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { forkJoin } from 'rxjs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatCardModule } from '@angular/material/card';

import {
  FarmerService,
  Farmer,
} from '../../../core/auth/services/farmer.service';

@Component({
  selector: 'app-list-farmers',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSnackBarModule,
    MatCheckboxModule,
    MatToolbarModule,
    MatDividerModule,
    MatCardModule,
  ],
  templateUrl: './list-farmers.component.html',
  styleUrls: ['./list-farmers.component.scss'],
})
export class ListFarmersComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'id',
    'full_name',
    'mobile_number',
    'city_name',
    'state_name',
    'farming_type',
    'actions',
  ];

  dataSource: MatTableDataSource<Farmer>;
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  isLoading = false;
  selection = new SelectionModel<Farmer>(true, []);

  constructor(
    private farmerService: FarmerService,
    private snackBar: MatSnackBar
  ) {
    this.dataSource = new MatTableDataSource<Farmer>([]);
  }

  ngOnInit(): void {
    this.loadFarmers();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  trackById(index: number, farmer: Farmer): number {
    return farmer.id;
  }

  loadFarmers(): void {
    this.isLoading = true;
    this.farmerService.getAllFarmers().subscribe({
      next: (farmers: Farmer[]) => {
        // ✅ Response is an array, not an object with 'status'
        this.dataSource.data = farmers; // ✅ Assign the array directly
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading farmers:', error);
        this.snackBar.open('Error loading farmers', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
      },
    });
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  toggleAllRows() {
    if (this.isAllSelected()) {
      this.selection.clear();
      return;
    }
    this.selection.select(...this.dataSource.data);
  }

  checkboxLabel(row?: Farmer): string {
    if (!row) {
      return `${this.isAllSelected() ? 'deselect' : 'select'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${
      row.id
    }`;
  }

  deleteSelected(): void {
    if (
      confirm(
        `Are you sure you want to delete ${this.selection.selected.length} farmers?`
      )
    ) {
      const deletePromises = this.selection.selected.map((farmer) =>
        this.farmerService.deleteFarmer(farmer.id)
      );

      forkJoin(deletePromises).subscribe({
        next: (responses) => {
          this.snackBar.open(
            `Successfully deleted ${this.selection.selected.length} farmers`,
            'Close',
            {
              duration: 3000,
            }
          );
          this.selection.clear();
          this.loadFarmers();
        },
        error: (error) => {
          console.error('Error deleting farmers:', error);
          this.snackBar.open('Error deleting farmers', 'Close', {
            duration: 3000,
          });
        },
      });
    }
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  deleteFarmer(id: number): void {
    if (confirm('Are you sure you want to delete this farmer?')) {
      this.farmerService.deleteFarmer(id).subscribe({
        next: () => {
          this.snackBar.open('Farmer deleted successfully', 'Close', {
            duration: 3000,
          });
          this.loadFarmers();
        },
        error: (error) => {
          console.error('Error deleting farmer:', error);
          this.snackBar.open('Error deleting farmer', 'Close', {
            duration: 3000,
          });
        },
      });
    }
  }
}

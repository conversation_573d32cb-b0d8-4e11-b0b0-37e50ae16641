.farmers-list-container {
    padding: 24px;
    height: calc(100vh - 130px);
    display: flex;
    flex-direction: column;
    gap: 24px;

    .header-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 24px;
        background: white;
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
        .filter-field {
            width: 100%;
            max-width: 500px;
            margin: 0;

            .mat-mdc-form-field-subscript-wrapper {
                display: none;
            }
        }
    
        .button-container {
            display: flex;
            gap: 12px;
            
            button {
                min-width: 120px;
                
                mat-icon {
                    margin-right: 8px;
                }
            }
        }
    }

    .table-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        flex: 1;
        overflow: auto;
        position: relative;

        table {
            width: 100%;
            
            .mat-mdc-header-row {
                background-color: #f5f5f5;
            }

            .mat-mdc-header-cell {
                color: rgba(0, 0, 0, 0.87);
                font-weight: 600;
                font-size: 14px;
            }

            .mat-mdc-cell {
                color: rgba(0, 0, 0, 0.87);
                font-size: 14px;
            }

            .mat-column-select {
                width: 60px;
                padding: 0 8px;
            }

            .mat-column-id {
                width: 80px;
            }

            .mat-column-actions {
                width: 120px;
                text-align: center;

                button {
                    opacity: 0.7;
                    transition: opacity 0.2s;

                    &:hover {
                        opacity: 1;
                    }
                }
            }
        }

        .mat-mdc-row {
            transition: background-color 0.2s;

            &:hover {
                background-color: rgba(0, 0, 0, 0.04);
            }
        }

        .mat-mdc-paginator {
            border-top: 1px solid rgba(0, 0, 0, 0.12);
        }

        .no-data-row {
            padding: 16px;
            text-align: center;
            color: rgba(0, 0, 0, 0.54);
        }
    }

    // Loading state
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }
}
.content-container {
    padding: 24px;
    height: calc(100vh - 80px);
    overflow-y: auto;
  
    mat-card {
      .page-header {
        background: transparent;
        h1 {
          margin-left: 16px;
          font-size: 24px;
          font-weight: 500;
        }
      }
    }
  }
  
  // Loading overlay
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  // Stepper Styles
  ::ng-deep {
    .mat-horizontal-stepper-header-container {
      padding: 24px 24px 0;
    }
  
    .mat-horizontal-content-container {
      padding: 24px !important;
    }
  
    .mat-step-header {
      &.mat-step-header-ripple {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }
  
  // Form Container
  .form-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 24px 0;
  
    mat-form-field {
      width: 100%;
    }
  
    // Radio Group Styling
    .radio-group-container {
      display: flex;
      flex-direction: column;
      margin: 10px 0;
  
      .mat-label {
        color: rgba(0, 0, 0, 0.6);
        font-size: 14px;
        margin-bottom: 8px;
      }
  
      mat-radio-group {
        display: flex;
        gap: 16px;
        
        mat-radio-button {
          margin-right: 16px;
        }
      }
    }
  
    // File Upload Styling
    .file-upload-container {
      border: 2px dashed #ccc;
      padding: 20px;
      border-radius: 4px;
      text-align: center;
      transition: border-color 0.3s ease;
  
      &:hover {
        border-color: #666;
      }
  
      label {
        display: block;
        margin-bottom: 10px;
        color: rgba(0, 0, 0, 0.6);
        font-size: 14px;
      }
  
      .existing-file {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
  
        .preview-image {
          max-width: 200px;
          max-height: 200px;
          border-radius: 4px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
  
      input[type="file"] {
        width: 100%;
        &::-webkit-file-upload-button {
          visibility: hidden;
        }
        &::before {
          content: 'Select File';
          display: inline-block;
          background: #f5f5f5;
          border: 1px solid #ccc;
          border-radius: 4px;
          padding: 8px 16px;
          outline: none;
          white-space: nowrap;
          cursor: pointer;
          font-weight: 500;
          font-size: 14px;
        }
        &:hover::before {
          background: #eee;
        }
  
        &.hidden {
          display: none;
        }
      }
    }
  }
  
  // Step Actions
  .step-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding: 16px 0;
    border-top: 1px solid rgba(0, 0, 0, 0.12);
  
    button {
      min-width: 100px;
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .content-container {
      padding: 16px;
    }
  
    .form-container {
      grid-template-columns: 1fr;
    }
  
    ::ng-deep {
      .mat-horizontal-stepper-header-container {
        padding: 16px 16px 0;
      }
  
      .mat-horizontal-content-container {
        padding: 16px !important;
      }
    }
  }
  
  // Error States
  .mat-error {
    font-size: 12px;
    margin-top: 4px;
  }
  
  // Invalid Form Fields
  .ng-invalid.ng-touched {
    mat-form-field {
      .mat-form-field-outline {
        color: #f44336;
      }
    }
  }
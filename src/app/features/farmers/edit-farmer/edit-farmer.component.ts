import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatStepperModule } from '@angular/material/stepper';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatRadioModule } from '@angular/material/radio';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { forkJoin } from 'rxjs';

// Import services and interfaces
import {
  FarmerService,
  Farmer,
} from '../../../core/auth/services/farmer.service';
import {
  StateService,
  State,
} from '../../../core/auth/services/masters/state.service';
import {
  CityService,
  City,
} from '../../../core/auth/services/masters/city.service';
import {
  FarmingTypeService,
  FarmingType,
} from '../../../core/auth/services/masters/farming-type.service';
import {
  IrrigationTypeService,
  IrrigationType,
} from '../../../core/auth/services/masters/irrigation-type.service';
import {
  CropService,
  Crop,
} from '../../../core/auth/services/masters/crop.service';
import {
  EquipmentTypeService,
  EquipmentType,
} from '../../../core/auth/services/masters/equipment-type.service';
import {
  LivestockTypeService,
  LivestockType,
} from '../../../core/auth/services/masters/livestock-type.service';

@Component({
  selector: 'app-edit-farmer',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatStepperModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatRadioModule,
    MatCardModule,
    MatIconModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './edit-farmer.component.html',
  styleUrls: ['./edit-farmer.component.scss'],
})
export class EditFarmerComponent implements OnInit {
  personalInfoForm!: FormGroup;
  addressForm!: FormGroup;
  farmingDetailsForm!: FormGroup;
  governmentDetailsForm!: FormGroup;
  documentsForm!: FormGroup;

  states: State[] = [];
  cities: City[] = [];
  farmingTypes: FarmingType[] = [];
  irrigationTypes: IrrigationType[] = [];
  crops: Crop[] = [];
  equipmentTypes: EquipmentType[] = [];
  livestockTypes: LivestockType[] = [];

  isLoading = false;
  selectedFiles: { [key: string]: File } = {};
  farmer: Farmer | null = null;
  farmerId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private farmerService: FarmerService,
    private stateService: StateService,
    private cityService: CityService,
    private farmingTypeService: FarmingTypeService,
    private irrigationTypeService: IrrigationTypeService,
    private cropService: CropService,
    private equipmentTypeService: EquipmentTypeService,
    private livestockTypeService: LivestockTypeService
  ) {
    this.initializeForms();
  }

  ngOnInit() {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.farmerId = +params['id'];
        this.loadFarmerData();
      } else {
        this.showError('Farmer ID not found');
        this.goBack();
      }
    });
  }

  private initializeForms() {
    this.personalInfoForm = this.fb.group({
      full_name: ['', [Validators.required]],
      mobile_number: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      alternate_number: ['', [Validators.pattern('^[0-9]{10}$')]],
      email_id: ['', [Validators.email]],
      date_of_birth: ['', Validators.required],
      gender: ['', Validators.required],
      username: [{ value: '', disabled: true }],
    });

    this.addressForm = this.fb.group({
      address_line1: ['', Validators.required],
      address_line2: [''],
      locality: [''],
      state_id: ['', Validators.required],
      city_id: ['', Validators.required],
      pincode: ['', [Validators.required, Validators.pattern('^[0-9]{6}$')]],
    });

    this.farmingDetailsForm = this.fb.group({
      farm_size: ['', [Validators.required, Validators.min(0)]],
      farming_type_id: ['', Validators.required],
      irrigation_type_id: ['', Validators.required],
      farming_experience: ['', [Validators.required, Validators.min(0)]],
      crops: [[]],
      livestock: [[]],
      equipment: [[]],
    });

    this.governmentDetailsForm = this.fb.group({
      aadhaar_number: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{12}$')],
      ],
      ration_card_number: [''],
      farmer_id: [''],
    });

    this.documentsForm = this.fb.group({
      id_proof: [''],
      profile_pic: [''],
    });
  }

  private loadFarmerData() {
    this.isLoading = true;

    // Load master data and farmer details simultaneously
    forkJoin({
      farmer: this.farmerService.getFarmerById(this.farmerId!),
      states: this.stateService.getAll(),
      farmingTypes: this.farmingTypeService.getAll(),
      irrigationTypes: this.irrigationTypeService.getAll(),
      crops: this.cropService.getAll(),
      equipmentTypes: this.equipmentTypeService.getAll(),
      livestockTypes: this.livestockTypeService.getAll(),
    }).subscribe({
      next: (responses) => {
        // Store farmer data
        this.farmer = responses.farmer;

        // Store master data
        this.states = responses.states.data.filter((state) => state.is_active);
        this.farmingTypes = responses.farmingTypes.data.filter(
          (type) => type.is_active
        );
        this.irrigationTypes = responses.irrigationTypes.data.filter(
          (type) => type.is_active
        );
        this.crops = responses.crops.data.filter((crop) => crop.is_active);
        this.equipmentTypes = responses.equipmentTypes.data.filter(
          (type) => type.is_active
        );
        this.livestockTypes = responses.livestockTypes.data.filter(
          (type) => type.is_active
        );

        // Load cities based on state
        if (this.farmer.state_id) {
          this.onStateChange(this.farmer.state_id);
        }

        this.populateFormData();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading farmer data:', error);
        this.showError('Failed to load farmer data');
        this.isLoading = false;
        this.goBack();
      },
    });
  }

  private populateFormData() {
    if (!this.farmer) return;

    // Populate Personal Info Form
    this.personalInfoForm.patchValue({
      full_name: this.farmer.full_name,
      mobile_number: this.farmer.mobile_number,
      alternate_number: this.farmer.alternate_number,
      email_id: this.farmer.email_id,
      date_of_birth: this.farmer.date_of_birth,
      gender: this.farmer.gender,
      username: this.farmer.username,
    });

    // Populate Address Form
    this.addressForm.patchValue({
      address_line1: this.farmer.address_line1,
      address_line2: this.farmer.address_line2,
      locality: this.farmer.locality,
      state_id: this.farmer.state_id,
      city_id: this.farmer.city_id,
      pincode: this.farmer.pincode,
    });

    // Populate Farming Details Form
    this.farmingDetailsForm.patchValue({
      farm_size: this.farmer.farm_size,
      farming_type_id: this.farmer.farming_type_id,
      irrigation_type_id: this.farmer.irrigation_type_id,
      farming_experience: this.farmer.farming_experience,
      crops: JSON.parse(this.farmer.crops_grown || '[]'),
      livestock: JSON.parse(this.farmer.livestock_owned || '[]'),
      equipment: JSON.parse(this.farmer.tools_equipment || '[]'),
    });

    // Populate Government Details Form
    this.governmentDetailsForm.patchValue({
      aadhaar_number: this.farmer.aadhaar_number,
      ration_card_number: this.farmer.ration_card_number,
      farmer_id: this.farmer.farmer_id,
    });
  }

  onStateChange(stateId: number) {
    if (!stateId) {
      this.cities = [];
      return;
    }

    this.cityService.getCitiesByState(stateId).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.cities = response.data.filter((city) => city.is_active);
        }
      },
      error: (error) => {
        console.error('Error loading cities:', error);
        this.showError('Failed to load cities');
      },
    });
  }

  onIdProofSelected(event: Event) {
    const element = event.target as HTMLInputElement;
    const file = element.files?.[0];
    if (file) {
      this.selectedFiles['id_proof'] = file;
      this.documentsForm.patchValue({ id_proof: file.name });
    }
  }

  onProfilePicSelected(event: Event) {
    const element = event.target as HTMLInputElement;
    const file = element.files?.[0];
    if (file) {
      this.selectedFiles['profile_pic'] = file;
      this.documentsForm.patchValue({ profile_pic: file.name });
    }
  }

  onSubmit() {
    if (this.isFormsValid() && this.farmerId) {
      const formData = new FormData();

      // Append all form data
      Object.keys(this.personalInfoForm.value).forEach((key) => {
        const value = this.personalInfoForm.get(key)?.value;
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      });

      Object.keys(this.addressForm.value).forEach((key) => {
        const value = this.addressForm.get(key)?.value;
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      });

      Object.keys(this.farmingDetailsForm.value).forEach((key) => {
        const value = this.farmingDetailsForm.get(key)?.value;
        if (value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, value);
          }
        }
      });

      Object.keys(this.governmentDetailsForm.value).forEach((key) => {
        const value = this.governmentDetailsForm.get(key)?.value;
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      });

      // Append files if selected
      if (this.selectedFiles['id_proof']) {
        formData.append('id_proof', this.selectedFiles['id_proof']);
      }
      if (this.selectedFiles['profile_pic']) {
        formData.append('profile_pic', this.selectedFiles['profile_pic']);
      }

      this.isLoading = true;
      this.farmerService.updateFarmer(this.farmerId, formData).subscribe({
        next: () => {
          this.showSuccess('Farmer updated successfully');
          this.router.navigate(['../../'], { relativeTo: this.route });
        },
        error: (error) => {
          this.isLoading = false;
          this.showError(error.message || 'Failed to update farmer');
        },
      });
    } else {
      this.validateAllFormFields();
    }
  }

  private validateAllFormFields() {
    [
      this.personalInfoForm,
      this.addressForm,
      this.farmingDetailsForm,
      this.governmentDetailsForm,
    ].forEach((form) => {
      Object.keys(form.controls).forEach((key) => {
        const control = form.get(key);
        if (control) {
          control.markAsTouched();
        }
      });
    });
  }

  isFormsValid(): boolean {
    return (
      this.personalInfoForm.valid &&
      this.addressForm.valid &&
      this.farmingDetailsForm.valid &&
      this.governmentDetailsForm.valid
    );
  }

  goBack() {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  private showSuccess(message: string) {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['success-snackbar'],
    });
  }

  private showError(message: string) {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['error-snackbar'],
    });
  }
}

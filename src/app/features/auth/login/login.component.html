<div class="login-container">
    <mat-card>
      <mat-card-content>
        <h2>FPO Traders</h2>
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <mat-form-field>
            <mat-label>Username or Email</mat-label>
            <input matInput formControlName="identifier">
          </mat-form-field>
  
          <mat-form-field>
            <mat-label>Password</mat-label>
            <input matInput type="password" formControlName="password">
          </mat-form-field>
  
          <button mat-raised-button color="primary" type="submit">Login</button>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
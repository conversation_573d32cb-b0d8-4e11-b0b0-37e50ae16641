// login.component.scss
.login-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fafafa;
  }
  
  .login-box {
    width: 100%;
    max-width: 400px;
    padding: 0 20px;
  }
  
  .login-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      font-size: 24px;
      font-weight: 500;
      color: #333;
      margin: 0;
    }
  }
  
  mat-card {
    padding: 30px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .full-width {
    width: 100%;
  }
  
  mat-form-field {
    .mat-mdc-form-field-flex {
      background-color: white;
    }
  }
  
  button {
    height: 45px;
    font-size: 16px;
  }
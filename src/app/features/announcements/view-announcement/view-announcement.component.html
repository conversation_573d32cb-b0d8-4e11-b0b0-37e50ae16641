<div class="view-announcement-container">
    <mat-card>
      <div class="card-header">
        <h2>View Announcement</h2>
        <div class="header-actions">
          <button mat-icon-button color="primary" routerLink="/announcements" matTooltip="Back to Announcements">
            <mat-icon>arrow_back</mat-icon>
          </button>
          <button mat-raised-button color="primary" [routerLink]="['/announcements/edit', announcementId]" 
                  *ngIf="!isLoading && announcement">
            <mat-icon>edit</mat-icon> Edit
          </button>
        </div>
      </div>
  
      <div class="announcement-content">
        <div *ngIf="isLoading" class="loading-shade">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
  
        <div *ngIf="!isLoading && announcement" class="announcement-details">
          <div class="announcement-header">
            <h1 class="announcement-title">{{announcement.title}}</h1>
            <div class="announcement-meta">
              <div class="meta-item">
                <mat-icon>person</mat-icon>
                <span>Posted by: {{announcement.created_by_name || 'Unknown'}}</span>
              </div>
              <div class="meta-item">
                <mat-icon>event</mat-icon>
                <span>Posted on: {{announcement.created_at | date:'medium'}}</span>
              </div>
              <div class="meta-item" *ngIf="announcement.updated_at && announcement.updated_at !== announcement.created_at">
                <mat-icon>update</mat-icon>
                <span>Updated on: {{announcement.updated_at | date:'medium'}} by {{announcement.updated_by_name || 'Unknown'}}</span>
              </div>
            </div>
          </div>
  
          <div class="announcement-image" *ngIf="announcement.image_url">
            <img [src]="getFullImageUrl(announcement.image_url)" alt="Announcement Image" 
                (click)="viewImage(announcement.image_url)" (error)="handleImageError($event)">
          </div>
  
          <div class="announcement-description" *ngIf="announcement.description">
            <h3>Description</h3>
            <div class="description-content">
              <p>{{announcement.description}}</p>
            </div>
          </div>
  
          <div class="announcement-actions">
            <button mat-raised-button color="warn" (click)="deleteAnnouncement()">
              <mat-icon>delete</mat-icon> Delete
            </button>
            <button mat-raised-button color="primary" [routerLink]="['/announcements/edit', announcementId]">
              <mat-icon>edit</mat-icon> Edit
            </button>
            <button mat-stroked-button routerLink="/announcements">
              <mat-icon>list</mat-icon> All Announcements
            </button>
          </div>
        </div>
  
        <div *ngIf="!isLoading && !announcement" class="no-data">
          <mat-icon>error</mat-icon>
          <span>Announcement not found</span>
          <button mat-raised-button color="primary" routerLink="/announcements">Back to Announcements</button>
        </div>
      </div>
    </mat-card>
  </div>
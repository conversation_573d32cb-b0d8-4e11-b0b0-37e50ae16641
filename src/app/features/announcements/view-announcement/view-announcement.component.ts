// src/app/features/announcements/view-announcement/view-announcement.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import {
  AnnouncementService,
  Announcement,
} from '../../../core/auth/services/announcement.service';
import { environment } from '../../../../environments/environment';
import { ImageViewerComponent } from '../../../components/image-viewer.component';

@Component({
  selector: 'app-view-announcement',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './view-announcement.component.html',
  styleUrls: ['./view-announcement.component.scss'],
})
export class ViewAnnouncementComponent implements OnInit {
  announcementId: number = 0;
  announcement: Announcement | null = null;
  isLoading = false;

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private announcementService: AnnouncementService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.announcementId = +params['id'];
        this.loadAnnouncementData(this.announcementId);
      } else {
        this.router.navigate(['/announcements']);
      }
    });
  }

  loadAnnouncementData(id: number): void {
    this.isLoading = true;
    this.announcementService.getAnnouncementById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.announcement = response.data;
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error loading announcement', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
      },
    });
  }

  deleteAnnouncement(): void {
    if (confirm('Are you sure you want to delete this announcement?')) {
      this.announcementService
        .deleteAnnouncement(this.announcementId)
        .subscribe({
          next: (response) => {
            if (response.status === 'success') {
              this.snackBar.open('Announcement deleted successfully', 'Close', {
                duration: 3000,
                horizontalPosition: 'end',
                verticalPosition: 'top',
              });
              this.router.navigate(['/announcements']);
            }
          },
          error: (error) => {
            this.snackBar.open('Error deleting announcement', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
          },
        });
    }
  }

  viewImage(imageUrl: string): void {
    this.dialog.open(ImageViewerComponent, {
      data: { imageUrl: this.getFullImageUrl(imageUrl) },
      width: '80%',
      maxWidth: '1000px',
    });
  }

  getFullImageUrl(imageUrl: string | null | undefined): string {
    if (!imageUrl) return '';
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    return `${this.apiBaseUrl}${imageUrl}`;
  }

  handleImageError(event: any): void {
    event.target.src = 'assets/images/placeholder-image.png';
  }
}

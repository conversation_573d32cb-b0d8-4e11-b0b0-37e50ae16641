.view-announcement-container {
    padding: 24px;
    width: 50%;
    
    mat-card {
      overflow: hidden;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
        
        button {
          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }
    
    .announcement-content {
      position: relative;
      padding: 24px;
      
      .loading-shade {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .announcement-details {
        .announcement-header {
          margin-bottom: 24px;
          
          .announcement-title {
            font-size: 28px;
            font-weight: 500;
            color: #333;
            margin-bottom: 16px;
          }
          
          .announcement-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
            
            .meta-item {
              display: flex;
              align-items: center;
              color: rgba(0, 0, 0, 0.6);
              font-size: 14px;
              
              mat-icon {
                margin-right: 8px;
                font-size: 18px;
                height: 18px;
                width: 18px;
              }
            }
          }
        }
        
        .announcement-image {
          margin-bottom: 24px;
          max-width: 100%;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          
          img {
            width: 100%;
            max-height: 400px;
            object-fit: contain;
            transition: transform 0.3s;
            
            &:hover {
              transform: scale(1.02);
            }
          }
        }
        
        .announcement-description {
          margin-bottom: 32px;
          
          h3 {
            font-size: 20px;
            font-weight: 500;
            color: #333;
            margin-bottom: 16px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
          }
          
          .description-content {
            color: rgba(0, 0, 0, 0.7);
            line-height: 1.6;
            white-space: pre-line; // Respects line breaks
            
            p {
              margin-bottom: 16px;
            }
          }
        }
        
        .announcement-actions {
          display: flex;
          gap: 16px;
          margin-top: 32px;
          padding-top: 16px;
          border-top: 1px solid #eee;
        }
      }
      
      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 48px 0;
        
        mat-icon {
          font-size: 64px;
          width: 64px;
          height: 64px;
          margin-bottom: 16px;
          color: #bdbdbd;
        }
        
        span {
          font-size: 20px;
          color: #757575;
          margin-bottom: 24px;
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .view-announcement-container {
      padding: 16px 8px;
      
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        
        .header-actions {
          width: 100%;
          justify-content: space-between;
        }
      }
      
      .announcement-content {
        padding: 16px;
        
        .announcement-details {
          .announcement-header {
            .announcement-title {
              font-size: 24px;
            }
            
            .announcement-meta {
              flex-direction: column;
              gap: 8px;
            }
          }
          
          .announcement-actions {
            flex-wrap: wrap;
          }
        }
      }
    }
  }
.announcements-container {
    padding: 24px;
    
    mat-card {
      overflow: hidden;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        button {
          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }
    
    .table-container {
      position: relative;
      min-height: 400px;
      overflow: auto;
      
      .loading-shade {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .announcements-table {
        width: 100%;
        
        .mat-column-actions {
          width: 150px;
          text-align: right;
        }
        
        .mat-column-id {
          width: 60px;
        }
  
        .mat-column-image {
          width: 100px;
          padding: 8px;
        }
        
        .mat-column-created_at {
          width: 180px;
        }
        
        .mat-column-created_by {
          width: 120px;
        }
        
        .announcement-image-container {
          display: flex;
          justify-content: center;
          padding: 5px;
        }
        
        .announcement-thumbnail {
          width: 80px;
          height: 60px;
          object-fit: cover;
          border-radius: 4px;
          cursor: pointer;
          transition: transform 0.2s;
          
          &:hover {
            transform: scale(1.1);
          }
        }
        
        .no-image {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 80px;
          height: 60px;
          background-color: #f0f0f0;
          border-radius: 4px;
          margin: 0 auto;
          
          mat-icon {
            font-size: 24px;
            color: #bdbdbd;
          }
        }
        
        .description-preview {
          color: rgba(0, 0, 0, 0.65);
          max-width: 300px;
          white-space: normal;
          line-height: 1.4;
        }
        
        .mat-mdc-row:hover {
          background-color: rgba(0, 0, 0, 0.04);
        }
      }
      
      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 32px;
        color: rgba(0, 0, 0, 0.54);
        
        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          margin-bottom: 16px;
        }
      }
    }
  }
  
  // Make table layout more compact
  ::ng-deep {
    .mat-mdc-table {
      .mat-mdc-row, .mat-mdc-header-row {
        height: 64px; // Slightly increased for image display
      }
      
      .mat-mdc-cell, .mat-mdc-header-cell {
        padding: 0 12px; // Reduced horizontal padding
      }
    }
    
    // Ensure proper alignment for paginator
    .mat-mdc-paginator {
      border-top: 1px solid rgba(0, 0, 0, 0.12);
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .announcements-container {
      padding: 16px 8px;
      
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        
        .header-actions {
          width: 100%;
        }
      }
    }
  }
<div class="announcements-container">
    <mat-card>
      <div class="card-header">
        <h2>Announcements</h2>
        <div class="header-actions">
          <button mat-raised-button color="primary" routerLink="/announcements/add">
            <mat-icon>add</mat-icon> Add Announcement
          </button>
        </div>
      </div>
  
      <!-- Announcements Table -->
      <div class="table-container">
        <div *ngIf="isLoading" class="loading-shade">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
  
        <table mat-table [dataSource]="dataSource" matSort class="announcements-table">
          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
            <td mat-cell *matCellDef="let announcement"> {{announcement.id}} </td>
          </ng-container>
  
          <!-- Image Column -->
          <ng-container matColumnDef="image">
            <th mat-header-cell *matHeaderCellDef> Image </th>
            <td mat-cell *matCellDef="let announcement">
              <div class="announcement-image-container">
                <img *ngIf="announcement.image_url" 
                  [src]="getFullImageUrl(announcement.image_url)" 
                  alt="Announcement Image" 
                  class="announcement-thumbnail"
                  (click)="viewImage(announcement.image_url)"
                  (error)="handleImageError($event)">
                <div *ngIf="!announcement.image_url" class="no-image">
                  <mat-icon>image_not_supported</mat-icon>
                </div>
              </div>
            </td>
          </ng-container>
  
          <!-- Title Column -->
          <ng-container matColumnDef="title">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Title </th>
            <td mat-cell *matCellDef="let announcement"> {{announcement.title}} </td>
          </ng-container>
  
          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef> Description </th>
            <td mat-cell *matCellDef="let announcement"> 
              <div class="description-preview">
                {{announcement.description ? (announcement.description | slice:0:100) + (announcement.description.length > 100 ? '...' : '') : '-'}}
              </div>
            </td>
          </ng-container>
  
          <!-- Created By Column -->
          <ng-container matColumnDef="created_by">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
            <td mat-cell *matCellDef="let announcement"> {{announcement.created_by_name || '-'}} </td>
          </ng-container>
  
          <!-- Created At Column -->
          <ng-container matColumnDef="created_at">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Created At </th>
            <td mat-cell *matCellDef="let announcement"> {{announcement.created_at | date:'medium'}} </td>
          </ng-container>
  
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let announcement">
              <button mat-icon-button color="primary" 
                      [routerLink]="['/announcements/view', announcement.id]" 
                      matTooltip="View Details">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button color="primary" 
                      [routerLink]="['/announcements/edit', announcement.id]" 
                      matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" 
                      (click)="deleteAnnouncement(announcement.id)" 
                      matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  
          <!-- No Data Row -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="7">
              <div class="no-data">
                <mat-icon>info</mat-icon>
                <span>No announcements found</span>
              </div>
            </td>
          </tr>
        </table>
  
        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </div>
    </mat-card>
  </div>
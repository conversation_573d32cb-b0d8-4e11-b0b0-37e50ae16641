.edit-announcement-container {
    padding: 24px;
    
    mat-card {
      overflow: hidden;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }
    
    .form-container {
      position: relative;
      padding: 24px;
      
      .loading-shade {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .form-row {
        margin-bottom: 16px;
      }
      
      .full-width {
        width: 100%;
      }
      
      .image-upload-section {
        margin-bottom: 24px;
        
        .upload-container {
          display: flex;
          flex-direction: column;
          
          .upload-label {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 12px;
            color: rgba(0, 0, 0, 0.6);
          }
          
          .upload-preview, .upload-placeholder {
            width: 100%;
            max-width: 400px;
            height: 240px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
          }
          
          .upload-preview {
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          
          .upload-placeholder {
            flex-direction: column;
            background-color: #f9f9f9;
            
            mat-icon {
              font-size: 48px;
              width: 48px;
              height: 48px;
              margin-bottom: 16px;
              color: #757575;
            }
            
            span {
              color: #757575;
              font-size: 14px;
            }
          }
          
          .upload-actions {
            display: flex;
            gap: 16px;
            margin-top: 8px;
          }
        }
      }
      
      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 16px;
        margin-top: 24px;
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .edit-announcement-container {
      padding: 16px 8px;
      
      .form-container {
        padding: 16px;
        
        .image-upload-section {
          .upload-container {
            .upload-preview, .upload-placeholder {
              max-width: 100%;
              height: 200px;
            }
          }
        }
      }
    }
  }
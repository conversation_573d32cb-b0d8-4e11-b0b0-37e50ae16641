// src/app/features/announcements/edit-announcement/edit-announcement.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

import { AnnouncementService } from '../../../core/auth/services/announcement.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-edit-announcement',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatCardModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
  ],
  templateUrl: './edit-announcement.component.html',
  styleUrls: ['./edit-announcement.component.scss'],
})
export class EditAnnouncementComponent implements OnInit {
  announcementForm: FormGroup;
  isLoading = false;
  isSubmitting = false;
  announcementId: number = 0;

  // Image handling
  selectedImage: File | null = null;
  imagePreviewUrl: string | null = null;
  existingImageUrl: string | null = null;
  removeExistingImage = false;

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private announcementService: AnnouncementService,
    private snackBar: MatSnackBar
  ) {
    this.announcementForm = this.fb.group({
      title: ['', [Validators.required]],
      description: [''],
      // We don't include image in the form as it will be handled separately
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.announcementId = +params['id'];
        this.loadAnnouncementData(this.announcementId);
      } else {
        this.router.navigate(['/announcements']);
      }
    });
  }

  loadAnnouncementData(id: number): void {
    this.isLoading = true;
    this.announcementService.getAnnouncementById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          const announcement = response.data;
          this.announcementForm.patchValue({
            title: announcement.title,
            description: announcement.description || '',
          });

          if (announcement.image_url) {
            this.existingImageUrl = announcement.image_url;
          }
        } else {
          this.snackBar.open('Announcement not found', 'Close', {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          });
          this.router.navigate(['/announcements']);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error loading announcement', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
        this.router.navigate(['/announcements']);
      },
    });
  }

  onImageSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      const file = input.files[0];
      this.selectedImage = file;

      // Create a preview URL
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreviewUrl = reader.result as string;
      };
      reader.readAsDataURL(file);

      this.removeExistingImage = true;
    }
  }

  removeImage(): void {
    this.selectedImage = null;
    this.imagePreviewUrl = null;
    this.removeExistingImage = true;
    this.existingImageUrl = null;
  }

  onSubmit(): void {
    if (this.announcementForm.invalid) {
      return;
    }

    this.isSubmitting = true;

    // Prepare form data
    const formData = new FormData();
    formData.append('title', this.announcementForm.value.title);

    if (this.announcementForm.value.description) {
      formData.append('description', this.announcementForm.value.description);
    }

    // Handle image
    if (this.selectedImage) {
      formData.append('image', this.selectedImage);
    } else if (this.removeExistingImage) {
      // If the user wants to remove the existing image
      formData.append('image_url', '');
    }

    // Update announcement
    this.announcementService
      .updateAnnouncement(this.announcementId, formData)
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open('Announcement updated successfully', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
            this.router.navigate(['/announcements']);
          } else {
            this.snackBar.open('Error updating announcement', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          this.snackBar.open('Error updating announcement', 'Close', {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          });
          this.isSubmitting = false;
        },
      });
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    return `${this.apiBaseUrl}${imageUrl}`;
  }
}

// src/app/features/announcements/announcement-form/announcement-form.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

import { AnnouncementService } from '../../../core/auth/services/announcement.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-announcement-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatCardModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
  ],
  templateUrl: './add-announcements.component.html',
  styleUrls: ['./add-announcements.component.scss'],
})
export class AddAnnouncementsComponent implements OnInit {
  announcementForm: FormGroup;
  isEditMode = false;
  isLoading = false;
  isSubmitting = false;
  announcementId: number | null = null;

  // Image handling
  selectedImage: File | null = null;
  imagePreviewUrl: string | null = null;
  existingImageUrl: string | null = null;
  removeExistingImage = false;

  apiBaseUrl = environment.apiUrl.endsWith('/api')
    ? environment.apiUrl.substring(0, environment.apiUrl.length - 4)
    : environment.apiUrl;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private announcementService: AnnouncementService,
    private snackBar: MatSnackBar
  ) {
    this.announcementForm = this.fb.group({
      title: ['', [Validators.required]],
      description: [''],
      // We don't include image in the form as it will be handled separately
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.isEditMode = true;
        this.announcementId = +params['id'];
        this.loadAnnouncementData(this.announcementId);
      }
    });
  }

  loadAnnouncementData(id: number): void {
    this.isLoading = true;
    this.announcementService.getAnnouncementById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          const announcement = response.data;
          this.announcementForm.patchValue({
            title: announcement.title,
            description: announcement.description || '',
          });

          if (announcement.image_url) {
            this.existingImageUrl = announcement.image_url;
          }
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Error loading announcement', 'Close', {
          duration: 3000,
          horizontalPosition: 'end',
          verticalPosition: 'top',
        });
        this.isLoading = false;
        this.router.navigate(['/announcements']);
      },
    });
  }

  onImageSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      const file = input.files[0];
      this.selectedImage = file;

      // Create a preview URL
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreviewUrl = reader.result as string;
      };
      reader.readAsDataURL(file);

      this.removeExistingImage = true;
    }
  }

  removeImage(): void {
    this.selectedImage = null;
    this.imagePreviewUrl = null;
    this.removeExistingImage = true;
    this.existingImageUrl = null;
  }

  onSubmit(): void {
    if (this.announcementForm.invalid) {
      return;
    }

    this.isSubmitting = true;

    // Prepare form data
    const formData = new FormData();
    formData.append('title', this.announcementForm.value.title);

    if (this.announcementForm.value.description) {
      formData.append('description', this.announcementForm.value.description);
    }

    // Handle image
    if (this.selectedImage) {
      formData.append('image', this.selectedImage);
    } else if (this.removeExistingImage && this.isEditMode) {
      // If in edit mode and the user wants to remove the existing image
      formData.append('image_url', '');
    }

    if (this.isEditMode && this.announcementId) {
      // Update existing announcement
      this.announcementService
        .updateAnnouncement(this.announcementId, formData)
        .subscribe({
          next: (response) => {
            if (response.status === 'success') {
              this.snackBar.open('Announcement updated successfully', 'Close', {
                duration: 3000,
                horizontalPosition: 'end',
                verticalPosition: 'top',
              });
              this.router.navigate(['/announcements']);
            }
            this.isSubmitting = false;
          },
          error: (error) => {
            this.snackBar.open('Error updating announcement', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
            this.isSubmitting = false;
          },
        });
    } else {
      // Create new announcement
      this.announcementService.createAnnouncement(formData).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.snackBar.open('Announcement created successfully', 'Close', {
              duration: 3000,
              horizontalPosition: 'end',
              verticalPosition: 'top',
            });
            this.router.navigate(['/announcements']);
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          this.snackBar.open('Error creating announcement', 'Close', {
            duration: 3000,
            horizontalPosition: 'end',
            verticalPosition: 'top',
          });
          this.isSubmitting = false;
        },
      });
    }
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    return `${this.apiBaseUrl}${imageUrl}`;
  }
}

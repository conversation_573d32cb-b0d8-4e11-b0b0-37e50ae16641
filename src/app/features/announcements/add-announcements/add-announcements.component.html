<div class="announcement-form-container">
    <mat-card>
      <div class="card-header">
        <h2>{{ isEditMode ? 'Edit Announcement' : 'Add New Announcement' }}</h2>
        <button mat-icon-button color="primary" routerLink="/announcements" matTooltip="Back to Announcements">
          <mat-icon>arrow_back</mat-icon>
        </button>
      </div>
  
      <div class="form-container">
        <div *ngIf="isLoading" class="loading-shade">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
  
        <form [formGroup]="announcementForm" (ngSubmit)="onSubmit()">
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Title</mat-label>
              <input matInput formControlName="title" placeholder="Enter announcement title" required>
              <mat-error *ngIf="announcementForm.get('title')?.hasError('required')">
                Title is required
              </mat-error>
            </mat-form-field>
          </div>
  
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" placeholder="Enter announcement description" rows="5"></textarea>
            </mat-form-field>
          </div>
  
          <div class="form-row image-upload-section">
            <div class="upload-container">
              <div class="upload-label">Announcement Image</div>
              <div class="upload-preview" *ngIf="imagePreviewUrl || existingImageUrl" 
                  (click)="fileInput.click()">
                <img [src]="imagePreviewUrl || getFullImageUrl(existingImageUrl)" alt="Announcement Image Preview">
              </div>
              <div class="upload-placeholder" *ngIf="!imagePreviewUrl && !existingImageUrl" 
                  (click)="fileInput.click()">
                <mat-icon>add_photo_alternate</mat-icon>
                <span>Click to upload an image</span>
              </div>
              <input type="file" #fileInput hidden accept="image/*" (change)="onImageSelected($event)">
              
              <div class="upload-actions">
                <button type="button" mat-raised-button color="primary" (click)="fileInput.click()">
                  <mat-icon>file_upload</mat-icon> 
                  {{ imagePreviewUrl || existingImageUrl ? 'Change Image' : 'Upload Image' }}
                </button>
                <button type="button" mat-stroked-button color="warn" 
                        *ngIf="imagePreviewUrl || existingImageUrl"
                        (click)="removeImage()">
                  <mat-icon>delete</mat-icon> Remove Image
                </button>
              </div>
            </div>
          </div>
  
          <div class="form-actions">
            <button type="button" mat-stroked-button routerLink="/announcements">
              Cancel
            </button>
            <button type="submit" mat-raised-button color="primary" [disabled]="announcementForm.invalid || isSubmitting">
              {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update' : 'Create') }}
            </button>
          </div>
        </form>
      </div>
    </mat-card>
  </div>
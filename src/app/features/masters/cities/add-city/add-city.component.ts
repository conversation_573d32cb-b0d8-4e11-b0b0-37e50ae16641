import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';

import { CityService } from '../../../../core/auth/services/masters/city.service';
import { StateService } from '../../../../core/auth/services/masters/state.service';
import { State } from '../../../../core/auth/services/masters/state.service';

@Component({
  selector: 'app-add-city',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
  ],
  templateUrl: './add-city.component.html',
  styleUrls: ['./add-city.component.scss'],
})
export class AddCityComponent implements OnInit {
  cityForm: FormGroup = this.fb.group({
    state_id: ['', Validators.required],
    city_name: ['', Validators.required],
    is_active: [true],
  });

  states: State[] = [];
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private cityService: CityService,
    private stateService: StateService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadStates();
  }

  loadStates(): void {
    this.stateService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.states = response.data.filter((state) => state.is_active);
        }
      },
      error: (error) => {
        this.showError('Error loading states');
        console.error('Error loading states:', error);
      },
    });
  }

  onSubmit(): void {
    if (this.cityForm.valid) {
      this.isLoading = true;
      this.cityService.create(this.cityForm.value).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('City created successfully');
            this.router.navigate(['../'], { relativeTo: this.route });
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.showError(error.error.message || 'Error creating city');
          this.isLoading = false;
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

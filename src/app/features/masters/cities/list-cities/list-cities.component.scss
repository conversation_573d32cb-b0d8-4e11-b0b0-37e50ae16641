.content-container {
    padding: 24px;
}

.page-header {
    background: transparent;
    
    h1 {
        font-size: 24px;
        margin: 0;
    }
}

.spacer {
    flex: 1 1 auto;
}

.filters-container {
    padding: 16px;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;

    mat-form-field {
        flex: 1;
        min-width: 200px;
    }
}

.table-container {
    position: relative;
    min-height: 200px;
    overflow: auto;
}

.loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.city-table {
    width: 100%;

    .mat-mdc-row:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }

    .mat-column-actions {
        width: 120px;
        text-align: center;
    }

    .mat-column-is_active {
        width: 100px;
        text-align: center;

        .active {
            color: #4caf50;
        }

        .inactive {
            color: #f44336;
        }
    }
}

.mat-mdc-row .mat-mdc-cell {
    border-bottom: 1px solid transparent;
    border-top: 1px solid transparent;
}
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  MatTableModule,
  MatTable,
  MatTableDataSource,
} from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

import {
  CityService,
  City,
} from '../../../../core/auth/services/masters/city.service';
import {
  StateService,
  State,
} from '../../../../core/auth/services/masters/state.service';

@Component({
  selector: 'app-list-cities',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatToolbarModule,
    MatDividerModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
  ],
  templateUrl: './list-cities.component.html',
  styleUrls: ['./list-cities.component.scss'],
})
export class ListCitiesComponent implements OnInit {
  displayedColumns: string[] = [
    'city_name',
    'state_name',
    'is_active',
    'actions',
  ];
  dataSource: MatTableDataSource<City>;
  allCities: City[] = [];
  states: State[] = [];
  selectedStateId: number | null = null;
  isLoading = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatTable) table!: MatTable<any>;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private cityService: CityService,
    private stateService: StateService,
    private snackBar: MatSnackBar
  ) {
    this.dataSource = new MatTableDataSource<City>([]);
  }

  ngOnInit(): void {
    this.loadStates();
    this.loadCities();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  loadStates(): void {
    this.stateService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.states = response.data.filter((state) => state.is_active);
        }
      },
      error: (error) => {
        this.showError('Error loading states');
        console.error('Error loading states:', error);
      },
    });
  }

  loadCities(): void {
    this.isLoading = true;
    const request = this.selectedStateId
      ? this.cityService.getCitiesByState(this.selectedStateId)
      : this.cityService.getAll();

    request.subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.allCities = response.data;
          this.dataSource.data = this.allCities;
          this.setupFilter();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('Error loading cities');
        this.isLoading = false;
      },
    });
  }

  private setupFilter(): void {
    this.dataSource.filterPredicate = (data: City, filter: string) => {
      const filterValue = filter.toLowerCase();
      return (
        data.city_name.toLowerCase().includes(filterValue) ||
        (data.state_name || '').toLowerCase().includes(filterValue)
      );
    };
  }

  filterCities(): void {
    this.loadCities();
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addCity(): void {
    this.router.navigate(['add'], { relativeTo: this.route });
  }

  editCity(cityId: number): void {
    this.router.navigate(['edit', cityId], { relativeTo: this.route });
  }

  // Update add-city.component.ts and edit-city.component.ts
  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  deleteCity(id: number): void {
    if (confirm('Are you sure you want to delete this city?')) {
      this.cityService.delete(id).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('City deleted successfully');
            this.loadCities();
          }
        },
        error: (error) => {
          this.showError(error.error.message || 'Error deleting city');
        },
      });
    }
  }

  toggleStatus(city: City): void {
    if (!city.id) return;

    this.cityService.toggleStatus(city.id, !city.is_active).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.showSuccess(
            `City ${city.is_active ? 'deactivated' : 'activated'} successfully`
          );
          this.loadCities();
        }
      },
      error: (error) => {
        this.showError(error.error.message || 'Error updating city status');
      },
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

<div class="content-container">
    <mat-card>
        <mat-toolbar class="page-header">
            <h1>Cities</h1>
            <span class="spacer"></span>
            <button mat-raised-button color="primary" (click)="addCity()">
                <mat-icon>add</mat-icon>
                Add City
            </button>
        </mat-toolbar>
        <mat-divider></mat-divider>

        <!-- Filters -->
        <div class="filters-container">
            <mat-form-field appearance="outline">
                <mat-label>Filter by State</mat-label>
                <mat-select [(ngModel)]="selectedStateId" (selectionChange)="filterCities()">
                    <mat-option [value]="null">All States</mat-option>
                    <mat-option *ngFor="let state of states" [value]="state.id">
                        {{state.state_name}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
                <mat-label>Search Cities</mat-label>
                <input matInput (keyup)="applyFilter($event)" placeholder="Type to search..." #input>
                <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
        </div>

        <div class="table-container">
            <div class="loading-container" *ngIf="isLoading">
                <mat-spinner diameter="40"></mat-spinner>
            </div>

            <table mat-table [dataSource]="dataSource" class="city-table" *ngIf="!isLoading">
                <!-- City Name Column -->
                <ng-container matColumnDef="city_name">
                    <th mat-header-cell *matHeaderCellDef> City Name </th>
                    <td mat-cell *matCellDef="let element"> {{element.city_name}} </td>
                </ng-container>

                <!-- State Name Column -->
                <ng-container matColumnDef="state_name">
                    <th mat-header-cell *matHeaderCellDef> State </th>
                    <td mat-cell *matCellDef="let element"> {{element.state_name}} </td>
                </ng-container>

                <!-- Status Column -->
                <ng-container matColumnDef="is_active">
                    <th mat-header-cell *matHeaderCellDef> Status </th>
                    <td mat-cell *matCellDef="let element">
                        <mat-icon [ngClass]="{'active': element.is_active, 'inactive': !element.is_active}">
                            {{element.is_active ? 'check_circle' : 'cancel'}}
                        </mat-icon>
                    </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef> Actions </th>
                    <td mat-cell *matCellDef="let element">
                        <button mat-icon-button color="primary" 
                                (click)="editCity(element.id)"
                                matTooltip="Edit City">
                            <mat-icon>edit</mat-icon>
                        </button>
                        <button mat-icon-button 
                                [color]="element.is_active ? 'warn' : 'primary'"
                                (click)="toggleStatus(element)"
                                [matTooltip]="element.is_active ? 'Deactivate' : 'Activate'">
                            <mat-icon>{{element.is_active ? 'block' : 'check_circle'}}</mat-icon>
                        </button>
                        <button mat-icon-button color="warn" 
                                (click)="deleteCity(element.id)"
                                matTooltip="Delete City">
                            <mat-icon>delete</mat-icon>
                        </button>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                <!-- No Data Row -->
                <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" colspan="4">
                        No cities found
                    </td>
                </tr>
            </table>

            <mat-paginator [pageSizeOptions]="[10, 25, 50]"
                          showFirstLastButtons
                          aria-label="Select page of cities">
            </mat-paginator>
        </div>
    </mat-card>
</div>
<div class="content-container">
    <mat-card>
        <mat-toolbar class="page-header">
            <button mat-icon-button (click)="goBack()">
                <mat-icon>arrow_back</mat-icon>
            </button>
            <h1>Edit City</h1>
        </mat-toolbar>
        <mat-divider></mat-divider>

        <div class="loading-container" *ngIf="isLoading">
            <mat-spinner diameter="40"></mat-spinner>
        </div>

        <form *ngIf="!isLoading" [formGroup]="cityForm" (ngSubmit)="onSubmit()" class="form-container">
            <!-- State Selection -->
            <mat-form-field appearance="outline">
                <mat-label>Select State</mat-label>
                <mat-select formControlName="state_id" required>
                    <mat-option *ngFor="let state of states" [value]="state.id">
                        {{state.state_name}}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="cityForm.get('state_id')?.hasError('required')">
                    State selection is required
                </mat-error>
            </mat-form-field>

            <!-- City Name -->
            <mat-form-field appearance="outline">
                <mat-label>City Name</mat-label>
                <input matInput formControlName="city_name" required>
                <mat-error *ngIf="cityForm.get('city_name')?.hasError('required')">
                    City name is required
                </mat-error>
            </mat-form-field>

            <!-- Active Status -->
            <div class="status-toggle">
                <mat-slide-toggle formControlName="is_active" color="primary">
                    Active
                </mat-slide-toggle>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" mat-stroked-button (click)="goBack()">
                    Cancel
                </button>
                <button type="submit" mat-raised-button color="primary" 
                        [disabled]="!cityForm.valid || isLoading">
                    Update
                </button>
            </div>
        </form>
    </mat-card>
</div>
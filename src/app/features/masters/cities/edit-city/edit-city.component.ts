import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import {
  CityService,
  City,
} from '../../../../core/auth/services/masters/city.service';
import {
  StateService,
  State,
} from '../../../../core/auth/services/masters/state.service';

@Component({
  selector: 'app-edit-city',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './edit-city.component.html',
  styleUrls: ['./edit-city.component.scss'],
})
export class EditCityComponent implements OnInit {
  cityForm: FormGroup = this.fb.group({
    state_id: ['', Validators.required],
    city_name: ['', Validators.required],
    is_active: [true],
  });

  states: State[] = [];
  isLoading = true;
  cityId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private cityService: CityService,
    private stateService: StateService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadStates();

    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.cityId = +params['id'];
        this.loadCity(this.cityId);
      } else {
        this.showError('City ID not found');
        this.goBack();
      }
    });
  }

  loadStates(): void {
    this.stateService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.states = response.data.filter((state) => state.is_active);
        }
      },
      error: (error) => {
        this.showError('Error loading states');
        console.error('Error loading states:', error);
      },
    });
  }

  private loadCity(id: number): void {
    this.isLoading = true;
    this.cityService.getById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.cityForm.patchValue({
            state_id: response.data.state_id,
            city_name: response.data.city_name,
            is_active: response.data.is_active,
          });
        } else {
          this.showError('City not found');
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.showError(error.error.message || 'Error loading city');
        this.goBack();
      },
    });
  }

  onSubmit(): void {
    if (this.cityForm.valid && this.cityId) {
      this.isLoading = true;
      this.cityService.update(this.cityId, this.cityForm.value).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('City updated successfully');
            this.goBack();
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.showError(error.error.message || 'Error updating city');
          this.isLoading = false;
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { EquipmentTypeService } from '../../../../core/auth/services/masters/equipment-type.service';

@Component({
  selector: 'app-edit-equipment-type',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './edit-equipment-type.component.html',
  styleUrls: ['./edit-equipment-type.component.scss'],
})
export class EditEquipmentTypeComponent implements OnInit {
  equipmentTypeForm: FormGroup = this.fb.group({
    type_name: ['', Validators.required],
    description: [''],
    is_active: [true],
  });

  isLoading = true;
  typeId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private equipmentTypeService: EquipmentTypeService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.typeId = +params['id'];
        this.loadEquipmentType(this.typeId);
      } else {
        this.showError('Equipment type ID not found');
        this.goBack();
      }
    });
  }

  private loadEquipmentType(id: number): void {
    this.isLoading = true;
    this.equipmentTypeService.getById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.equipmentTypeForm.patchValue({
            type_name: response.data.type_name,
            description: response.data.description,
            is_active: response.data.is_active,
          });
        } else {
          this.showError('Equipment type not found');
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.showError(error.error.message || 'Error loading equipment type');
        this.goBack();
      },
    });
  }

  onSubmit(): void {
    if (this.equipmentTypeForm.valid && this.typeId) {
      this.isLoading = true;
      this.equipmentTypeService
        .update(this.typeId, this.equipmentTypeForm.value)
        .subscribe({
          next: (response) => {
            if (response.status === 'success') {
              this.showSuccess('Equipment type updated successfully');
              this.goBack();
            }
            this.isLoading = false;
          },
          error: (error) => {
            this.showError(
              error.error.message || 'Error updating equipment type'
            );
            this.isLoading = false;
          },
        });
    }
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

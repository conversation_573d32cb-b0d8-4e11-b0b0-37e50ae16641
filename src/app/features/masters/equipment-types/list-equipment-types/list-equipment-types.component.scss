.content-container {
    padding: 24px;
}

.page-header {
    background: transparent;
    
    h1 {
        font-size: 24px;
        margin: 0;
    }
}

.spacer {
    flex: 1 1 auto;
}

.filters-container {
    padding: 16px;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;

    mat-form-field {
        flex: 1;
        min-width: 200px;
    }
}

.table-container {
    position: relative;
    min-height: 200px;
    overflow: auto;
    padding: 16px;
}

.loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.equipment-type-table {
    width: 100%;

    .mat-mdc-row:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }

    .mat-column-actions {
        width: 120px;
        text-align: center;
    }

    .mat-column-is_active {
        width: 100px;
        text-align: center;

        .active {
            color: #4caf50;
        }

        .inactive {
            color: #f44336;
        }
    }

    .mat-column-type_name {
        width: 25%;
    }

    .mat-column-description {
        width: 45%;
    }
}

.mat-mdc-row .mat-mdc-cell {
    border-bottom: 1px solid transparent;
    border-top: 1px solid transparent;
}

.mat-mdc-header-row {
    background-color: #f5f5f5;
}

.mat-mdc-header-cell {
    font-weight: bold;
    color: rgba(0, 0, 0, 0.87);
}

tr.mat-mdc-row:nth-child(even) {
    background-color: #fafafa;
}

.no-data {
    text-align: center;
    padding: 2rem;
    color: rgba(0, 0, 0, 0.54);
    font-style: italic;
}

:host ::ng-deep {
    .mat-mdc-card {
        border-radius: 8px;
    }

    .mat-mdc-table {
        border-radius: 4px;
        overflow: hidden;
    }

    .mat-mdc-row:last-child .mat-mdc-cell {
        border-bottom: none;
    }

    .mdc-data-table__row:last-child .mdc-data-table__cell {
        border-bottom: none;
    }

    .mat-mdc-paginator {
        border-top: 1px solid rgba(0, 0, 0, 0.12);
    }
}
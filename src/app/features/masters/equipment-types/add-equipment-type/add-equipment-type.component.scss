.content-container {
    padding: 24px;
    
    mat-card {
        .page-header {
            background: transparent;
            
            h1 {
                margin-left: 16px;
                font-size: 20px;
                font-weight: 500;
            }
        }
    }
}

.form-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    mat-form-field {
        width: 100%;

        textarea {
            min-height: 100px;
        }
    }

    .status-toggle {
        margin: 8px 0;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 24px;

        button {
            min-width: 100px;
        }
    }
}

:host ::ng-deep {
    .mat-mdc-card {
        border-radius: 8px;
    }

    .mat-mdc-form-field-subscript-wrapper {
        padding: 0 !important;
    }

    textarea {
        resize: vertical;
        min-height: 100px;
    }
}

@media screen and (max-width: 600px) {
    .content-container {
        padding: 16px;
    }

    .form-container {
        padding: 16px;
    }
}
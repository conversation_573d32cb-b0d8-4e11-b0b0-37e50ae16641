import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';

import { CropService } from '../../../../core/auth/services/masters/crop.service';

@Component({
  selector: 'app-add-crop',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
  ],
  templateUrl: './add-crop.component.html',
  styleUrls: ['./add-crop.component.scss'],
})
export class AddCropComponent implements OnInit {
  cropForm: FormGroup = this.fb.group({
    crop_name: ['', Validators.required],
    description: [''],
    is_active: [true],
  });

  selectedImage: File | null = null;
  imagePreview: string | null = null;
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private cropService: CropService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // No initialization needed
  }

  onImageSelected(event: Event): void {
    const element = event.target as HTMLInputElement;
    if (element.files && element.files.length) {
      this.selectedImage = element.files[0];

      // Create a preview
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreview = reader.result as string;
      };
      reader.readAsDataURL(this.selectedImage);
    }
  }

  removeImage(): void {
    this.selectedImage = null;
    this.imagePreview = null;
  }

  onSubmit(): void {
    if (this.cropForm.valid) {
      this.isSubmitting = true;

      // Create FormData object to send both form data and image
      const formData = new FormData();
      formData.append('crop_name', this.cropForm.get('crop_name')?.value);
      formData.append(
        'description',
        this.cropForm.get('description')?.value || ''
      );
      formData.append(
        'is_active',
        this.cropForm.get('is_active')?.value ? '1' : '0'
      );

      if (this.selectedImage) {
        formData.append('image', this.selectedImage);
      }

      this.cropService.createWithImage(formData).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          if (response.status === 'success') {
            this.showSuccess('Crop created successfully');
            this.router.navigate(['../'], { relativeTo: this.route });
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          this.showError(error.error?.message || 'Error creating crop');
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

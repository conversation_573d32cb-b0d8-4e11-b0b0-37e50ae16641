<div class="content-container">
    <mat-card>
        <mat-toolbar class="page-header">
            <h1>Crops</h1>
            <span class="spacer"></span>
            <button mat-raised-button color="primary" (click)="addCrop()">
                <mat-icon>add</mat-icon>
                Add Crop
            </button>
        </mat-toolbar>
        <mat-divider></mat-divider>

        <!-- Filters -->
        <div class="filters-container">
            <mat-form-field appearance="outline">
                <mat-label>Search Crops</mat-label>
                <input matInput (keyup)="applyFilter($event)" placeholder="Type to search...">
                <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
        </div>

        <div class="table-container">
            <div class="loading-container" *ngIf="isLoading">
                <mat-spinner diameter="40"></mat-spinner>
            </div>

            <table mat-table [dataSource]="dataSource" class="crops-table" *ngIf="!isLoading">
                <!-- Image Column -->
                <ng-container matColumnDef="image">
                    <th mat-header-cell *matHeaderCellDef>Image</th>
                    <td mat-cell *matCellDef="let element">
                        <div class="crop-image-container">
                            <img *ngIf="element.image_url" [src]="getFullImageUrl(element.image_url)" alt="{{ element.crop_name }}" class="crop-image">
                            <div *ngIf="!element.image_url" class="no-image">
                                <mat-icon>image</mat-icon>
                            </div>
                        </div>
                    </td>
                </ng-container>

                <!-- Crop Name Column -->
                <ng-container matColumnDef="crop_name">
                    <th mat-header-cell *matHeaderCellDef>Crop Name</th>
                    <td mat-cell *matCellDef="let element">{{element.crop_name}}</td>
                </ng-container>

                <!-- Description Column -->
                <ng-container matColumnDef="description">
                    <th mat-header-cell *matHeaderCellDef>Description</th>
                    <td mat-cell *matCellDef="let element">{{element.description}}</td>
                </ng-container>

                <!-- Status Column -->
                <ng-container matColumnDef="is_active">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let element">
                        <mat-icon [ngClass]="{'active': element.is_active, 'inactive': !element.is_active}">
                            {{element.is_active ? 'check_circle' : 'cancel'}}
                        </mat-icon>
                    </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let element">
                        <button mat-icon-button color="primary" 
                                (click)="editCrop(element.id)"
                                matTooltip="Edit Crop">
                            <mat-icon>edit</mat-icon>
                        </button>
                        <button mat-icon-button 
                                [color]="element.is_active ? 'warn' : 'primary'"
                                (click)="toggleStatus(element)"
                                [matTooltip]="element.is_active ? 'Deactivate' : 'Activate'">
                            <mat-icon>{{element.is_active ? 'block' : 'check_circle'}}</mat-icon>
                        </button>
                        <button mat-icon-button color="warn" 
                                (click)="deleteCrop(element.id)"
                                matTooltip="Delete Crop">
                            <mat-icon>delete</mat-icon>
                        </button>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                <!-- No Data Row -->
                <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" colspan="5">No crops found</td>
                </tr>
            </table>

            <mat-paginator [pageSizeOptions]="[10, 25, 50]"
                          showFirstLastButtons
                          aria-label="Select page of crops">
            </mat-paginator>
        </div>
    </mat-card>
</div>
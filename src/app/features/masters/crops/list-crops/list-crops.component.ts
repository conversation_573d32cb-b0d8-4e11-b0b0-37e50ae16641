import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { environment } from '../../../../../environments/environment';

import {
  CropService,
  Crop,
} from '../../../../core/auth/services/masters/crop.service';

@Component({
  selector: 'app-list-crops',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatToolbarModule,
    MatDividerModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  templateUrl: './list-crops.component.html',
  styleUrls: ['./list-crops.component.scss'],
})
export class ListCropsComponent implements OnInit {
  // Update displayedColumns to include the image column
  displayedColumns: string[] = [
    'image',
    'crop_name',
    'description',
    'is_active',
    'actions',
  ];
  dataSource: MatTableDataSource<Crop>;
  isLoading = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private cropService: CropService,
    private snackBar: MatSnackBar
  ) {
    this.dataSource = new MatTableDataSource<Crop>([]);
  }

  ngOnInit(): void {
    this.loadCrops();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.setupFilter();
  }

  private setupFilter(): void {
    this.dataSource.filterPredicate = (data: Crop, filter: string) => {
      const searchStr = (
        data.crop_name + (data.description || '')
      ).toLowerCase();
      return searchStr.indexOf(filter.toLowerCase()) !== -1;
    };
  }

  loadCrops(): void {
    this.isLoading = true;
    this.cropService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.dataSource.data = response.data;
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('Error loading crops');
        this.isLoading = false;
        console.error('Error loading crops:', error);
      },
    });
  }

  // Add method to get full image URL
  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    // Otherwise, prepend the API URL
    return `${environment.backendUrl}${imageUrl}`;
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addCrop(): void {
    this.router.navigate(['add'], { relativeTo: this.route });
  }

  editCrop(id: number): void {
    this.router.navigate(['edit', id], { relativeTo: this.route });
  }

  deleteCrop(id: number): void {
    if (confirm('Are you sure you want to delete this crop?')) {
      this.cropService.delete(id).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('Crop deleted successfully');
            this.loadCrops();
          }
        },
        error: (error) => {
          this.showError(error.error.message || 'Error deleting crop');
        },
      });
    }
  }

  toggleStatus(crop: Crop): void {
    if (!crop.id) return;

    this.cropService.toggleStatus(crop.id, !crop.is_active).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.showSuccess(
            `Crop ${crop.is_active ? 'deactivated' : 'activated'} successfully`
          );
          this.loadCrops();
        }
      },
      error: (error) => {
        this.showError(error.error.message || 'Error updating crop status');
      },
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['success-snackbar'],
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['error-snackbar'],
    });
  }
}

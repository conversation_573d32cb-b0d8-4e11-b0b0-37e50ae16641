.content-container {
    padding: 24px;
    
    mat-card {
        .page-header {
            background: transparent;
            
            h1 {
                margin-left: 16px;
                font-size: 20px;
                font-weight: 500;
            }
        }
    }
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 48px;
}

.form-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    mat-form-field {
        width: 100%;

        textarea {
            min-height: 100px;
        }
    }

    .status-toggle {
        margin: 8px 0;
    }

    .image-upload-section {
        margin-bottom: 16px;
        
        h3 {
            margin-top: 0;
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: 500;
        }
        
        .image-upload-container {
            border: 2px dashed #ccc;
            border-radius: 4px;
            padding: 16px;
            min-height: 120px;
            display: flex;
            justify-content: center;
            align-items: center;
            
            .image-preview {
                position: relative;
                max-width: 300px;
                
                img {
                    width: 100%;
                    max-height: 300px;
                    object-fit: contain;
                    border-radius: 4px;
                }
                
                .remove-image-btn {
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    background-color: rgba(255, 255, 255, 0.8);
                }
            }
            
            .upload-button-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                
                .hint-text {
                    margin-top: 8px;
                    color: rgba(0, 0, 0, 0.54);
                    font-size: 12px;
                }
            }
        }
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 24px;

        button {
            min-width: 100px;
        }
    }
}

:host ::ng-deep {
    .mat-mdc-card {
        border-radius: 8px;
    }

    .mat-mdc-form-field-subscript-wrapper {
        padding: 0 !important;
    }
}
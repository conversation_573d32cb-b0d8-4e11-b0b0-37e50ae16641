<div class="content-container">
    <mat-card>
        <mat-toolbar class="page-header">
            <button mat-icon-button (click)="goBack()">
                <mat-icon>arrow_back</mat-icon>
            </button>
            <h1>Edit Crop</h1>
        </mat-toolbar>
        <mat-divider></mat-divider>

        <div class="loading-container" *ngIf="isLoading">
            <mat-spinner diameter="40"></mat-spinner>
        </div>

        <form *ngIf="!isLoading" [formGroup]="cropForm" (ngSubmit)="onSubmit()" class="form-container">
            <!-- Crop Name -->
            <mat-form-field appearance="outline">
                <mat-label>Crop Name</mat-label>
                <input matInput formControlName="crop_name" required>
                <mat-error *ngIf="cropForm.get('crop_name')?.hasError('required')">
                    Crop name is required
                </mat-error>
            </mat-form-field>

            <!-- Description -->
            <mat-form-field appearance="outline">
                <mat-label>Description</mat-label>
                <textarea matInput formControlName="description" rows="3"></textarea>
            </mat-form-field>

            <!-- Image Upload -->
            <div class="image-upload-section">
                <h3>Crop Image</h3>
                
                <div class="image-upload-container">
                    <div class="image-preview" *ngIf="imagePreview">
                        <img [src]="imagePreview" alt="Crop image preview">
                        <button type="button" mat-icon-button color="warn" (click)="removeImage()" class="remove-image-btn">
                            <mat-icon>delete</mat-icon>
                        </button>
                    </div>
                    
                    <div class="upload-button-container" *ngIf="!imagePreview">
                        <button type="button" mat-raised-button (click)="fileInput.click()">
                            <mat-icon>upload</mat-icon>
                            Select Image
                        </button>
                        <input hidden type="file" #fileInput (change)="onImageSelected($event)" accept="image/*">
                        <p class="hint-text">Recommended size: 500 x 500 pixels</p>
                    </div>
                </div>
            </div>

            <!-- Active Status -->
            <div class="status-toggle">
                <mat-slide-toggle formControlName="is_active" color="primary">
                    Active
                </mat-slide-toggle>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" mat-stroked-button (click)="goBack()">
                    Cancel
                </button>
                <button type="submit" mat-raised-button color="primary" 
                        [disabled]="!cropForm.valid || isSubmitting">
                    Update
                </button>
            </div>
        </form>
    </mat-card>
</div>
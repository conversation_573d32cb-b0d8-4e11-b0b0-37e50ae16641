import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { environment } from '../../../../../environments/environment';

import { CropService } from '../../../../core/auth/services/masters/crop.service';

@Component({
  selector: 'app-edit-crop',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './edit-crop.component.html',
  styleUrls: ['./edit-crop.component.scss'],
})
export class EditCropComponent implements OnInit {
  cropForm: FormGroup = this.fb.group({
    crop_name: ['', Validators.required],
    description: [''],
    is_active: [true],
  });

  isLoading = true;
  isSubmitting = false;
  cropId: number | null = null;

  selectedImage: File | null = null;
  imagePreview: string | null = null;
  existingImageUrl: string | null = null;
  imageChanged = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private cropService: CropService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.cropId = +params['id'];
        this.loadCrop(this.cropId);
      } else {
        this.showError('Crop ID not found');
        this.goBack();
      }
    });
  }

  private loadCrop(id: number): void {
    this.isLoading = true;
    this.cropService.getById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.cropForm.patchValue({
            crop_name: response.data.crop_name,
            description: response.data.description,
            is_active: response.data.is_active,
          });

          // Handle image preview for existing image
          if (response.data.image_url) {
            this.existingImageUrl = response.data.image_url;
            this.imagePreview = this.getFullImageUrl(response.data.image_url);
          }
        } else {
          this.showError('Crop not found');
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.showError(error.error.message || 'Error loading crop');
        this.goBack();
      },
    });
  }

  onImageSelected(event: Event): void {
    const element = event.target as HTMLInputElement;
    if (element.files && element.files.length) {
      this.selectedImage = element.files[0];
      this.imageChanged = true;

      // Create a preview
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreview = reader.result as string;
      };
      reader.readAsDataURL(this.selectedImage);
    }
  }

  removeImage(): void {
    this.selectedImage = null;
    this.imagePreview = null;
    this.existingImageUrl = null;
    this.imageChanged = true;
  }

  getFullImageUrl(imageUrl: string | null): string {
    if (!imageUrl) return '';
    // If the URL already starts with http, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    // Otherwise, prepend the API URL
    return `${environment.backendUrl}${imageUrl}`;
  }

  onSubmit(): void {
    if (this.cropForm.valid && this.cropId) {
      this.isSubmitting = true;

      // Check if there's an image update
      if (this.imageChanged) {
        // Create FormData object to send both form data and image
        const formData = new FormData();
        formData.append('crop_name', this.cropForm.get('crop_name')?.value);
        formData.append(
          'description',
          this.cropForm.get('description')?.value || ''
        );
        formData.append(
          'is_active',
          this.cropForm.get('is_active')?.value ? '1' : '0'
        );

        if (this.selectedImage) {
          formData.append('image', this.selectedImage);
        }

        this.cropService.updateWithImage(this.cropId, formData).subscribe({
          next: (response) => {
            this.isSubmitting = false;
            if (response.status === 'success') {
              this.showSuccess('Crop updated successfully');
              this.goBack();
            }
          },
          error: (error) => {
            this.isSubmitting = false;
            this.showError(error.error?.message || 'Error updating crop');
          },
        });
      } else {
        // Regular update without image change
        this.cropService.update(this.cropId, this.cropForm.value).subscribe({
          next: (response) => {
            this.isSubmitting = false;
            if (response.status === 'success') {
              this.showSuccess('Crop updated successfully');
              this.goBack();
            }
          },
          error: (error) => {
            this.isSubmitting = false;
            this.showError(error.error?.message || 'Error updating crop');
          },
        });
      }
    }
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

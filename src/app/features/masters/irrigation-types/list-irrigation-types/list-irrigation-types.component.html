<div class="content-container">
    <mat-card>
        <mat-toolbar class="page-header">
            <h1>Irrigation Types</h1>
            <span class="spacer"></span>
            <button mat-raised-button color="primary" (click)="addIrrigationType()">
                <mat-icon>add</mat-icon>
                Add Irrigation Type
            </button>
        </mat-toolbar>
        <mat-divider></mat-divider>

        <!-- Search Filter -->
        <div class="filters-container">
            <mat-form-field appearance="outline">
                <mat-label>Search Irrigation Types</mat-label>
                <input matInput (keyup)="applyFilter($event)" placeholder="Type to search...">
                <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
        </div>

        <div class="table-container">
            <div class="loading-container" *ngIf="isLoading">
                <mat-spinner diameter="40"></mat-spinner>
            </div>

            <table mat-table [dataSource]="dataSource" class="irrigation-type-table" *ngIf="!isLoading">
                <!-- Type Name Column -->
                <ng-container matColumnDef="type_name">
                    <th mat-header-cell *matHeaderCellDef>Type Name</th>
                    <td mat-cell *matCellDef="let element">{{element.type_name}}</td>
                </ng-container>

                <!-- Description Column -->
                <ng-container matColumnDef="description">
                    <th mat-header-cell *matHeaderCellDef>Description</th>
                    <td mat-cell *matCellDef="let element">{{element.description}}</td>
                </ng-container>

                <!-- Status Column -->
                <ng-container matColumnDef="is_active">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let element">
                        <mat-icon [ngClass]="{'active': element.is_active, 'inactive': !element.is_active}">
                            {{element.is_active ? 'check_circle' : 'cancel'}}
                        </mat-icon>
                    </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let element">
                        <button mat-icon-button color="primary" 
                                (click)="editIrrigationType(element.id)"
                                matTooltip="Edit Irrigation Type">
                            <mat-icon>edit</mat-icon>
                        </button>
                        <button mat-icon-button 
                                [color]="element.is_active ? 'warn' : 'primary'"
                                (click)="toggleStatus(element)"
                                [matTooltip]="element.is_active ? 'Deactivate' : 'Activate'">
                            <mat-icon>{{element.is_active ? 'block' : 'check_circle'}}</mat-icon>
                        </button>
                        <button mat-icon-button color="warn" 
                                (click)="deleteIrrigationType(element.id)"
                                matTooltip="Delete Irrigation Type">
                            <mat-icon>delete</mat-icon>
                        </button>
                    </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

                <!-- No Data Row -->
                <tr class="mat-row" *matNoDataRow>
                    <td class="mat-cell" colspan="4">No irrigation types found</td>
                </tr>
            </table>

            <mat-paginator [pageSizeOptions]="[10, 25, 50]"
                          showFirstLastButtons
                          aria-label="Select page of irrigation types">
            </mat-paginator>
        </div>
    </mat-card>
</div>
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';

import {
  IrrigationTypeService,
  IrrigationType,
} from '../../../../core/auth/services/masters/irrigation-type.service';

@Component({
  selector: 'app-list-irrigation-types',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatToolbarModule,
    MatDividerModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  templateUrl: './list-irrigation-types.component.html',
  styleUrls: ['./list-irrigation-types.component.scss'],
})
export class ListIrrigationTypesComponent implements OnInit {
  displayedColumns: string[] = [
    'type_name',
    'description',
    'is_active',
    'actions',
  ];
  dataSource: MatTableDataSource<IrrigationType>;
  isLoading = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private irrigationTypeService: IrrigationTypeService,
    private snackBar: MatSnackBar
  ) {
    this.dataSource = new MatTableDataSource<IrrigationType>([]);
  }

  ngOnInit(): void {
    this.loadIrrigationTypes();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  loadIrrigationTypes(): void {
    this.isLoading = true;
    this.irrigationTypeService.getAll().subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.dataSource.data = response.data;
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('Error loading irrigation types');
        this.isLoading = false;
        console.error('Error loading irrigation types:', error);
      },
    });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  addIrrigationType(): void {
    this.router.navigate(['add'], { relativeTo: this.route });
  }

  editIrrigationType(id: number): void {
    this.router.navigate(['edit', id], { relativeTo: this.route });
  }

  deleteIrrigationType(id: number): void {
    if (confirm('Are you sure you want to delete this irrigation type?')) {
      this.irrigationTypeService.delete(id).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('Irrigation type deleted successfully');
            this.loadIrrigationTypes();
          }
        },
        error: (error) => {
          this.showError(
            error.error.message || 'Error deleting irrigation type'
          );
        },
      });
    }
  }

  toggleStatus(type: IrrigationType): void {
    if (!type.id) return;

    this.irrigationTypeService
      .toggleStatus(type.id, !type.is_active)
      .subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess(
              `Irrigation type ${
                type.is_active ? 'deactivated' : 'activated'
              } successfully`
            );
            this.loadIrrigationTypes();
          }
        },
        error: (error) => {
          this.showError(
            error.error.message || 'Error updating irrigation type status'
          );
        },
      });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['success-snackbar'],
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['error-snackbar'],
    });
  }
}

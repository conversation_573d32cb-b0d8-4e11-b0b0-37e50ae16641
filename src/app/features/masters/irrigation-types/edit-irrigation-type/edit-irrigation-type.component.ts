import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { IrrigationTypeService } from '../../../../core/auth/services/masters/irrigation-type.service';

@Component({
  selector: 'app-edit-irrigation-type',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './edit-irrigation-type.component.html',
  styleUrls: ['./edit-irrigation-type.component.scss'],
})
export class EditIrrigationTypeComponent implements OnInit {
  irrigationTypeForm: FormGroup = this.fb.group({
    type_name: ['', Validators.required],
    description: [''],
    is_active: [true],
  });

  isLoading = true;
  typeId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private irrigationTypeService: IrrigationTypeService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.typeId = +params['id'];
        this.loadIrrigationType(this.typeId);
      } else {
        this.showError('Irrigation type ID not found');
        this.goBack();
      }
    });
  }

  private loadIrrigationType(id: number): void {
    this.isLoading = true;
    this.irrigationTypeService.getById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.irrigationTypeForm.patchValue({
            type_name: response.data.type_name,
            description: response.data.description,
            is_active: response.data.is_active,
          });
        } else {
          this.showError('Irrigation type not found');
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.showError(error.error.message || 'Error loading irrigation type');
        this.goBack();
      },
    });
  }

  onSubmit(): void {
    if (this.irrigationTypeForm.valid && this.typeId) {
      this.isLoading = true;
      this.irrigationTypeService
        .update(this.typeId, this.irrigationTypeForm.value)
        .subscribe({
          next: (response) => {
            if (response.status === 'success') {
              this.showSuccess('Irrigation type updated successfully');
              this.goBack();
            }
            this.isLoading = false;
          },
          error: (error) => {
            this.showError(
              error.error.message || 'Error updating irrigation type'
            );
            this.isLoading = false;
          },
        });
    }
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { StateService } from '../../../../core/auth/services/masters/state.service';

@Component({
  selector: 'app-list-states',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
  ],
  templateUrl: './list-states.component.html',
  styleUrls: ['./list-states.component.scss'],
})
export class ListStatesComponent implements OnInit {
  states: any[] = [];
  displayedColumns: string[] = [
    'state_name',
    'state_code',
    'is_active',
    'actions',
  ];
  isLoading = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private stateService: StateService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadStates();
  }

  loadStates(): void {
    this.isLoading = true;
    this.stateService.getAll().subscribe({
      next: (response) => {
        this.states = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading states:', error);
        this.showError('Failed to load states');
        this.isLoading = false;
      },
    });
  }

  addState(): void {
    this.router.navigate(['add'], { relativeTo: this.route });
  }

  editState(stateId: number): void {
    this.router.navigate(['edit', stateId], { relativeTo: this.route });
  }

  deleteState(id: number): void {
    if (confirm('Are you sure you want to delete this state?')) {
      this.stateService.delete(id).subscribe({
        next: () => {
          this.showSuccess('State deleted successfully');
          this.loadStates();
        },
        error: (error) => {
          console.error('Error deleting state:', error);
          this.showError('Failed to delete state');
        },
      });
    }
  }

  toggleStatus(state: any): void {
    this.stateService.toggleStatus(state.id, !state.is_active).subscribe({
      next: () => {
        this.showSuccess('State status updated successfully');
        this.loadStates();
      },
      error: (error) => {
        console.error('Error updating state status:', error);
        this.showError('Failed to update state status');
      },
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['success-snackbar'],
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['error-snackbar'],
    });
  }
}

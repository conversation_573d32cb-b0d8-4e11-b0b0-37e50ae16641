<p>list-states works!</p>
<mat-card>
    <mat-card-header>
      <mat-card-title>States</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="addState()">
          <mat-icon>add</mat-icon> Add State
        </button>
      </div>
    </mat-card-header>
  
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="states" class="state-table">
          <!-- State Name Column -->
          <ng-container matColumnDef="state_name">
            <th mat-header-cell *matHeaderCellDef>State Name</th>
            <td mat-cell *matCellDef="let element">{{element.state_name}}</td>
          </ng-container>
  
          <!-- State Code Column -->
          <ng-container matColumnDef="state_code">
            <th mat-header-cell *matHeaderCellDef>State Code</th>
            <td mat-cell *matCellDef="let element">{{element.state_code}}</td>
          </ng-container>
  
          <!-- Status Column -->
          <ng-container matColumnDef="is_active">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let element">
              <mat-icon [ngClass]="{'active': element.is_active, 'inactive': !element.is_active}">
                {{element.is_active ? 'check_circle' : 'cancel'}}
              </mat-icon>
            </td>
          </ng-container>
  
          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button color="primary" 
                      (click)="editState(element.id)"
                      matTooltip="Edit State">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button 
                      [color]="element.is_active ? 'warn' : 'primary'"
                      (click)="toggleStatus(element)"
                      [matTooltip]="element.is_active ? 'Deactivate' : 'Activate'">
                <mat-icon>{{element.is_active ? 'block' : 'check_circle'}}</mat-icon>
              </button>
              <button mat-icon-button color="warn" 
                      (click)="deleteState(element.id)"
                      matTooltip="Delete State">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
  
        <!-- Loading Spinner -->
        <div class="loading-spinner" *ngIf="isLoading">
          <mat-spinner diameter="50"></mat-spinner>
        </div>
  
        <!-- No Data Message -->
        <div class="no-data" *ngIf="!isLoading && states.length === 0">
          No states found
        </div>
      </div>
    </mat-card-content>
  </mat-card>
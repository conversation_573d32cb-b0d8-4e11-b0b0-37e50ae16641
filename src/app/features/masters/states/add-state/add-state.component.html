<div class="content-container">
    <mat-card>
        <mat-toolbar class="page-header">
            <button mat-icon-button (click)="goBack()">
                <mat-icon>arrow_back</mat-icon>
            </button>
            <h1>Add State</h1>
        </mat-toolbar>
        <mat-divider></mat-divider>

        <form [formGroup]="stateForm" (ngSubmit)="onSubmit()" class="form-container">
            <!-- State Name -->
            <mat-form-field appearance="outline">
                <mat-label>State Name</mat-label>
                <input matInput formControlName="state_name" required>
                <mat-error *ngIf="stateForm.get('state_name')?.hasError('required')">
                    State name is required
                </mat-error>
            </mat-form-field>

            <!-- State Code -->
            <mat-form-field appearance="outline">
                <mat-label>State Code</mat-label>
                <input matInput formControlName="state_code" required maxlength="3">
                <mat-error *ngIf="stateForm.get('state_code')?.hasError('required')">
                    State code is required
                </mat-error>
                <mat-error *ngIf="stateForm.get('state_code')?.hasError('maxlength')">
                    State code cannot exceed 3 characters
                </mat-error>
            </mat-form-field>

            <!-- Active Status -->
            <div class="status-toggle">
                <mat-slide-toggle formControlName="is_active" color="primary">
                    Active
                </mat-slide-toggle>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" mat-stroked-button (click)="goBack()">
                    Cancel
                </button>
                <button type="submit" mat-raised-button color="primary" 
                        [disabled]="!stateForm.valid">
                    Save
                </button>
            </div>
        </form>
    </mat-card>
</div>
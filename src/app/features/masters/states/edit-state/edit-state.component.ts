import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { StateService } from '../../../../core/auth/services/masters/state.service';

@Component({
  selector: 'app-edit-state',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './edit-state.component.html',
  styleUrls: ['./edit-state.component.scss'],
})
export class EditStateComponent implements OnInit {
  stateForm: FormGroup = this.fb.group({
    state_name: ['', Validators.required],
    state_code: ['', [Validators.required, Validators.maxLength(3)]],
    is_active: [true],
  });

  isLoading = true;
  stateId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private stateService: StateService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.stateId = +params['id'];
        this.loadState(this.stateId);
      } else {
        this.showError('State ID not found');
        this.goBack();
      }
    });
  }

  private loadState(id: number): void {
    this.isLoading = true;
    this.stateService.getById(id).subscribe({
      next: (response) => {
        if (response.status === 'success' && response.data) {
          this.stateForm.patchValue({
            state_name: response.data.state_name,
            state_code: response.data.state_code,
            is_active: response.data.is_active,
          });
        } else {
          this.showError('State not found');
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.showError(error.error.message || 'Error loading state');
        this.goBack();
      },
    });
  }

  onSubmit(): void {
    if (this.stateForm.valid && this.stateId) {
      this.isLoading = true;
      this.stateService.update(this.stateId, this.stateForm.value).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('State updated successfully');
            this.goBack();
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.showError(error.error.message || 'Error updating state');
          this.isLoading = false;
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

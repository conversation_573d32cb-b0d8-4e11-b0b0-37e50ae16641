import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

import {
  LivestockTypeService,
  LivestockType,
  ApiResponse,
} from '../../../../core/auth/services/masters/livestock-type.service';

@Component({
  selector: 'app-list-livestocktypes',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatToolbarModule,
    MatDividerModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatSlideToggleModule,
  ],
  templateUrl: './list-livestocktypes.component.html',
  styleUrls: ['./list-livestocktypes.component.scss'],
})
export class ListLivestocktypesComponent implements OnInit {
  onDeleteType(arg0: any) {
    throw new Error('Method not implemented.');
  }
  displayedColumns: string[] = [
    'type_name',
    'description',
    'is_active',
    'actions',
  ];
  dataSource: MatTableDataSource<LivestockType>;
  isLoading = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private livestockTypeService: LivestockTypeService,
    private snackBar: MatSnackBar
  ) {
    this.dataSource = new MatTableDataSource<LivestockType>([]);
  }

  ngOnInit(): void {
    this.loadLivestockTypes();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  loadLivestockTypes(): void {
    this.isLoading = true;
    this.livestockTypeService.getAll().subscribe({
      next: (response: ApiResponse<LivestockType[]>) => {
        if (response.status === 'success') {
          this.dataSource.data = response.data;
        }
        this.isLoading = false;
      },
      error: (error: any) => {
        this.showError('Error loading livestock types');
        this.isLoading = false;
        console.error('Error loading livestock types:', error);
      },
    });
  }

  addLivestockType(): void {
    this.router.navigate(['add'], { relativeTo: this.route });
  }

  editLivestockType(id: number): void {
    this.router.navigate(['edit', id], { relativeTo: this.route });
  }

  deleteLivestockType(id: number): void {
    if (confirm('Are you sure you want to delete this livestock type?')) {
      this.livestockTypeService.delete(id).subscribe({
        next: (response: ApiResponse<void>) => {
          if (response.status === 'success') {
            this.showSuccess('Livestock type deleted successfully');
            this.loadLivestockTypes();
          }
        },
        error: (error: any) => {
          this.showError(
            error.error?.message || 'Error deleting livestock type'
          );
        },
      });
    }
  }

  toggleStatus(type: LivestockType): void {
    if (!type.id) return;

    this.livestockTypeService.toggleStatus(type.id, !type.is_active).subscribe({
      next: (response: ApiResponse<void>) => {
        if (response.status === 'success') {
          this.showSuccess(
            `Livestock type ${
              type.is_active ? 'deactivated' : 'activated'
            } successfully`
          );
          this.loadLivestockTypes();
        }
      },
      error: (error: any) => {
        this.showError(
          error.error?.message || 'Error updating livestock type status'
        );
      },
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

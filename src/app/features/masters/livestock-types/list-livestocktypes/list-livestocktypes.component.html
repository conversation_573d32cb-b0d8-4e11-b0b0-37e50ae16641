<div class="p-4">
    <!-- Header with Add button -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Livestock Types</h2>
        <button mat-raised-button color="primary" routerLink="add">
            <mat-icon class="mr-2">add</mat-icon>
            Add Livestock Type
        </button>
    </div>

    <!-- Data Table -->
    <div class="mat-elevation-z8">
        <table mat-table [dataSource]="dataSource" class="w-full">
            <!-- ID Column -->
            <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef> ID </th>
                <td mat-cell *matCellDef="let element"> {{element.id}} </td>
            </ng-container>

            <!-- Type Name Column -->
            <ng-container matColumnDef="type_name">
                <th mat-header-cell *matHeaderCellDef> Type Name </th>
                <td mat-cell *matCellDef="let element"> {{element.type_name}} </td>
            </ng-container>

            <!-- Description Column -->
            <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef> Description </th>
                <td mat-cell *matCellDef="let element"> {{element.description}} </td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="is_active">
                <th mat-header-cell *matHeaderCellDef> Status </th>
                <td mat-cell *matCellDef="let element">
                    <mat-slide-toggle
                    [checked]="element.is_active"
                    (change)="toggleStatus(element)">  <!-- Changed from onToggleStatus -->
                </mat-slide-toggle>
                </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef> Actions </th>
                <td mat-cell *matCellDef="let element">
                    <button mat-icon-button color="primary" [routerLink]="['edit', element.id]" matTooltip="Edit">
                        <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="onDeleteType(element.id)" matTooltip="Delete">
                        <mat-icon>delete</mat-icon>
                    </button>
                </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
    </div>
</div>
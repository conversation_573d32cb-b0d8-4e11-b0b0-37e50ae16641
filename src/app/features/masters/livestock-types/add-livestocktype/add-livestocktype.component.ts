import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';

import {
  LivestockTypeService,
  LivestockType,
  ApiResponse,
} from '../../../../core/auth/services/masters/livestock-type.service';

@Component({
  selector: 'app-add-livestocktype',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
  ],
  templateUrl: './add-livestocktype.component.html',
  styleUrls: ['./add-livestocktype.component.scss'],
})
export class AddLivestocktypeComponent implements OnInit {
  livestockTypeForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private livestockTypeService: LivestockTypeService,
    private snackBar: MatSnackBar
  ) {
    this.livestockTypeForm = this.fb.group({
      type_name: ['', Validators.required],
      description: [''],
      is_active: [true],
    });
  }

  ngOnInit(): void {}

  onSubmit(): void {
    if (this.livestockTypeForm.valid) {
      this.livestockTypeService.create(this.livestockTypeForm.value).subscribe({
        next: (response: ApiResponse<LivestockType>) => {
          if (response.status === 'success') {
            this.showSuccess('Livestock type created successfully');
            this.router.navigate(['../'], { relativeTo: this.route });
          }
        },
        error: (error: any) => {
          this.showError(
            error.error?.message || 'Error creating livestock type'
          );
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

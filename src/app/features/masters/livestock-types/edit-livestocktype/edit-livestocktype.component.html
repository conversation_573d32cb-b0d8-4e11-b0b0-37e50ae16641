<div class="content-container">
    <mat-card>
        <mat-toolbar class="page-header">
            <button mat-icon-button (click)="goBack()">
                <mat-icon>arrow_back</mat-icon>
            </button>
            <h1>Edit Livestock Type</h1>
        </mat-toolbar>
        <mat-divider></mat-divider>

        <div class="loading-container" *ngIf="isLoading">
            <mat-spinner diameter="40"></mat-spinner>
        </div>

        <form *ngIf="!isLoading" [formGroup]="livestockTypeForm" (ngSubmit)="onSubmit()" class="form-container">
            <!-- Type Name -->
            <mat-form-field appearance="outline">
                <mat-label>Type Name</mat-label>
                <input matInput formControlName="type_name" required>
                <mat-error *ngIf="livestockTypeForm.get('type_name')?.hasError('required')">
                    Type name is required
                </mat-error>
            </mat-form-field>

            <!-- Description -->
            <mat-form-field appearance="outline">
                <mat-label>Description</mat-label>
                <textarea matInput formControlName="description" rows="4"></textarea>
            </mat-form-field>

            <!-- Active Status -->
            <div class="status-toggle">
                <mat-slide-toggle formControlName="is_active" color="primary">
                    Active
                </mat-slide-toggle>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" mat-stroked-button (click)="goBack()">
                    Cancel
                </button>
                <button type="submit" mat-raised-button color="primary" 
                        [disabled]="!livestockTypeForm.valid || isLoading">
                    Update
                </button>
            </div>
        </form>
    </mat-card>
</div>
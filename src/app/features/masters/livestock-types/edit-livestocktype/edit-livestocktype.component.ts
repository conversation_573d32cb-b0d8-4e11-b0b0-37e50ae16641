import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';

import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import {
  LivestockTypeService,
  LivestockType,
  ApiResponse,
} from '../../../../core/auth/services/masters/livestock-type.service';

@Component({
  selector: 'app-edit-livestocktype',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './edit-livestocktype.component.html',
  styleUrls: ['./edit-livestocktype.component.scss'],
})
export class EditLivestocktypeComponent implements OnInit {
  livestockTypeForm: FormGroup;
  typeId: number | null = null;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private livestockTypeService: LivestockTypeService,
    private snackBar: MatSnackBar
  ) {
    this.livestockTypeForm = this.fb.group({
      type_name: ['', Validators.required],
      description: [''],
      is_active: [true],
    });
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.typeId = +params['id'];
        this.loadLivestockType(this.typeId);
      } else {
        this.showError('Livestock type ID not found');
        this.goBack();
      }
    });
  }

  private loadLivestockType(id: number): void {
    this.isLoading = true;
    this.livestockTypeService.getById(id).subscribe({
      next: (response: ApiResponse<LivestockType>) => {
        if (response.status === 'success' && response.data) {
          this.livestockTypeForm.patchValue({
            type_name: response.data.type_name,
            description: response.data.description,
            is_active: response.data.is_active,
          });
        } else {
          this.showError('Livestock type not found');
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error: any) => {
        this.showError(error.error?.message || 'Error loading livestock type');
        this.goBack();
      },
    });
  }

  onSubmit(): void {
    if (this.livestockTypeForm.valid && this.typeId) {
      this.isLoading = true;
      this.livestockTypeService
        .update(this.typeId, this.livestockTypeForm.value)
        .subscribe({
          next: (response: ApiResponse<LivestockType>) => {
            if (response.status === 'success') {
              this.showSuccess('Livestock type updated successfully');
              this.goBack();
            }
            this.isLoading = false;
          },
          error: (error: any) => {
            this.showError(
              error.error?.message || 'Error updating livestock type'
            );
            this.isLoading = false;
          },
        });
    }
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

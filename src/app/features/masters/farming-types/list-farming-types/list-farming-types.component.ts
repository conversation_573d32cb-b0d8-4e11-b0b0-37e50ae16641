import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

import { FarmingTypeService } from '../../../../core/auth/services/masters/farming-type.service';

@Component({
  selector: 'app-list-farming-types',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatToolbarModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
  ],
  templateUrl: './list-farming-types.component.html',
  styleUrls: ['./list-farming-types.component.scss'],
})
export class ListFarmingTypesComponent implements OnInit {
  farmingTypes: any[] = [];
  displayedColumns: string[] = [
    'type_name',
    'description',
    'is_active',
    'actions',
  ];
  isLoading = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private farmingTypeService: FarmingTypeService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadFarmingTypes();
  }

  loadFarmingTypes(): void {
    this.isLoading = true;
    this.farmingTypeService.getAll().subscribe({
      next: (response) => {
        this.farmingTypes = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading farming types:', error);
        this.showError('Failed to load farming types');
        this.isLoading = false;
      },
    });
  }

  addFarmingType(): void {
    this.router.navigate(['add'], { relativeTo: this.route });
  }

  editFarmingType(typeId: number): void {
    this.router.navigate(['edit', typeId], { relativeTo: this.route });
  }

  deleteFarmingType(id: number): void {
    if (confirm('Are you sure you want to delete this farming type?')) {
      this.farmingTypeService.delete(id).subscribe({
        next: () => {
          this.showSuccess('Farming type deleted successfully');
          this.loadFarmingTypes();
        },
        error: (error) => {
          console.error('Error deleting farming type:', error);
          this.showError('Failed to delete farming type');
        },
      });
    }
  }

  toggleStatus(type: any): void {
    this.farmingTypeService.toggleStatus(type.id, !type.is_active).subscribe({
      next: () => {
        this.showSuccess('Farming type status updated successfully');
        this.loadFarmingTypes();
      },
      error: (error) => {
        console.error('Error updating farming type status:', error);
        this.showError('Failed to update farming type status');
      },
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['success-snackbar'],
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: ['error-snackbar'],
    });
  }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDividerModule } from '@angular/material/divider';

import { FarmingTypeService } from '../../../../core/auth/services/masters/farming-type.service';

@Component({
  selector: 'app-add-farming-type',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatToolbarModule,
    MatDividerModule,
  ],
  templateUrl: './add-farming-type.component.html',
  styleUrls: ['./add-farming-type.component.scss'],
})
export class AddFarmingTypeComponent implements OnInit {
  farmingTypeForm: FormGroup = this.fb.group({
    type_name: ['', Validators.required],
    description: [''],
    is_active: [true],
  });

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private farmingTypeService: FarmingTypeService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {}

  onSubmit(): void {
    if (this.farmingTypeForm.valid) {
      this.farmingTypeService.create(this.farmingTypeForm.value).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('Farming type created successfully');
            this.router.navigate(['../'], { relativeTo: this.route });
          }
        },
        error: (error) => {
          this.showError(error.error.message || 'Error creating farming type');
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

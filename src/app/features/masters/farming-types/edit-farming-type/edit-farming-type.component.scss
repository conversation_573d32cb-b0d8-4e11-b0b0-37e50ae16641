.content-container {
    padding: 24px;
    
    mat-card {
        .page-header {
            background: transparent;
            
            h1 {
                margin-left: 16px;
                font-size: 20px;
                font-weight: 500;
            }
        }
    }
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 48px;
}

.form-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    mat-form-field {
        width: 100%;
    }

    .status-toggle {
        margin: 8px 0;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 24px;

        button {
            min-width: 100px;
        }
    }
}
<!-- add-article.component.html -->
<div class="article-container">
    <mat-card>
      <mat-toolbar class="page-header">
        <button mat-icon-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <h1>Add New Article</h1>
      </mat-toolbar>
      <mat-divider></mat-divider>
  
      <form [formGroup]="articleForm" (ngSubmit)="onSubmit()" class="article-form">
        <div class="loading-overlay" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
  
        <!-- Title Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Title</mat-label>
          <input matInput formControlName="title" placeholder="Enter article title" required>
          <mat-error *ngIf="articleForm.get('title')?.hasError('required')">
            Title is required
          </mat-error>
          <mat-error *ngIf="articleForm.get('title')?.hasError('maxlength')">
            Title cannot exceed 200 characters
          </mat-error>
        </mat-form-field>
  
        <!-- Description Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Description</mat-label>
          <textarea matInput formControlName="description" rows="10" placeholder="Enter article content" required></textarea>
          <mat-error *ngIf="articleForm.get('description')?.hasError('required')">
            Description is required
          </mat-error>
        </mat-form-field>
  
        <!-- Image Upload -->
        <div class="image-upload-container">
          <div class="upload-label">Image (Optional)</div>
          
          <div class="image-preview-container" *ngIf="imagePreview">
            <img [src]="imagePreview" alt="Preview" class="image-preview">
            <button type="button" mat-icon-button color="warn" (click)="removeImage()" class="remove-image-btn">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
          
          <button type="button" mat-stroked-button (click)="fileInput.click()" *ngIf="!imagePreview">
            <mat-icon>cloud_upload</mat-icon>
            Select Image
          </button>
          <input type="file" #fileInput hidden (change)="onFileSelected($event)" accept="image/*">
        </div>
  
        <!-- Form Actions -->
        <div class="form-actions">
          <button type="button" mat-stroked-button (click)="goBack()">
            Cancel
          </button>
          <button type="submit" mat-raised-button color="primary" [disabled]="isLoading">
            Save Article
          </button>
        </div>
      </form>
    </mat-card>
  </div>
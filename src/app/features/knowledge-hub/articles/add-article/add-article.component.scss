// add-article.component.scss
.article-container {
    padding: 24px;
    max-width: 1000px;
    margin: 0 auto;
  
    .page-header {
      background: transparent;
      h1 {
        margin-left: 16px;
        font-size: 20px;
        font-weight: 500;
      }
    }
  
    .article-form {
      padding: 24px;
      position: relative;
  
      .full-width {
        width: 100%;
        margin-bottom: 20px;
      }
  
      .image-upload-container {
        margin-bottom: 24px;
  
        .upload-label {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
          margin-bottom: 8px;
        }
  
        .image-preview-container {
          position: relative;
          width: 100%;
          max-width: 300px;
          margin-bottom: 16px;
  
          .image-preview {
            width: 100%;
            height: auto;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
          }
  
          .remove-image-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background-color: rgba(255, 255, 255, 0.8);
          }
        }
      }
  
      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 16px;
      }
  
      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
      }
    }
  }
  
  @media (max-width: 768px) {
    .article-container {
      padding: 16px;
  
      .article-form {
        padding: 16px;
      }
    }
  }
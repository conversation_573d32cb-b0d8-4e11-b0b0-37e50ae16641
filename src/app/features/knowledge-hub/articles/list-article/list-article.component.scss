// src/app/features/knowledge-hub/list-article/list-article.component.scss
.article-container {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
  
    .header-section {
      margin-bottom: 32px;
      text-align: center;
  
      .page-title {
        font-size: 2.5rem;
        font-weight: 500;
        margin-bottom: 8px;
        color: #2c3e50;
      }
  
      .page-subtitle {
        font-size: 1.2rem;
        color: #7f8c8d;
        margin-bottom: 24px;
      }
  
      .action-buttons {
        display: flex;
        justify-content: center;
        margin-top: 16px;
  
        button, a {
          min-width: 200px;
        }
      }
    }
  
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  
    .articles-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 24px;
  
      .article-card {
        display: flex;
        flex-direction: column;
        height: 100%;
        border-radius: 8px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
  
        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
  
        .article-image-container {
          height: 180px;
          overflow: hidden;
  
          .article-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
  
            &:hover {
              transform: scale(1.05);
            }
          }
        }
  
        .card-content-wrapper {
          display: flex;
          flex-direction: column;
          flex: 1;
          padding: 16px;
  
          mat-card-header {
            padding: 0;
            margin-bottom: 12px;
          }
  
          mat-card-content {
            flex: 1;
          }
  
          mat-card-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
  
            .article-stats {
              display: flex;
              align-items: center;
              gap: 12px;
              color: #7f8c8d;
              font-size: 0.875rem;
  
              .view-count, .like-count {
                display: flex;
                align-items: center;
  
                mat-icon {
                  font-size: 18px;
                  height: 18px;
                  width: 18px;
                  margin-right: 4px;
                }
              }
  
              .like-count {
                mat-icon {
                  color: #e74c3c;
                }
              }
            }
  
            .admin-actions {
              display: flex;
              gap: 8px;
            }
          }
        }
      }
      .mat-mdc-card-subtitle{
        font-weight: 200;
      }
  
      .no-articles {
        grid-column: 1 / -1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 48px;
        background-color: #f8f9fa;
        border-radius: 8px;
        text-align: center;
  
        mat-icon {
          font-size: 48px;
          height: 48px;
          width: 48px;
          margin-bottom: 16px;
          color: #7f8c8d;
        }
  
        p {
          margin-bottom: 24px;
          color: #7f8c8d;
          font-size: 1.1rem;
        }
      }
    }
  }
  
  // Responsive adjustments
  @media (max-width: 768px) {
    .article-container {
      padding: 16px;
  
      .header-section {
        .page-title {
          font-size: 2rem;
        }
  
        .page-subtitle {
          font-size: 1rem;
        }
      }
  
      .articles-grid {
        grid-template-columns: 1fr;
      }
    }
  }
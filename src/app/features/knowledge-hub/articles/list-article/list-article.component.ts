// src/app/features/knowledge-hub/list-article/list-article.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../../environments/environment';

import {
  ArticleService,
  Article,
} from '../../../../core/auth/services/article.service';
import { AuthService } from '../../../../core/auth/services/auth.service';

@Component({
  selector: 'app-list-article',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
  ],
  templateUrl: './list-article.component.html',
  styleUrls: ['./list-article.component.scss'],
})
export class ListArticleComponent implements OnInit {
  articles: Article[] = [];
  isLoading = true;
  isAdmin = false;
  backendUrl = environment.backendUrl;

  constructor(
    private articleService: ArticleService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.isAdmin =
      this.authService.hasRole('admin') ||
      this.authService.hasRole('superadmin');
    this.loadArticles();
  }

  loadArticles(): void {
    this.isLoading = true;
    this.articleService.getAllArticles().subscribe({
      next: (response) => {
        console.log('API Response:', response);
        if (response.status === 'success') {
          this.articles = response.data;
          console.log('Articles loaded:', this.articles);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading articles:', error);
        this.showError('Failed to load knowledge articles');
        this.isLoading = false;
      },
    });
  }

  viewArticle(article: any): void {
    if (article && article.id) {
      this.router.navigate(['view-article', article.id], {
        relativeTo: this.route,
      });
    } else {
      // Handle the case where article or article.id is undefined
      console.warn('Cannot navigate: Article ID is missing');
      // You could show a message to the user or navigate to a different page
    }
  }

  createArticle() {
    this.router.navigate(['add-article'], {
      relativeTo: this.route,
    });
  }

  deleteArticle(id: number): void {
    if (confirm('Are you sure you want to delete this article?')) {
      this.articleService.deleteArticle(id).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('Article deleted successfully');
            this.loadArticles();
          }
        },
        error: (error) => {
          console.error('Error deleting article:', error);
          this.showError('Failed to delete article');
        },
      });
    }
  }

  formatDate(date: string | Date | undefined): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  truncateDescription(text: string, maxLength: number = 150): string {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

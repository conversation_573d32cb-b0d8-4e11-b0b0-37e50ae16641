<!-- src/app/features/knowledge-hub/list-article/list-article.component.html -->
<div class="article-container">
    <div class="header-section">
      <h1 class="page-title">Knowledge Hub</h1>
      <p class="page-subtitle">Explore agricultural tips, techniques, and best practices</p>
      
      <div class="action-buttons" *ngIf="isAdmin">
        <a mat-raised-button color="primary" (click)="createArticle()">
          <mat-icon>add</mat-icon>
          Create New Article
        </a>
      </div>
    </div>
  
    <!-- Loading Spinner -->
    <div class="loading-container" *ngIf="isLoading">
      <mat-spinner diameter="40"></mat-spinner>
    </div>
  
    <!-- Articles Grid -->
    <div class="articles-grid" *ngIf="!isLoading">
      <mat-card class="article-card" *ngFor="let article of articles">
        <div class="article-image-container" *ngIf="article.image_url">
          <img [src]="backendUrl+article.image_url" alt="{{ article.title }}" class="article-image">
        </div>
        
        <div class="card-content-wrapper">
          <mat-card-header>
            <mat-card-title>{{ article.title }}</mat-card-title>
            <mat-card-subtitle>
              Published {{ formatDate(article.created_at) }} by {{ article.created_by_name }}
            </mat-card-subtitle>
          </mat-card-header>
          
          <mat-card-content>
            <p>{{ truncateDescription(article.description) }}</p>
          </mat-card-content>
          
          <mat-card-actions>
            <a mat-button color="primary" (click)="viewArticle(article)">
                READ MORE
              </a>
            
          
            
            <!-- Admin actions -->
            <div class="admin-actions" *ngIf="isAdmin">
              <a mat-icon-button color="primary" [routerLink]="['../edit-article', article.id]" matTooltip="Edit Article">
                <mat-icon>edit</mat-icon>
              </a>
              <button mat-icon-button color="warn" (click)="deleteArticle(article.id!)" matTooltip="Delete Article">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </mat-card-actions>
        </div>
      </mat-card>
      
      <!-- No articles message -->
      <div class="no-articles" *ngIf="articles.length === 0">
        <mat-icon>article</mat-icon>
        <p>No articles found. Check back later for valuable farming knowledge.</p>
        <a mat-raised-button color="primary" routerLink="../add-article" *ngIf="isAdmin">
          Create Your First Article
        </a>
      </div>
    </div>
  </div>
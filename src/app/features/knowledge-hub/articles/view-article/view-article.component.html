<!-- src/app/features/knowledge-hub/view-article/view-article.component.html -->
<div class="article-card-container">
    <!-- Loading Spinner -->
    <div class="loading-container" *ngIf="isLoading">
      <mat-spinner diameter="40"></mat-spinner>
    </div>
  
    <div class="article-card" *ngIf="!isLoading && article">
      <!-- Featured Image -->
      <div class="featured-image-container" *ngIf="article.image_url">
        <img [src]="backendUrl + article.image_url" alt="{{ article.title }}" class="article-image">
      </div>
  
      <!-- Article Content -->
      <div class="card-content">
        <!-- Article Header -->
        <h2 class="article-title">{{ article.title }}</h2>
        
        <!-- Publication Info -->
        <div class="article-meta">
          Published {{ formatDate(article.created_at) }} by {{ article.created_by_name }}
        </div>
        
        <!-- Article Body -->
        <div class="article-body">
          <p *ngFor="let paragraph of article.description.split('\n\n')">{{ paragraph }}</p>
        </div>
        
        <!-- Action Buttons -->
        <div class="article-actions">
          <button mat-stroked-button color="primary" (click)="goBack()">
            <mat-icon>arrow_back</mat-icon> BACK
          </button>
          
          <div class="admin-actions" *ngIf="isAdmin">
            <button mat-icon-button color="primary" (click)="editArticle()" matTooltip="Edit Article">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deleteArticle()" matTooltip="Delete Article">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  
    <!-- Not Found Message -->
    <div class="not-found" *ngIf="!isLoading && !article">
      <mat-icon>error_outline</mat-icon>
      <h2>Article Not Found</h2>
      <p>The article you're looking for doesn't exist or has been removed.</p>
      <button mat-raised-button color="primary" (click)="goBack()">Back to Articles</button>
    </div>
  </div>
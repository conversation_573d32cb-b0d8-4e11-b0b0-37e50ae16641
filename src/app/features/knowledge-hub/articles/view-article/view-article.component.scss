// view-article.component.scss
.article-card-container {
    padding: 24px;
    max-width: 800px;
    margin: 0 auto;
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  .article-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .article-image {
    width: 100%;
    height: 240px;
    object-fit: cover;
  }
  
  .card-content {
    padding: 16px;
  }
  
  .article-title {
    font-size: 24px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: rgba(0, 0, 0, 0.87);
  }
  
  .article-meta {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 16px;
  }
  
  .article-body {
    line-height: 1.6;
    color: rgba(0, 0, 0, 0.87);
    margin-bottom: 24px;
    
    p {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .article-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .admin-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .not-found {
    text-align: center;
    padding: 40px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    
    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      color: #f44336;
      margin-bottom: 16px;
    }
    
    h2 {
      margin-bottom: 16px;
    }
    
    p {
      margin-bottom: 24px;
      color: rgba(0, 0, 0, 0.6);
    }
  }
  
  @media (max-width: 600px) {
    .article-card-container {
      padding: 16px;
    }
    
    .article-title {
      font-size: 20px;
    }
  }
// src/app/features/knowledge-hub/view-article/view-article.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { environment } from '../../../../../environments/environment';

import {
  ArticleService,
  Article,
} from '../../../../core/auth/services/article.service';
import { AuthService } from '../../../../core/auth/services/auth.service';

@Component({
  selector: 'app-view-article',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatChipsModule,
  ],
  templateUrl: './view-article.component.html',
  styleUrls: ['./view-article.component.scss'],
})
export class ViewArticleComponent implements OnInit {
  article: Article | null = null;
  isLoading = true;
  isAdmin = false;
  backendUrl = environment.backendUrl;
  articleId: number | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private articleService: ArticleService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.isAdmin =
      this.authService.hasRole('admin') ||
      this.authService.hasRole('superadmin');

    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.articleId = +params['id'];
        this.loadArticle(this.articleId);
      } else {
        this.showError('Article ID not found');
        this.goBack();
      }
    });
  }

  loadArticle(id: number): void {
    this.isLoading = true;
    this.articleService.getArticleById(id).subscribe({
      next: (response) => {
        if (response.status === 'success') {
          this.article = response.data;

          // Optional: Record view for analytics
          this.recordView(id);
        } else {
          this.showError('Article not found');
          this.goBack();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading article:', error);
        this.showError('Failed to load article');
        this.isLoading = false;
        this.goBack();
      },
    });
  }

  recordView(id: number): void {
    // Only implement if backend supports this feature
    // this.articleService.recordView(id).subscribe();
  }

  toggleLike(): void {
    // Only implement if backend supports this feature
    // if (this.articleId) {
    //   this.articleService.toggleLike(this.articleId).subscribe({
    //     next: (response) => {
    //       if (response.status === 'success') {
    //         // Refresh article to get updated like count
    //         this.loadArticle(this.articleId!);
    //       }
    //     },
    //     error: (error) => {
    //       console.error('Error toggling like:', error);
    //     },
    //   });
    // }
  }

  deleteArticle(): void {
    if (
      this.articleId &&
      confirm('Are you sure you want to delete this article?')
    ) {
      this.articleService.deleteArticle(this.articleId).subscribe({
        next: (response) => {
          if (response.status === 'success') {
            this.showSuccess('Article deleted successfully');
            this.router.navigate(['../../'], { relativeTo: this.route });
          }
        },
        error: (error) => {
          console.error('Error deleting article:', error);
          this.showError('Failed to delete article');
        },
      });
    }
  }

  formatDate(date: string | Date | undefined): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  editArticle(): void {
    if (this.articleId) {
      this.router.navigate(['../../edit-article', this.articleId], {
        relativeTo: this.route,
      });
    }
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}

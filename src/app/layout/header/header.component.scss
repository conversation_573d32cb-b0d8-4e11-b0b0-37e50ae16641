// src/app/layout/header/header.component.scss
.header-toolbar {
    background: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    position: sticky;
    top: 0;
    z-index: 1000;
  }
  
  .toolbar-spacer {
    flex: 1 1 auto;
  }
  
  .user-profile-btn {
    mat-icon {
      margin-right: 8px;
      
      &:last-child {
        margin-right: 0;
        margin-left: 8px;
      }
    }
  }
  
  .user-role {
    color: rgba(0, 0, 0, 0.54);
    font-size: 14px;
    
    mat-icon {
      margin-right: 8px;
    }
  }
<!-- src/app/layout/header/header.component.html -->
<mat-toolbar class="header-toolbar">
    <button mat-icon-button (click)="menuToggled.emit()">
      <mat-icon>menu</mat-icon>
    </button>
  
    <span class="toolbar-spacer"></span>
  
    <button mat-button [matMenuTriggerFor]="userMenu" class="user-profile-btn">
      <mat-icon>account_circle</mat-icon>
      <span>{{ currentUser?.full_name }}</span>
      <mat-icon>arrow_drop_down</mat-icon>
    </button>
  
    <mat-menu #userMenu="matMenu">
      <span mat-menu-item disabled class="user-role">
        <mat-icon>badge</mat-icon>
        {{ userRole }}
      </span>
      <button mat-menu-item (click)="onLogout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </mat-toolbar>
// src/app/layout/header/header.component.ts
import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { AuthService } from '../../core/auth/services/auth.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
  ],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent {
  @Output() menuToggled = new EventEmitter<void>();

  currentUser: any;
  userRole: string = '';

  constructor(private authService: AuthService) {
    this.currentUser = this.authService.getCurrentUser();
    const roles = this.authService.getCurrentUserRoles();
    this.userRole = roles.length > 0 ? roles[0].toUpperCase() : 'USER';
  }

  onLogout(): void {
    this.authService.logout();
  }
}

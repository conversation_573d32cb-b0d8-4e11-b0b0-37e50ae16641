<!-- src/app/layout/sidenav/sidenav.component.html -->
<div class="sidenav-container">
    <div class="logo-container">
      <!-- <img src="assets/logo.png" alt="FPO Logo" class="logo"> -->
      <span class="app-name">FPO Traders</span>
    </div>
  
    <mat-nav-list>
      <ng-container *ngFor="let item of menuItems">
        <!-- Only show items user has access to -->
        <ng-container *ngIf="canShowMenuItem(item)">
          <!-- Items without children -->
          <a mat-list-item *ngIf="!item.children" [routerLink]="item.route" routerLinkActive="active">
            <mat-icon matListItemIcon>{{item.icon}}</mat-icon>
            <span matListItemTitle>{{item.label}}</span>
          </a>
  
          <!-- Items with children -->
          <mat-expansion-panel *ngIf="item.children" class="mat-elevation-z0">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <mat-icon>{{item.icon}}</mat-icon>
                <span>{{item.label}}</span>
              </mat-panel-title>
            </mat-expansion-panel-header>
  
            <a mat-list-item *ngFor="let child of item.children" 
               [routerLink]="child.route" 
               routerLinkActive="active">
              <mat-icon matListItemIcon>{{child.icon}}</mat-icon>
              <span matListItemTitle>{{child.label}}</span>
            </a>
          </mat-expansion-panel>
        </ng-container>
      </ng-container>
    </mat-nav-list>
  </div>
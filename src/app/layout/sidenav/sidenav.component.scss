// src/app/layout/sidenav/sidenav.component.scss
.sidenav-container {
    height: 100%;
    background-color: #fff;
    
    .logo-container {
      padding: 16px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
      
      .logo {
        width: 32px;
        height: 32px;
        margin-right: 8px;
      }
      
      .app-name {
        font-size: 20px;
        font-weight: 500;
      }
    }
  
    .mat-nav-list {
      padding-top: 0;
    }
  
    .mat-expansion-panel {
      border-radius: 0;
      box-shadow: none;
      
      .mat-expansion-panel-header {
        padding: 0 16px;
        
        mat-panel-title {
          display: flex;
          align-items: center;
          
          mat-icon {
            margin-right: 16px;
          }
        }
      }
    }
  
    .mat-list-item {
      &.active {
        background-color: rgba(0, 0, 0, 0.04);
        color: #1976d2;
        
        mat-icon {
          color: #1976d2;
        }
      }
    }
  
    mat-icon {
      margin-right: 16px;
      color: rgba(0, 0, 0, 0.54);
    }
  }
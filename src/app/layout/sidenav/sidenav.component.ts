// src/app/layout/sidenav/sidenav.component.ts
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MenuItem } from '../../core/auth/types/menu.types';
import { MenuService } from '../../core/auth/services/menu.service';
import { AuthService } from '../../core/auth/services/auth.service';

@Component({
  selector: 'app-sidenav',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatListModule,
    MatIconModule,
    MatExpansionModule,
    MatTooltipModule,
  ],
  templateUrl: './sidenav.component.html',
  styleUrls: ['./sidenav.component.scss'],
})
export class SidenavComponent {
  menuItems: MenuItem[] = [];

  constructor(
    private menuService: MenuService,
    private authService: AuthService
  ) {
    this.menuItems = this.menuService.getMenuItems();
  }

  canShowMenuItem(item: MenuItem): boolean {
    if (!item.roles) return true;
    return item.roles.some((role) => this.authService.hasRole(role));
  }
}

import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { SidenavComponent } from '../sidenav/sidenav.component';
import { HeaderComponent } from '../header/header.component';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    MatSidenavModule,
    MatToolbarModule,
    SidenavComponent,
    HeaderComponent,
  ],
  template: `
    <mat-sidenav-container class="sidenav-container">
      <mat-sidenav #sidenav mode="side" opened class="sidenav">
        <app-sidenav></app-sidenav>
      </mat-sidenav>

      <mat-sidenav-content>
        <app-header (menuToggled)="sidenav.toggle()"></app-header>
        <div class="content">
          <router-outlet></router-outlet>
        </div>
      </mat-sidenav-content>
    </mat-sidenav-container>
  `,
  styles: [
    `
      .sidenav-container {
        height: 100vh;
        width: 100vw;
      }

      .sidenav {
        width: 260px;
        background: #f5f5f5;
      }

      .content {
        padding: 20px;
        height: calc(100vh - 64px);
        overflow-y: auto;
      }
    `,
  ],
})
export class MainLayoutComponent {}

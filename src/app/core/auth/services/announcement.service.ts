// src/app/core/auth/services/announcement.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface Announcement {
  id: number;
  title: string;
  description?: string;
  image_url?: string;
  created_by?: number;
  updated_by?: number;
  created_at: string;
  updated_at?: string;
  created_by_name?: string;
  updated_by_name?: string;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class AnnouncementService {
  private apiUrl = `${environment.apiUrl}/announcements`;

  constructor(private http: HttpClient) {}

  getAllAnnouncements(): Observable<ApiResponse<Announcement[]>> {
    return this.http.get<ApiResponse<Announcement[]>>(this.apiUrl);
  }

  getAnnouncementById(id: number): Observable<ApiResponse<Announcement>> {
    return this.http.get<ApiResponse<Announcement>>(`${this.apiUrl}/${id}`);
  }

  createAnnouncement(data: FormData): Observable<ApiResponse<{ id: number }>> {
    return this.http.post<ApiResponse<{ id: number }>>(this.apiUrl, data);
  }

  updateAnnouncement(
    id: number,
    data: FormData
  ): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${id}`, data);
  }

  deleteAnnouncement(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }
}

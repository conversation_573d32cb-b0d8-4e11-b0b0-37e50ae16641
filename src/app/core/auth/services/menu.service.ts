import { Injectable } from '@angular/core';
import { MenuItem } from '../types/menu.types';

@Injectable({
  providedIn: 'root',
})
export class MenuService {
  getMenuItems(): MenuItem[] {
    return [
      {
        label: 'Dashboard',
        icon: 'dashboard',
        route: '/dashboard',
      },
      {
        label: 'Farmer Management',
        icon: 'group',
        children: [
          {
            label: 'All Farmers',
            icon: 'people',
            route: '/farmers',
          },
          {
            label: 'Add Farmer',
            icon: 'person_add',
            route: '/farmers/add',
          },
        ],
      },
      {
        label: 'Marketplace',
        icon: 'storefront',
        children: [
          {
            label: 'Requirements',
            icon: 'assignment',
            route: '/marketplace/requirements',
          },
          {
            label: 'Farmer Interests',
            icon: 'handshake',
            route: '/marketplace/interests',
          },
          {
            label: 'Produce Listings',
            icon: 'agriculture',
            route: '/marketplace/produce',
          },
          {
            label: 'Transactions',
            icon: 'paid',
            route: '/marketplace/transactions',
          },

          {
            label: 'Market Prices',
            icon: 'trending_up',
            route: '/marketplace/market-prices',
          },
        ],
      },
      {
        label: 'Knowledge Hub',
        icon: 'menu_book',
        children: [
          {
            label: 'Articles',
            icon: 'article',
            route: '/knowledge-hub',
          },
          {
            label: 'Add Article',
            icon: 'post_add',
            route: '/knowledge-hub/add-article',
          },
        ],
      },
      {
        label: 'Queries',
        icon: 'help_outline',
        children: [
          {
            label: 'All Queries',
            icon: 'question_answer',
            route: '/queries',
          },
        ],
      },
      {
        label: 'Masters',
        icon: 'settings',
        children: [
          {
            label: 'States',
            icon: 'location_on',
            route: '/masters/states',
          },
          {
            label: 'Cities',
            icon: 'location_city',
            route: '/masters/cities',
          },
          {
            label: 'Farming Types',
            icon: 'agriculture',
            route: '/masters/farming-types',
          },
          {
            label: 'Irrigation Types',
            icon: 'water_drop',
            route: '/masters/irrigation-types',
          },
          {
            label: 'Crops',
            icon: 'grass',
            children: [
              {
                label: 'Categories',
                icon: 'category',
                route: '/masters/crop-categories',
              },
              {
                label: 'All Crops',
                icon: 'eco',
                route: '/masters/crops',
              },
            ],
          },
          {
            label: 'Equipment Types',
            icon: 'construction',
            route: '/masters/equipment-types',
          },
          {
            label: 'Livestock Types',
            icon: 'pets',
            route: '/masters/livestock-types',
          },
        ],
      },
      {
        label: 'Mobile Management',
        icon: 'smartphone',
        roles: ['admin', 'superadmin'],
        children: [
          {
            label: 'App Users',
            icon: 'person',
            route: '/mobile/users',
          },
          {
            label: 'App Settings',
            icon: 'settings_applications',
            route: '/mobile/settings',
          },
        ],
      },
      {
        label: 'User Management',
        icon: 'manage_accounts',
        roles: ['admin'],
        children: [
          {
            label: 'Users',
            icon: 'people',
            route: '/users',
          },
          {
            label: 'Roles',
            icon: 'admin_panel_settings',
            route: '/roles',
          },
        ],
      },
      {
        label: 'Announcements',
        icon: 'campaign',
        route: '/announcements',
        roles: ['admin', 'superadmin'],
      },
      {
        label: 'Settings',
        icon: 'settings',
        route: '/settings',
      },
    ];
  }
}

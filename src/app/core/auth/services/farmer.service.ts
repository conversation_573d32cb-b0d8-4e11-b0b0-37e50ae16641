import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

export interface Farmer {
  id: number;
  full_name: string;
  mobile_number: string;
  alternate_number?: string;
  email_id?: string;
  date_of_birth?: Date;
  gender?: string;
  address_line1?: string;
  address_line2?: string;
  locality?: string;
  city_id?: number;
  state_id?: number;
  pincode?: string;
  farm_size?: number;
  category_id?: number;
  farming_type_id?: number;
  irrigation_type_id?: number;
  farming_experience?: number;
  crops_grown?: string;
  livestock_owned?: string;
  tools_equipment?: string;
  aadhaar_number?: string;
  ration_card_number?: string;
  farmer_id?: string;
  username: string;
  password?: string;
  id_proof_url?: string;
  profile_pic_url?: string;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
  state_name?: string;
  city_name?: string;
  farming_type?: string;
  irrigation_type?: string;
  category_name?: string;
  created_by_name?: string;
  updated_by_name?: string;
}

export interface SearchParams {
  name?: string;
  mobile?: string;
  city_id?: number;
  state_id?: number;
  farming_type_id?: number;
  irrigation_type_id?: number;
  category_id?: number;
  crop_id?: number;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

export interface FarmerStats {
  total: number;
  active: number;
  byState: { state_name: string; count: number }[];
  byFarmingType: { type_name: string; count: number }[];
}

@Injectable({
  providedIn: 'root',
})
export class FarmerService {
  private apiUrl = `${environment.apiUrl}/farmers`;

  constructor(private http: HttpClient) {}

  getAllFarmers(): Observable<Farmer[]> {
    return this.http.get<ApiResponse<Farmer[]>>(this.apiUrl).pipe(
      map((response) => response.data),
      catchError(this.handleError)
    );
  }

  searchFarmers(params: SearchParams): Observable<Farmer[]> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.http
      .get<ApiResponse<Farmer[]>>(
        `${this.apiUrl}/search?${queryParams.toString()}`
      )
      .pipe(
        map((response) => response.data),
        catchError(this.handleError)
      );
  }

  getFarmerById(id: number): Observable<Farmer> {
    return this.http.get<ApiResponse<Farmer>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => response.data),
      catchError(this.handleError)
    );
  }

  getFarmerByUsername(username: string): Observable<Farmer> {
    return this.http
      .get<ApiResponse<Farmer>>(`${this.apiUrl}/username/${username}`)
      .pipe(
        map((response) => response.data),
        catchError(this.handleError)
      );
  }

  getById(id: number): Observable<ApiResponse<Farmer>> {
    return this.http
      .get<ApiResponse<Farmer>>(`${this.apiUrl}/${id}`)
      .pipe(catchError(this.handleError));
  }

  createFarmer(data: FormData): Observable<number> {
    console.log('Form data being sent to API:');
    data.forEach((value, key) => {
      console.log(`${key}:`, value);
    });

    // First, validate the required fields based on DB structure
    const fullName = data.get('full_name');
    const mobileNumber = data.get('mobile_number');
    const username = data.get('username');

    // Validate required fields
    if (!fullName || !mobileNumber || !username) {
      return throwError(
        () =>
          new Error(
            'Required fields missing: full name, mobile number, and username are required'
          )
      );
    }

    // Create fresh FormData to ensure clean data
    const processedFormData = new FormData();

    // Required fields first (NOT NULL in DB)
    processedFormData.append('full_name', fullName.toString().trim());
    processedFormData.append('mobile_number', mobileNumber.toString().trim());
    processedFormData.append('username', username.toString().trim());

    // Handle optional string fields
    const stringFields = [
      'email_id',
      'gender',
      'alternate_number',
      'address_line1',
      'address_line2',
      'locality',
      'pincode',
      'aadhaar_number',
      'ration_card_number',
      'farmer_id',
      'password',
    ];

    stringFields.forEach((field) => {
      const value = data.get(field);
      if (value) {
        processedFormData.append(field, value.toString().trim());
      }
    });

    // Handle numeric fields (all integers in DB)
    const numericFields = [
      'state_id',
      'city_id',
      'category_id',
      'farming_type_id',
      'irrigation_type_id',
      'farming_experience',
    ];

    numericFields.forEach((field) => {
      const value = data.get(field);
      if (value) {
        const numValue = Number(value);
        if (!isNaN(numValue)) {
          processedFormData.append(field, numValue.toString());
        }
      }
    });

    // Handle decimal fields
    const farmSize = data.get('farm_size');
    if (farmSize) {
      const numValue = Number(farmSize);
      if (!isNaN(numValue)) {
        processedFormData.append('farm_size', numValue.toFixed(2));
      }
    }

    // Handle date fields
    const dateOfBirth = data.get('date_of_birth');
    if (dateOfBirth) {
      try {
        const date = new Date(dateOfBirth.toString());
        processedFormData.append(
          'date_of_birth',
          date.toISOString().split('T')[0]
        );
      } catch (error) {
        console.error('Invalid date format:', error);
      }
    }

    // Handle array fields and map to correct DB columns
    const arrayFields = {
      crops: 'crops_grown',
      livestock: 'livestock_owned',
      equipment: 'tools_equipment',
    };

    Object.entries(arrayFields).forEach(([fromField, toField]) => {
      const value = data.get(fromField);
      if (value) {
        try {
          // Parse the JSON string and stringify it again to ensure proper format
          const parsedArray = JSON.parse(value.toString());
          processedFormData.append(toField, JSON.stringify(parsedArray));
        } catch (error) {
          console.error(`Error processing ${fromField}:`, error);
        }
      }
    });

    // Handle file uploads and URLs
    const idProof = data.get('id_proof');
    if (idProof instanceof Blob) {
      processedFormData.append('id_proof', idProof);
    }

    const profilePic = data.get('profile_pic');
    if (profilePic instanceof Blob) {
      processedFormData.append('profile_pic', profilePic);
    }

    // Additional debug logging
    console.log('Original form data:');
    data.forEach((value, key) => {
      if (value instanceof Blob) {
        console.log(`${key}: File - ${(value as File).name}`);
      } else {
        console.log(`${key}: ${value}`);
      }
    });

    // Log the final processed data being sent
    console.log('Processed form data:');
    const debugData: any = {};
    processedFormData.forEach((value, key) => {
      debugData[key] =
        value instanceof Blob ? `File - ${(value as File).name}` : value;
    });
    console.log('Sending to API:', debugData);

    // Make the API call
    return this.http
      .post<ApiResponse<{ id: number }>>(this.apiUrl, processedFormData)
      .pipe(
        map((response) => {
          if (response.status === 'error') {
            throw new Error(response.message || 'Failed to create farmer');
          }
          return response.data.id;
        }),
        catchError((error) => {
          console.error('API Error:', error);
          if (error.error?.message) {
            return throwError(() => new Error(error.error.message));
          }
          return throwError(
            () => new Error('Failed to create farmer. Please try again.')
          );
        })
      );
  }

  updateFarmer(id: number, data: FormData): Observable<void> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${id}`, data).pipe(
      map(() => void 0),
      catchError(this.handleError)
    );
  }

  deleteFarmer(id: number): Observable<void> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`).pipe(
      map(() => void 0),
      catchError(this.handleError)
    );
  }

  getFarmerStats(): Observable<FarmerStats> {
    return this.http.get<ApiResponse<FarmerStats>>(`${this.apiUrl}/stats`).pipe(
      map((response) => response.data),
      catchError(this.handleError)
    );
  }

  uploadFarmerDocument(
    farmerId: number,
    documentType: string,
    file: File
  ): Observable<string> {
    const formData = new FormData();
    formData.append('document', file);

    return this.http
      .post<ApiResponse<{ url: string }>>(
        `${this.apiUrl}/${farmerId}/documents/${documentType}`,
        formData
      )
      .pipe(
        map((response) => response.data.url),
        catchError(this.handleError)
      );
  }

  changePassword(
    farmerId: number,
    currentPassword: string,
    newPassword: string
  ): Observable<void> {
    return this.http
      .post<ApiResponse<void>>(`${this.apiUrl}/${farmerId}/change-password`, {
        current_password: currentPassword,
        new_password: newPassword,
      })
      .pipe(
        map(() => void 0),
        catchError(this.handleError)
      );
  }

  resetPassword(farmerId: number): Observable<string> {
    return this.http
      .post<ApiResponse<{ password: string }>>(
        `${this.apiUrl}/${farmerId}/reset-password`,
        {}
      )
      .pipe(
        map((response) => response.data.password),
        catchError(this.handleError)
      );
  }

  private handleError(error: HttpErrorResponse) {
    console.error('API Error:', error);
    let errorMessage = 'An unexpected error occurred';

    if (error.error instanceof ErrorEvent) {
      errorMessage = error.error.message;
    } else {
      errorMessage = error.error?.message || `Server error: ${error.status}`;

      switch (error.status) {
        case 400:
          errorMessage = 'Invalid request. Please check your input data.';
          break;
        case 401:
          errorMessage = 'Unauthorized. Please log in again.';
          break;
        case 403:
          errorMessage =
            'Forbidden. You do not have permission to perform this action.';
          break;
        case 404:
          errorMessage = 'Farmer not found.';
          break;
        case 409:
          errorMessage = 'Conflict. This record may already exist.';
          break;
        case 500:
          errorMessage = 'Server error. Please try again later.';
          break;
      }
    }

    return throwError(() => new Error(errorMessage));
  }
}

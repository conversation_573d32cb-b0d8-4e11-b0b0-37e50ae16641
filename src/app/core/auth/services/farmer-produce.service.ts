import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class FarmerProduceService {
  private apiUrl = `${environment.apiUrl}/marketplace/farmer-produce`;
  private transactionsUrl = `${environment.apiUrl}/marketplace/transactions`;

  constructor(private http: HttpClient) {}

  // Get all produce listings
  getAllProduce(filters: any = {}): Observable<any> {
    // Convert filters to query parameters
    const params = this.buildQueryParams(filters);
    return this.http.get(this.apiUrl, { params });
  }

  // Get produce listings with market price and requirement info
  getProduceWithDetails(filters: any = {}): Observable<any> {
    const params = this.buildQueryParams(filters);
    return this.http.get(`${this.apiUrl}/with-details`, { params });
  }

  // Get a single produce listing by ID
  getProduceById(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${id}`);
  }

  /**
   * Get produce listings that can fulfill a specific requirement with improved error handling
   * @param requirementId The ID of the requirement
   * @returns Observable with matching produce data
   */
  getProduceForRequirement(requirementId: number): Observable<any> {
    return this.http
      .get(`${this.apiUrl}/for-requirement/${requirementId}`)
      .pipe(
        // Add timeout to prevent hanging requests
        timeout(15000),
        // Add better error handling
        catchError((error) => {
          console.error(
            `Error fetching matching produce for requirement ${requirementId}:`,
            error
          );

          // Format our error response to match API format
          const errorResponse = {
            status: 'error',
            message: 'Failed to retrieve matching produce',
            data: {
              requirement: { id: requirementId },
              matching_produce: [],
            },
          };

          // If we have more specific error info, use it
          if (error.error?.message) {
            errorResponse.message = error.error.message;
          } else if (error.status === 500) {
            errorResponse.message =
              'Server error while retrieving matching produce. Please try again later.';
          } else if (error.status === 404) {
            errorResponse.message =
              'No matching produce found for this requirement.';
          }

          // Return this as an observable so the component can handle it normally
          return of(errorResponse);
        })
      );
  }

  // Get matching requirements for a produce listing
  getMatchingRequirements(produceId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${produceId}/matching-requirements`);
  }

  // Get market price information for a produce item
  getMarketPrice(produceId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${produceId}/market-price`);
  }

  // Get existing transactions for a produce item
  getProduceTransactions(produceId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${produceId}/transactions`);
  }

  // Create a new produce listing
  createProduce(produceData: FormData): Observable<any> {
    return this.http.post(this.apiUrl, produceData);
  }

  // Update a produce listing
  updateProduce(id: number, produceData: FormData): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, produceData);
  }

  // Update produce status
  updateProduceStatus(id: number, status: string): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${id}/status`, { status });
  }

  // Batch update produce status
  batchUpdateStatus(produceIds: number[], status: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/batch-update`, {
      produceIds,
      status,
    });
  }

  // Create transaction from produce (link to requirement)
  createTransactionFromProduce(
    produceId: number,
    requirementId: number,
    quantity: number,
    notes?: string
  ): Observable<any> {
    return this.http.post(`${this.transactionsUrl}/from-produce`, {
      produceId,
      requirementId,
      quantity,
      notes,
    });
  }

  // Create new requirement and link produce
  createRequirementFromProduce(
    produceId: number,
    requirementData: any
  ): Observable<any> {
    return this.http.post(
      `${this.transactionsUrl}/produce/${produceId}/new-requirement`,
      requirementData
    );
  }

  // Helper method to convert filters object to HttpParams
  private buildQueryParams(filters: any): any {
    const params: any = {};

    // Add all non-empty filters
    Object.keys(filters).forEach((key) => {
      if (
        filters[key] !== null &&
        filters[key] !== undefined &&
        filters[key] !== ''
      ) {
        // Handle date objects
        if (filters[key] instanceof Date) {
          params[key] = filters[key].toISOString().split('T')[0];
        } else {
          params[key] = filters[key];
        }
      }
    });

    return params;
  }
}

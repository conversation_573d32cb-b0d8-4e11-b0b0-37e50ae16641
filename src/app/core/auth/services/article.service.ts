// src/app/core/auth/services/knowledge-hub/article.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface Article {
  id?: number;
  title: string;
  description: string;
  image_url?: string;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
  created_by_name?: string;
  updated_by_name?: string;
  likes_count?: number;
  views_count?: number;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  private apiUrl = `${environment.apiUrl}/knowledgehub/articles`;

  constructor(private http: HttpClient) {}

  getAllArticles(): Observable<ApiResponse<Article[]>> {
    return this.http.get<ApiResponse<Article[]>>(this.apiUrl);
  }

  getArticleById(id: number): Observable<ApiResponse<Article>> {
    return this.http.get<ApiResponse<Article>>(`${this.apiUrl}/${id}`);
  }

  createArticle(formData: FormData): Observable<ApiResponse<{ id: number }>> {
    return this.http.post<ApiResponse<{ id: number }>>(this.apiUrl, formData);
  }

  updateArticle(id: number, formData: FormData): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${id}`, formData);
  }

  deleteArticle(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  // Optional: If you want to implement the like functionality
  toggleLike(id: number): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${id}/like`, {});
  }

  // Optional: Record a view for analytics
  recordView(id: number): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/${id}/view`, {});
  }
}

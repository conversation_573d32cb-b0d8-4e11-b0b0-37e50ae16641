// src/app/core/auth/services/requirement.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface Requirement {
  id?: number;
  product_id: number;
  crop_name?: string; // From join with master_crops
  variety?: string;
  quantity: number;
  unit_type: string;
  price_offered: number;
  status?: string;
  description?: string;
  required_by?: Date | string;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
  created_by_name?: string; // From join with users
  updated_by_name?: string; // From join with users
  market_price_at_creation?: number; // Store the market price when requirement was created
  market_price_unit?: string;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class RequirementService {
  private apiUrl = `${environment.apiUrl}/marketplace/requirements`;

  constructor(private http: HttpClient) {}

  getAllRequirements(
    filters: any = {}
  ): Observable<ApiResponse<Requirement[]>> {
    // Add query params for filtering
    let queryParams = '';
    if (filters) {
      const params = new URLSearchParams();
      if (filters.product_id) params.append('product_id', filters.product_id);
      if (filters.status) params.append('status', filters.status);
      queryParams = params.toString() ? `?${params.toString()}` : '';
    }

    return this.http.get<ApiResponse<Requirement[]>>(
      `${this.apiUrl}${queryParams}`
    );
  }

  getActiveRequirements(): Observable<ApiResponse<Requirement[]>> {
    return this.http.get<ApiResponse<Requirement[]>>(`${this.apiUrl}/active`);
  }

  getRequirementById(id: number): Observable<ApiResponse<Requirement>> {
    return this.http.get<ApiResponse<Requirement>>(`${this.apiUrl}/${id}`);
  }

  createRequirement(
    requirement: Requirement
  ): Observable<ApiResponse<{ id: number }>> {
    return this.http.post<ApiResponse<{ id: number }>>(
      this.apiUrl,
      requirement
    );
  }

  updateRequirement(
    id: number,
    requirement: Requirement
  ): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(
      `${this.apiUrl}/${id}`,
      requirement
    );
  }

  deleteRequirement(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  updateRequirementStatus(
    id: number,
    status: string
  ): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(`${this.apiUrl}/${id}/status`, {
      status,
    });
  }

  // Find available produce that matches the requirement
  getAvailableProduceForRequirement(
    requirementId: number
  ): Observable<ApiResponse<any[]>> {
    return this.http.get<ApiResponse<any[]>>(
      `${this.apiUrl}/${requirementId}/available-produce`
    );
  }
}

// src/app/core/auth/services/query.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface Query {
  id?: number;
  title: string;
  description: string;
  image_url?: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  farmer_id: number;
  assigned_to?: number;
  resolved_at?: Date;
  created_by: number;
  created_at?: Date;
  farmer_name?: string;
  responses?: QueryResponse[];
  response_count?: number;
}

export interface QueryResponse {
  id?: number;
  query_id: number;
  response: string;
  image_url?: string;
  created_by: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
  responder_name?: string;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class QueryService {
  private apiUrl = `${environment.apiUrl}/queries`;

  constructor(private http: HttpClient) {}

  getAllQueries(): Observable<ApiResponse<Query[]>> {
    return this.http.get<ApiResponse<Query[]>>(this.apiUrl);
  }

  getQueryById(id: number): Observable<ApiResponse<Query>> {
    return this.http.get<ApiResponse<Query>>(`${this.apiUrl}/${id}`);
  }

  addResponse(
    queryId: number,
    data: FormData
  ): Observable<ApiResponse<{ id: number }>> {
    return this.http.post<ApiResponse<{ id: number }>>(
      `${this.apiUrl}/${queryId}/responses`,
      data
    );
  }

  updateStatus(queryId: number, status: string): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(
      `${this.apiUrl}/${queryId}/status`,
      { status }
    );
  }
}

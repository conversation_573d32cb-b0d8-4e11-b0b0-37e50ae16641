import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface Transaction {
  id: number;
  transaction_type: 'requirement_based' | 'direct_purchase';
  farmer_id: number;
  fpo_requirement_id?: number;
  farmer_produce_id: number;
  quantity: number;
  unit_type: string;
  price_per_unit: number;
  total_amount: number;
  status: 'accepted' | 'in_progress' | 'delivered' | 'closed' | 'cancelled';
  notes?: string;
  created_at: string;
  updated_at?: string;
  farmer_name: string;
  crop_name: string;
  produce_image_url?: string;
}

export interface TransactionFilter {
  farmer_id?: number;
  requirement_id?: number;
  produce_id?: number;
  status?: string;
  transaction_type?: string;
}

@Injectable({
  providedIn: 'root',
})
export class TransactionService {
  private apiUrl = `${environment.apiUrl}/marketplace/transactions`;

  constructor(private http: HttpClient) {}

  getAllTransactions(filters?: TransactionFilter): Observable<any> {
    let url = this.apiUrl;

    if (filters) {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value.toString());
        }
      });

      if (params.toString()) {
        url += `?${params.toString()}`;
      }
    }

    return this.http.get(url);
  }

  getTransactionById(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${id}`);
  }

  updateTransactionStatus(id: number, status: string): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${id}/status`, { status });
  }

  exportTransactions(filters?: TransactionFilter): Observable<Blob> {
    let url = `${this.apiUrl}/export`;

    if (filters) {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value.toString());
        }
      });

      if (params.toString()) {
        url += `?${params.toString()}`;
      }
    }

    return this.http.get(url, { responseType: 'blob' });
  }
}

// src/app/core/services/marketplace/market-price.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

export interface MarketPrice {
  id?: number;
  product_id: number;
  crop_name?: string;
  price_per_unit: number;
  unit_type: string;
  price_date: string;
  notes?: string;
  crop_image_url?: string;
  created_by?: number;
  created_by_name?: string;
  updated_by?: number;
  updated_by_name?: string;
  created_at?: string;
  updated_at?: string;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class MarketPriceService {
  private apiUrl = `${environment.apiUrl}/marketplace/market-prices`;

  constructor(private http: HttpClient) {}

  getAllPrices(filters: any = {}): Observable<ApiResponse<MarketPrice[]>> {
    // Convert filters to query params
    const queryParams = new URLSearchParams();

    if (filters.product_id) {
      queryParams.set('product_id', filters.product_id.toString());
    }

    if (filters.date_from) {
      queryParams.set('date_from', filters.date_from);
    }

    if (filters.date_to) {
      queryParams.set('date_to', filters.date_to);
    }

    const url = queryParams.toString()
      ? `${this.apiUrl}?${queryParams.toString()}`
      : this.apiUrl;

    return this.http.get<ApiResponse<MarketPrice[]>>(url);
  }

  getLatestPrices(): Observable<ApiResponse<MarketPrice[]>> {
    return this.http.get<ApiResponse<MarketPrice[]>>(`${this.apiUrl}/latest`);
  }

  getPriceById(id: number): Observable<ApiResponse<MarketPrice>> {
    return this.http.get<ApiResponse<MarketPrice>>(`${this.apiUrl}/${id}`);
  }

  getPriceHistory(productId: number): Observable<ApiResponse<any>> {
    return this.http.get<ApiResponse<any>>(
      `${this.apiUrl}/${productId}/history`
    );
  }

  createPrice(data: MarketPrice): Observable<ApiResponse<any>> {
    // Make sure required fields have the correct type
    const payload = {
      product_id: Number(data.product_id),
      price_per_unit: Number(data.price_per_unit),
      unit_type: data.unit_type,
      price_date: data.price_date,
      notes: data.notes || '',
    };

    return this.http.post<ApiResponse<any>>(this.apiUrl, payload);
  }

  updatePrice(id: number, data: MarketPrice): Observable<ApiResponse<any>> {
    // Make sure required fields have the correct type
    const payload = {
      product_id: Number(data.product_id),
      price_per_unit: Number(data.price_per_unit),
      unit_type: data.unit_type,
      price_date: data.price_date,
      notes: data.notes || '',
    };

    return this.http.put<ApiResponse<any>>(`${this.apiUrl}/${id}`, payload);
  }

  deletePrice(id: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`);
  }

  downloadPriceTemplate(): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/template`, {
      responseType: 'blob',
    });
  }

  uploadPriceSheet(file: File): Observable<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<ApiResponse<any>>(`${this.apiUrl}/upload`, formData);
  }

  exportPrices(filters: any = {}): Observable<Blob> {
    // Convert filters to query params
    const queryParams = new URLSearchParams();

    if (filters.product_id) {
      queryParams.set('product_id', filters.product_id.toString());
    }

    if (filters.date_from) {
      queryParams.set('date_from', filters.date_from);
    }

    if (filters.date_to) {
      queryParams.set('date_to', filters.date_to);
    }

    const url = queryParams.toString()
      ? `${this.apiUrl}/export?${queryParams.toString()}`
      : `${this.apiUrl}/export`;

    return this.http.get(url, {
      responseType: 'blob',
    });
  }
}

// crop.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../../environments/environment';

export interface Crop {
  id?: number;
  crop_name: string;
  description?: string;
  image_url?: string;
  is_active?: boolean;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class CropService {
  private apiUrl = `${environment.apiUrl}/masters/crops`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<ApiResponse<Crop[]>> {
    return this.http.get<ApiResponse<Crop[]>>(this.apiUrl);
  }

  getById(id: number): Observable<ApiResponse<Crop>> {
    return this.http.get<ApiResponse<Crop>>(`${this.apiUrl}/${id}`);
  }

  create(crop: Crop): Observable<ApiResponse<Crop>> {
    return this.http.post<ApiResponse<Crop>>(this.apiUrl, crop);
  }

  // Add this new method for handling image uploads
  createWithImage(formData: FormData): Observable<ApiResponse<Crop>> {
    return this.http.post<ApiResponse<Crop>>(this.apiUrl, formData);
  }

  // Add this new method for updating with image
  updateWithImage(id: number, formData: FormData): Observable<ApiResponse<Crop>> {
    return this.http.put<ApiResponse<Crop>>(`${this.apiUrl}/${id}`, formData);
  }

  update(id: number, crop: Crop): Observable<ApiResponse<Crop>> {
    return this.http.put<ApiResponse<Crop>>(`${this.apiUrl}/${id}`, crop);
  }

  delete(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  toggleStatus(id: number, status: boolean): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(
      `${this.apiUrl}/${id}/toggle-status`,
      { status }
    );
  }
}
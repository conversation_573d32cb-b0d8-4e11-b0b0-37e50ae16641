import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../src/environments/environment';

export interface FarmingType {
  id?: number;
  type_name: string;
  description?: string;
  is_active?: boolean;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class FarmingTypeService {
  private apiUrl = `${environment.apiUrl}/masters/farming-types`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<ApiResponse<FarmingType[]>> {
    return this.http.get<ApiResponse<FarmingType[]>>(this.apiUrl);
  }

  getById(id: number): Observable<ApiResponse<FarmingType>> {
    return this.http.get<ApiResponse<FarmingType>>(`${this.apiUrl}/${id}`);
  }

  create(farmingType: FarmingType): Observable<ApiResponse<FarmingType>> {
    return this.http.post<ApiResponse<FarmingType>>(this.apiUrl, farmingType);
  }

  update(
    id: number,
    farmingType: FarmingType
  ): Observable<ApiResponse<FarmingType>> {
    return this.http.put<ApiResponse<FarmingType>>(
      `${this.apiUrl}/${id}`,
      farmingType
    );
  }

  delete(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  toggleStatus(id: number, status: boolean): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(
      `${this.apiUrl}/${id}/toggle-status`,
      { status }
    );
  }
}

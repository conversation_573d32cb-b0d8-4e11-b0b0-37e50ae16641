import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../../environments/environment';

export interface LivestockType {
  id?: number;
  type_name: string;
  description?: string;
  is_active?: boolean;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class LivestockTypeService {
  private apiUrl = `${environment.apiUrl}/masters/livestock-types`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<ApiResponse<LivestockType[]>> {
    return this.http.get<ApiResponse<LivestockType[]>>(this.apiUrl);
  }

  getById(id: number): Observable<ApiResponse<LivestockType>> {
    return this.http.get<ApiResponse<LivestockType>>(`${this.apiUrl}/${id}`);
  }

  create(data: Partial<LivestockType>): Observable<ApiResponse<LivestockType>> {
    return this.http.post<ApiResponse<LivestockType>>(this.apiUrl, data);
  }

  update(
    id: number,
    data: Partial<LivestockType>
  ): Observable<ApiResponse<LivestockType>> {
    return this.http.put<ApiResponse<LivestockType>>(
      `${this.apiUrl}/${id}`,
      data
    );
  }

  delete(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  toggleStatus(id: number, status: boolean): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(
      `${this.apiUrl}/${id}/toggle-status`,
      { status }
    );
  }
}

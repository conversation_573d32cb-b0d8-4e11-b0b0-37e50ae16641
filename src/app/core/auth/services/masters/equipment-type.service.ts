import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../src/environments/environment';

export interface EquipmentType {
  id?: number;
  type_name: string;
  description?: string;
  is_active?: boolean;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class EquipmentTypeService {
  private apiUrl = `${environment.apiUrl}/masters/equipment-types`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<ApiResponse<EquipmentType[]>> {
    return this.http.get<ApiResponse<EquipmentType[]>>(this.apiUrl);
  }

  getById(id: number): Observable<ApiResponse<EquipmentType>> {
    return this.http.get<ApiResponse<EquipmentType>>(`${this.apiUrl}/${id}`);
  }

  create(equipmentType: EquipmentType): Observable<ApiResponse<EquipmentType>> {
    return this.http.post<ApiResponse<EquipmentType>>(
      this.apiUrl,
      equipmentType
    );
  }

  update(
    id: number,
    equipmentType: EquipmentType
  ): Observable<ApiResponse<EquipmentType>> {
    return this.http.put<ApiResponse<EquipmentType>>(
      `${this.apiUrl}/${id}`,
      equipmentType
    );
  }

  delete(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  toggleStatus(id: number, status: boolean): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(
      `${this.apiUrl}/${id}/toggle-status`,
      { status }
    );
  }
}

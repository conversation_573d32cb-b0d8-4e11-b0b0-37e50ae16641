import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../src/environments/environment';

export interface State {
  id?: number;
  state_name: string;
  state_code: string;
  is_active?: boolean;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class StateService {
  private apiUrl = `${environment.apiUrl}/masters/states`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<ApiResponse<State[]>> {
    return this.http.get<ApiResponse<State[]>>(this.apiUrl);
  }

  getById(id: number): Observable<ApiResponse<State>> {
    return this.http.get<ApiResponse<State>>(`${this.apiUrl}/${id}`);
  }

  create(state: State): Observable<ApiResponse<State>> {
    return this.http.post<ApiResponse<State>>(this.apiUrl, state);
  }

  update(id: number, state: State): Observable<ApiResponse<State>> {
    return this.http.put<ApiResponse<State>>(`${this.apiUrl}/${id}`, state);
  }

  delete(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  toggleStatus(id: number, status: boolean): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(
      `${this.apiUrl}/${id}/toggle-status`,
      { status }
    );
  }
}

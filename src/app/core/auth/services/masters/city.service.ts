import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../src/environments/environment';

export interface City {
  id?: number;
  city_name: string;
  state_id: number;
  state_name?: string; // From join
  is_active?: boolean;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class CityService {
  private apiUrl = `${environment.apiUrl}/masters/cities`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<ApiResponse<City[]>> {
    return this.http.get<ApiResponse<City[]>>(this.apiUrl);
  }

  getById(id: number): Observable<ApiResponse<City>> {
    return this.http.get<ApiResponse<City>>(`${this.apiUrl}/${id}`);
  }

  create(city: City): Observable<ApiResponse<City>> {
    return this.http.post<ApiResponse<City>>(this.apiUrl, city);
  }

  update(id: number, city: City): Observable<ApiResponse<City>> {
    return this.http.put<ApiResponse<City>>(`${this.apiUrl}/${id}`, city);
  }

  delete(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  toggleStatus(id: number, status: boolean): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(
      `${this.apiUrl}/${id}/toggle-status`,
      { status }
    );
  }

  getCitiesByState(stateId: number): Observable<ApiResponse<City[]>> {
    return this.http.get<ApiResponse<City[]>>(
      `${this.apiUrl}/state/${stateId}`
    );
  }
}

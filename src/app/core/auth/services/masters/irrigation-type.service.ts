import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../src/environments/environment';

export interface IrrigationType {
  id?: number;
  type_name: string;
  description?: string;
  is_active?: boolean;
  created_by?: number;
  updated_by?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
}

@Injectable({
  providedIn: 'root',
})
export class IrrigationTypeService {
  private apiUrl = `${environment.apiUrl}/masters/irrigation-types`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<ApiResponse<IrrigationType[]>> {
    return this.http.get<ApiResponse<IrrigationType[]>>(this.apiUrl);
  }

  getById(id: number): Observable<ApiResponse<IrrigationType>> {
    return this.http.get<ApiResponse<IrrigationType>>(`${this.apiUrl}/${id}`);
  }

  create(
    irrigationType: IrrigationType
  ): Observable<ApiResponse<IrrigationType>> {
    return this.http.post<ApiResponse<IrrigationType>>(
      this.apiUrl,
      irrigationType
    );
  }

  update(
    id: number,
    irrigationType: IrrigationType
  ): Observable<ApiResponse<IrrigationType>> {
    return this.http.put<ApiResponse<IrrigationType>>(
      `${this.apiUrl}/${id}`,
      irrigationType
    );
  }

  delete(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`);
  }

  toggleStatus(id: number, status: boolean): Observable<ApiResponse<void>> {
    return this.http.patch<ApiResponse<void>>(
      `${this.apiUrl}/${id}/toggle-status`,
      { status }
    );
  }
}

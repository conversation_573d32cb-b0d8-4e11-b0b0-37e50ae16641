// src/core/auth/services/interest.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class InterestService {
  private apiUrl = `${environment.apiUrl}/marketplace/interests`;

  constructor(private http: HttpClient) {}

  /**
   * Get all interests with optional filters
   * @param filters Optional filters for the query
   * @returns Observable with interests data
   */
  getAllInterests(filters: any = {}): Observable<any> {
    let params = new HttpParams();

    // Add filters to params if they exist
    if (filters.farmer_id) {
      params = params.set('farmer_id', filters.farmer_id);
    }

    if (filters.requirement_id) {
      params = params.set('requirement_id', filters.requirement_id);
    }

    if (filters.product_id) {
      params = params.set('product_id', filters.product_id);
    }

    if (filters.status) {
      params = params.set('status', filters.status);
    }

    if (filters.farmer_name) {
      params = params.set('farmer_name', filters.farmer_name);
    }

    return this.http.get(this.apiUrl, { params });
  }

  /**
   * Get a specific interest by ID
   * @param id Interest ID
   * @returns Observable with interest data
   */
  getInterestById(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${id}`);
  }

  /**
   * Get interests for a specific requirement
   * @param requirementId Requirement ID
   * @returns Observable with interests data
   */
  getInterestsByRequirement(requirementId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/requirement/${requirementId}`);
  }

  /**
   * Get interests from a specific farmer
   * @param farmerId Farmer ID
   * @returns Observable with interests data
   */
  getInterestsByFarmer(farmerId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/farmer/${farmerId}`);
  }

  /**
   * Create a new interest
   * @param interestData Interest data
   * @returns Observable with created interest data
   */
  createInterest(interestData: any): Observable<any> {
    return this.http.post(this.apiUrl, interestData);
  }

  /**
   * Update an interest's status
   * @param id Interest ID
   * @param status New status
   * @returns Observable with update result
   */
  updateInterestStatus(id: number, status: string): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${id}/status`, { status });
  }

  /**
   * Delete an interest
   * @param id Interest ID
   * @returns Observable with delete result
   */
  deleteInterest(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }

  /**
   * Get interest statistics
   * @returns Observable with statistics data
   */
  getInterestStatistics(): Observable<any> {
    return this.http.get(`${this.apiUrl}/statistics`);
  }
}

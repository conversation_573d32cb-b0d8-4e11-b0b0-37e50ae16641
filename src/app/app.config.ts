import { ApplicationConfig, inject } from '@angular/core';
import { provideRouter, withHashLocation } from '@angular/router';

import {
  provideHttpClient,
  withInterceptors,
  HttpErrorResponse,
} from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { routes } from './app.routes';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import {
  MAT_SNACK_BAR_DEFAULT_OPTIONS,
  MatSnackBarModule,
} from '@angular/material/snack-bar';

export function provideHttpInterceptors() {
  return provideHttpClient(
    withInterceptors([
      // Auth Interceptor
      (req, next) => {
        const token = localStorage.getItem('token');

        if (!req.url.includes('/auth/login') && token) {
          const authReq = req.clone({
            headers: req.headers.set('Authorization', `Bearer ${token}`),
          });
          return next(authReq);
        }
        return next(req);
      },
      // Error Interceptor
      (req, next) => {
        return next(req).pipe(
          catchError((error: HttpErrorResponse) => {
            console.error('API Error:', error);
            if (error.status === 401) {
              localStorage.removeItem('token');
              localStorage.removeItem('user');
            }
            return throwError(() => error);
          })
        );
      },
    ])
  );
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes, withHashLocation()),
    provideHttpInterceptors(),
    provideAnimations(),
    provideAnimationsAsync(),
    {
      provide: MAT_SNACK_BAR_DEFAULT_OPTIONS,
      useValue: {
        duration: 5000,
        horizontalPosition: 'end',
        verticalPosition: 'top',
      },
    },
  ],
};

import { Routes } from '@angular/router';
import { LoginComponent } from './features/auth/login/login.component';
import { MainLayoutComponent } from './layout/main-layout/main-layout.component';
import { DashboardComponent } from './features/dashboard/dashboard.component';
import { authGuard, loginGuard } from './core/auth/guards/auth.guard';
import { ListLivestocktypesComponent } from './features/masters/livestock-types/list-livestocktypes/list-livestocktypes.component';
import { ListStatesComponent } from './features/masters/states/list-states/list-states.component';
import { EditFarmerComponent } from './features/farmers/edit-farmer/edit-farmer.component';
import { AddFarmerComponent } from './features/farmers/add-farmer/add-farmer.component';
import { ListFarmersComponent } from './features/farmers/list-farmers/list-farmers.component';
import { FarmersComponent } from './features/farmers/farmers.component';
import { FarmerViewComponent } from './features/farmers/view-farmer/view-farmer.component';
import { LivestockTypesComponent } from './features/masters/livestock-types/livestock-types.component';
import { AddLivestocktypeComponent } from './features/masters/livestock-types/add-livestocktype/add-livestocktype.component';
import { EditLivestocktypeComponent } from './features/masters/livestock-types/edit-livestocktype/edit-livestocktype.component';
import { AddStateComponent } from './features/masters/states/add-state/add-state.component';
import { EditStateComponent } from './features/masters/states/edit-state/edit-state.component';
import { ListCitiesComponent } from './features/masters/cities/list-cities/list-cities.component';
import { AddCityComponent } from './features/masters/cities/add-city/add-city.component';
import { EditCityComponent } from './features/masters/cities/edit-city/edit-city.component';
import { CitiesComponent } from './features/masters/cities/cities.component';

import { FarmingTypesComponent } from './features/masters/farming-types/farming-types.component';
import { ListFarmingTypesComponent } from './features/masters/farming-types/list-farming-types/list-farming-types.component';
import { AddFarmingTypeComponent } from './features/masters/farming-types/add-farming-type/add-farming-type.component';
import { EditFarmingTypeComponent } from './features/masters/farming-types/edit-farming-type/edit-farming-type.component';
import { CropsComponent } from './features/masters/crops/crops.component';
import { ListCropsComponent } from './features/masters/crops/list-crops/list-crops.component';
import { AddCropComponent } from './features/masters/crops/add-crop/add-crop.component';
import { EditCropComponent } from './features/masters/crops/edit-crop/edit-crop.component';
import { EquipmentTypesComponent } from './features/masters/equipment-types/equipment-types.component';
import { ListEquipmentTypesComponent } from './features/masters/equipment-types/list-equipment-types/list-equipment-types.component';
import { AddEquipmentTypeComponent } from './features/masters/equipment-types/add-equipment-type/add-equipment-type.component';
import { EditEquipmentTypeComponent } from './features/masters/equipment-types/edit-equipment-type/edit-equipment-type.component';
import { IrrigationTypesComponent } from './features/masters/irrigation-types/irrigation-types.component';
import { ListIrrigationTypesComponent } from './features/masters/irrigation-types/list-irrigation-types/list-irrigation-types.component';
import { AddIrrigationTypeComponent } from './features/masters/irrigation-types/add-irrigation-type/add-irrigation-type.component';
import { EditIrrigationTypeComponent } from './features/masters/irrigation-types/edit-irrigation-type/edit-irrigation-type.component';
import { ArticlesComponent } from './features/knowledge-hub/articles/articles.component';
import { ListArticleComponent } from './features/knowledge-hub/articles/list-article/list-article.component';
import { AddArticleComponent } from './features/knowledge-hub/articles/add-article/add-article.component';
import { EditArticleComponent } from './features/knowledge-hub/articles/edit-article/edit-article.component';
import { ViewArticleComponent } from './features/knowledge-hub/articles/view-article/view-article.component';
import { QueriesComponent } from './features/queries/queries.component';
import { ListQueriesComponent } from './features/queries/list-queries/list-queries.component';
import { ViewQueryComponent } from './features/queries/view-query/view-query.component';
import { MarketplaceComponent } from './features/marketplace/marketplace.component';
import { MarketPricesComponent } from './features/marketplace/market-prices/market-prices.component';
import { ListMarketPricesComponent } from './features/marketplace/market-prices/list-market-prices/list-market-prices.component';
import { AddMarketPriceComponent } from './features/marketplace/market-prices/add-market-price/add-market-price.component';
import { EditMarketPriceComponent } from './features/marketplace/market-prices/edit-market-price/edit-market-price.component';
import { ListRequirementsComponent } from './features/marketplace/requirements/list-requirements/list-requirements.component';
import { AddRequirementComponent } from './features/marketplace/requirements/add-requirement/add-requirement.component';
import { EditRequirementComponent } from './features/marketplace/requirements/edit-requirement/edit-requirement.component';
import { ViewRequirementComponent } from './features/marketplace/requirements/view-requirement/view-requirement.component';
import { RequirementInterestsComponent } from './features/marketplace/interests/requirement-interests/requirement-interests.component';
import { TransactionsComponent } from './features/marketplace/transactions/transactions.component';
import { ListTransactionsComponent } from './features/marketplace/transactions/list-transactions/list-transactions.component';
import { ViewTransactionComponent } from './features/marketplace/transactions/view-transaction/view-transaction.component';
import { ProduceComponent } from './features/marketplace/produce/produce.component';
import { ListProduceComponent } from './features/marketplace/produce/list-produce/list-produce.component';
import { ViewProduceComponent } from './features/marketplace/produce/view-produce/view-produce.component';
import { ListAnnouncementsComponent } from './features/announcements/list-announcements/list-announcements.component';
import { AddAnnouncementsComponent } from './features/announcements/add-announcements/add-announcements.component';
import { EditAnnouncementComponent } from './features/announcements/edit-announcement/edit-announcement.component';
import { ViewAnnouncementComponent } from './features/announcements/view-announcement/view-announcement.component';

export const routes: Routes = [
  {
    path: 'login',
    component: LoginComponent,
    canActivate: [loginGuard],
  },
  {
    path: '',
    component: MainLayoutComponent,
    canActivate: [authGuard],
    children: [
      {
        path: 'dashboard',
        component: DashboardComponent,
      },
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full',
      },
      {
        path: 'masters/livestock-types',
        component: LivestockTypesComponent,
        children: [
          {
            path: '',
            component: ListLivestocktypesComponent,
          },
          {
            path: 'add',
            component: AddLivestocktypeComponent,
          },
          {
            path: 'edit/:id',
            component: EditLivestocktypeComponent,
          },
        ],
      },
      {
        path: 'masters/states',
        children: [
          {
            path: '',
            component: ListStatesComponent,
          },
          {
            path: 'add',
            component: AddStateComponent,
          },
          {
            path: 'edit/:id',
            component: EditStateComponent,
          },
        ],
      },
      {
        path: 'masters/cities',
        component: CitiesComponent,
        children: [
          {
            path: '',
            component: ListCitiesComponent,
          },
          {
            path: 'add',
            component: AddCityComponent,
          },
          {
            path: 'edit/:id',
            component: EditCityComponent,
          },
        ],
      },

      // Add this to your existing routes under the masters path

      {
        path: 'masters/farming-types',
        component: FarmingTypesComponent,
        children: [
          {
            path: '',
            component: ListFarmingTypesComponent,
          },
          {
            path: 'add',
            component: AddFarmingTypeComponent,
          },
          {
            path: 'edit/:id',
            component: EditFarmingTypeComponent,
          },
        ],
      },

      {
        path: 'announcements',
        children: [
          {
            path: '',
            component: ListAnnouncementsComponent,
          },
          {
            path: 'add',
            component: AddAnnouncementsComponent,
          },
          {
            path: 'edit/:id',
            component: EditAnnouncementComponent,
          },
          {
            path: 'view/:id',
            component: ViewAnnouncementComponent,
          },
        ],
      },

      {
        path: 'masters/crops',
        component: CropsComponent,
        children: [
          {
            path: '',
            component: ListCropsComponent,
          },
          {
            path: 'add',
            component: AddCropComponent,
          },
          {
            path: 'edit/:id',
            component: EditCropComponent,
          },
        ],
      },

      {
        path: 'masters/equipment-types',
        component: EquipmentTypesComponent,
        children: [
          {
            path: '',
            component: ListEquipmentTypesComponent,
          },
          {
            path: 'add',
            component: AddEquipmentTypeComponent,
          },
          {
            path: 'edit/:id',
            component: EditEquipmentTypeComponent,
          },
        ],
      },

      {
        path: 'masters/irrigation-types',
        component: IrrigationTypesComponent,
        children: [
          {
            path: '',
            component: ListIrrigationTypesComponent,
          },
          {
            path: 'add',
            component: AddIrrigationTypeComponent,
          },
          {
            path: 'edit/:id',
            component: EditIrrigationTypeComponent,
          },
        ],
      },

      {
        path: 'farmers',
        component: FarmersComponent,
        children: [
          {
            path: '',
            component: ListFarmersComponent,
          },
          {
            path: 'add',
            component: AddFarmerComponent,
          },
          {
            path: 'edit/:id',
            component: EditFarmerComponent,
          },
          {
            path: 'view/:id',
            component: FarmerViewComponent,
          },
        ],
      },

      {
        path: 'knowledge-hub',
        component: ArticlesComponent,
        children: [
          {
            path: '',
            component: ListArticleComponent,
          },
          {
            path: 'add-article',
            component: AddArticleComponent,
          },
          {
            path: 'edit-article/:id',
            component: EditArticleComponent,
          },
          {
            path: 'view-article/:id', // Make sure this matches exactly
            component: ViewArticleComponent,
          },
        ],
      },

      // In app.routes.ts, add these routes to the existing routes array
      {
        path: 'queries',
        component: QueriesComponent,
        children: [
          {
            path: '',
            component: ListQueriesComponent,
          },
          {
            path: 'view/:id',
            component: ViewQueryComponent,
          },
        ],
      },

      {
        path: 'marketplace',
        component: MarketplaceComponent,
        children: [
          {
            path: 'market-prices',
            component: MarketPricesComponent,
            children: [
              {
                path: '',
                component: ListMarketPricesComponent,
              },
              {
                path: 'add-market-price',
                component: AddMarketPriceComponent,
              },
              {
                path: 'edit-market-price/:id',
                component: EditMarketPriceComponent,
              },
            ],
          },
          // Add the requirements routes here inside the marketplace children array
          {
            path: 'requirements', // Give it a proper path
            children: [
              {
                path: '',
                component: ListRequirementsComponent,
              },
              {
                path: 'add',
                component: AddRequirementComponent,
              },
              {
                path: 'edit-requirement/:id',
                component: EditRequirementComponent,
              },
              {
                path: 'view-requirement/:id',
                component: ViewRequirementComponent,
              },
            ],
          },

          {
            path: 'produce',
            component: ProduceComponent,
            children: [
              {
                path: '',
                component: ListProduceComponent,
              },
              {
                path: 'view/:id',
                component: ViewProduceComponent,
              },
              {
                path: 'add',
                component: ProduceComponent,
              },
              {
                path: 'edit/:id',
                component: ProduceComponent,
              },
            ],
          },
          {
            path: 'interests', // Give it a proper path
            children: [
              {
                path: '',
                component: RequirementInterestsComponent,
              },
            ],
          },
          {
            path: 'transactions',
            component: TransactionsComponent,
            children: [
              {
                path: '',
                component: ListTransactionsComponent,
              },
              {
                path: 'view/:id',
                component: ViewTransactionComponent,
              },
              {
                path: 'requirements-interests',
                component: RequirementInterestsComponent,
              },
              // Add more transaction routes here as needed
            ],
          },

          {
            path: '',
            redirectTo: 'market-prices',
            pathMatch: 'full',
          },
        ],
      },
    ],
  },
];
